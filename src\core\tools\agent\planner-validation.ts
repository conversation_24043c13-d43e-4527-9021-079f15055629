/**
 * Execution Planner Validation - Dependency Validation and Preemptive Pipeline
 * Split from executionPlanner.ts for constraint compliance (≤150 lines)
 *
 * @notation P:core/tools/agent/planner-validation F:validateDependencies,preemptiveValidationPipeline CB:plannerValidation I:ValidationResult DB:execution
 */

import { ExecutionTask } from '../../types'
import { TaskDependencyGraph, buildDependencyGraph } from './planner-core'
import * as fs from 'fs'
import * as path from 'path'
import * as ts from 'typescript'

export type ValidationResult = {
  readonly valid: boolean
  readonly errors: readonly string[]
  readonly warnings: readonly string[]
  readonly circularDependencies: readonly string[][]
  readonly unreachableTasks: readonly string[]
}

export type PreemptiveValidationResult = {
  readonly success: boolean
  readonly moduleExists: boolean
  readonly hasValidExports: boolean
  readonly canExecute: boolean
  readonly errors: readonly string[]
  readonly warnings: readonly string[]
  readonly timestamp: number
}

/**
 * F:validateDependencies - Validate task dependencies for cycles and reachability
 * @notation P:tasks F:validateDependencies CB:dependencyValidation I:ValidationResult DB:execution
 */
export const validateDependencies = (
  tasks: readonly ExecutionTask[]
): ValidationResult => {
  const errors: string[] = []
  const warnings: string[] = []
  const circularDependencies: string[][] = []
  const unreachableTasks: string[] = []

  const graph = buildDependencyGraph(tasks)
  const taskIds = new Set(tasks.map(t => t.id))

  // Check for missing dependencies
  for (const task of tasks) {
    for (const dep of task.dependencies) {
      if (!taskIds.has(dep)) {
        errors.push(`Task ${task.id} depends on non-existent task: ${dep}`)
      }
    }
  }

  // Detect circular dependencies using DFS
  const visited = new Set<string>()
  const recursionStack = new Set<string>()
  const currentPath: string[] = []

  const detectCycles = (taskId: string): boolean => {
    if (recursionStack.has(taskId)) {
      const cycleStart = currentPath.indexOf(taskId)
      const cycle = currentPath.slice(cycleStart).concat(taskId)
      circularDependencies.push(cycle)
      return true
    }

    if (visited.has(taskId)) {
      return false
    }

    visited.add(taskId)
    recursionStack.add(taskId)
    currentPath.push(taskId)

    const dependencies = graph.edges.get(taskId) || []
    for (const dep of dependencies) {
      if (detectCycles(dep)) {
        // Continue to find all cycles
      }
    }

    recursionStack.delete(taskId)
    currentPath.pop()
    return false
  }

  // Check each task for cycles
  for (const taskId of taskIds) {
    if (!visited.has(taskId)) {
      detectCycles(taskId)
    }
  }

  // Find unreachable tasks (tasks that have no path from root nodes)
  const reachable = new Set<string>()
  
  const markReachable = (taskId: string) => {
    if (reachable.has(taskId)) return
    reachable.add(taskId)
    
    const dependencies = graph.edges.get(taskId) || []
    for (const dep of dependencies) {
      markReachable(dep)
    }
  }

  // Start from root nodes
  for (const rootId of graph.roots) {
    markReachable(rootId)
  }

  // Find unreachable tasks
  for (const taskId of taskIds) {
    if (!reachable.has(taskId) && !graph.roots.includes(taskId)) {
      unreachableTasks.push(taskId)
    }
  }

  // Add warnings for potential issues
  if (graph.roots.length === 0 && tasks.length > 0) {
    warnings.push('No root tasks found - all tasks have dependencies')
  }

  if (graph.leaves.length === 0 && tasks.length > 0) {
    warnings.push('No leaf tasks found - potential circular dependency')
  }

  const valid = errors.length === 0 && circularDependencies.length === 0

  return Object.freeze({
    valid,
    errors: Object.freeze(errors),
    warnings: Object.freeze(warnings),
    circularDependencies: Object.freeze(circularDependencies.map(cycle => Object.freeze(cycle))),
    unreachableTasks: Object.freeze(unreachableTasks)
  })
}

/**
 * F:preemptiveValidationPipeline - Validate module and handler execution capability
 * @notation P:targetModule,proposedChanges,workspaceRoot F:preemptiveValidationPipeline CB:preemptiveValidation I:PreemptiveValidationResult DB:execution
 */
export const preemptiveValidationPipeline = async (
  targetModule: string,
  proposedChanges: readonly string[],
  workspaceRoot: string = process.cwd()
): Promise<PreemptiveValidationResult> => {
  const errors: string[] = []
  const warnings: string[] = []
  let moduleExists = false
  let hasValidExports = false
  let canExecute = false

  try {
    // Phase 1: Module existence validation
    const modulePath = path.resolve(workspaceRoot, targetModule)
    moduleExists = fs.existsSync(modulePath)
    
    if (!moduleExists) {
      errors.push(`Target module does not exist: ${modulePath}`)
      return createValidationResult(false, moduleExists, hasValidExports, canExecute, errors, warnings)
    }

    // Phase 2: Export validation using TypeScript compiler
    try {
      const sourceFile = ts.createSourceFile(
        modulePath,
        fs.readFileSync(modulePath, 'utf-8'),
        ts.ScriptTarget.Latest,
        true
      )

      const exports: string[] = []
      
      const visit = (node: ts.Node) => {
        if (ts.isExportDeclaration(node) || ts.isExportAssignment(node)) {
          if (ts.isExportDeclaration(node) && node.exportClause) {
            if (ts.isNamedExports(node.exportClause)) {
              for (const element of node.exportClause.elements) {
                exports.push(element.name.text)
              }
            }
          }
        } else if (ts.isFunctionDeclaration(node) || ts.isVariableStatement(node)) {
          if (node.modifiers?.some(mod => mod.kind === ts.SyntaxKind.ExportKeyword)) {
            if (ts.isFunctionDeclaration(node) && node.name) {
              exports.push(node.name.text)
            } else if (ts.isVariableStatement(node)) {
              for (const declaration of node.declarationList.declarations) {
                if (ts.isIdentifier(declaration.name)) {
                  exports.push(declaration.name.text)
                }
              }
            }
          }
        }
        
        ts.forEachChild(node, visit)
      }

      visit(sourceFile)
      hasValidExports = exports.length > 0

      if (!hasValidExports) {
        warnings.push(`Module ${targetModule} has no exports`)
      }

    } catch (parseError) {
      errors.push(`Failed to parse module ${targetModule}: ${(parseError as Error).message}`)
      return createValidationResult(false, moduleExists, hasValidExports, canExecute, errors, warnings)
    }

    // Phase 3: Runtime execution validation
    try {
      const moduleExports = await import(modulePath)
      canExecute = typeof moduleExports === 'object' && moduleExports !== null
      
      if (!canExecute) {
        errors.push(`Module ${targetModule} cannot be imported at runtime`)
      }

    } catch (importError) {
      errors.push(`Runtime import failed for ${targetModule}: ${(importError as Error).message}`)
      canExecute = false
    }

    const success = moduleExists && hasValidExports && canExecute && errors.length === 0

    return createValidationResult(success, moduleExists, hasValidExports, canExecute, errors, warnings)

  } catch (error) {
    errors.push(`Validation pipeline failed: ${(error as Error).message}`)
    return createValidationResult(false, moduleExists, hasValidExports, canExecute, errors, warnings)
  }
}

/**
 * F:createValidationResult - Create preemptive validation result
 * @notation P:success,moduleExists,hasValidExports,canExecute,errors,warnings F:createValidationResult CB:validationResult I:PreemptiveValidationResult DB:execution
 */
const createValidationResult = (
  success: boolean,
  moduleExists: boolean,
  hasValidExports: boolean,
  canExecute: boolean,
  errors: string[],
  warnings: string[]
): PreemptiveValidationResult => {
  return Object.freeze({
    success,
    moduleExists,
    hasValidExports,
    canExecute,
    errors: Object.freeze(errors),
    warnings: Object.freeze(warnings),
    timestamp: Date.now()
  })
}
