{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "skipLibCheck": true, "downlevelIteration": true, "allowSyntheticDefaultImports": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests", "**/*.test.ts", "**/*.spec.ts"]}