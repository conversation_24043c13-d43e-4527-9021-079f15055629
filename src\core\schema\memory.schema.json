{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Memory Operations Schema - Unified Architecture", "description": "Validation schema for memory operations using unified database schema", "type": "object", "required": ["action"], "properties": {"action": {"type": "string", "enum": ["insert", "query", "update", "delete", "template"]}, "table": {"type": "string", "enum": ["system_config", "handlers", "handler_tests", "handler_metrics", "refactor_manifest", "architecture_compliance", "agent_sessions", "command_executions", "circuit_breaker_events", "error_recovery_events", "performance_insights", "system_traits", "audit_trail", "traits", "sessions", "failures", "reflections", "tools", "mcp_calls"]}, "data": {"type": "object", "description": "Data to insert or update - must align with unified schema table structure"}, "where": {"type": "object", "description": "Query conditions using unified schema column names"}, "limit": {"type": "number", "minimum": 1, "maximum": 10000, "default": 100}, "offset": {"type": "number", "minimum": 0, "default": 0}, "orderBy": {"type": "string", "description": "Column to order by - must be valid unified schema column"}, "orderDirection": {"type": "string", "enum": ["ASC", "DESC"], "default": "DESC"}, "joins": {"type": "array", "description": "Cross-referential joins using unified schema foreign keys", "items": {"type": "object", "properties": {"table": {"type": "string"}, "on": {"type": "string"}, "type": {"type": "string", "enum": ["INNER", "LEFT", "RIGHT"], "default": "LEFT"}}, "required": ["table", "on"]}}, "aggregations": {"type": "object", "description": "Aggregation functions for analytics queries", "properties": {"groupBy": {"type": "array", "items": {"type": "string"}}, "having": {"type": "object"}, "functions": {"type": "array", "items": {"type": "object", "properties": {"function": {"type": "string", "enum": ["COUNT", "AVG", "SUM", "MIN", "MAX"]}, "column": {"type": "string"}, "alias": {"type": "string"}}, "required": ["function", "column"]}}}}, "content": {"type": "string", "description": "Template content for template action"}, "templateVars": {"type": "object", "description": "Template variables for substitution"}, "templateOutputPath": {"type": "string", "description": "Output path for processed template"}}}