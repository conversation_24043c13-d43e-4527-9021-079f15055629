/**
 * Batch Analyzer Tool - AI-Optimized Analysis Engine
 * @notation P:core/tools/batchAnalyzer F:analyzeBatch,analyzeFile,extractMethods,extractCircuitBreakers,extractInterfaces,extractImports,extractExports CB:none I:BatchAnalysisOptions,FileAnalysisResult,BatchAnalysisResult DB:none
 */

import * as fs from 'fs'
import * as path from 'path'
import { glob } from 'glob'
import {
  BatchAnalysisOptions,
  FileAnalysisResult,
  BatchAnalysisResult,
  MethodInfo,
  CircuitBreakerInfo,
  InterfaceInfo,
  PropertyInfo,
  ImportInfo,
  ExportInfo
} from '../types'
import {
  METHOD_PATTERNS,
  CIRCUIT_BREAKER_PATTERNS,
  EXPORT_PATTERNS,
  INTERFACE_PATTERN,
  IMPORT_PATTERN,
  PARAMETER_PATTERN,
  PROPERTY_PATTERN,
  EXCLUDED_PATTERNS,
  KNOWN_HANDLERS
} from '../constants'

/**
 * F:analyzeBatch - Analyze multiple files matching a pattern
 * @notation P:options,workspaceRoot F:analyzeBatch CB:none I:BatchAnalysisOptions,BatchAnalysisResult DB:none
 */
export const analyzeBatch = async (
  options: BatchAnalysisOptions,
  workspaceRoot: string = process.cwd()
): Promise<BatchAnalysisResult> => {
  const startTime = Date.now()

  const files = await new Promise<string[]>((resolve, reject) => {
    glob(
      options.pattern,
      {
        cwd: workspaceRoot,
        absolute: true,
        ignore: EXCLUDED_PATTERNS.map((pattern: string) => `**/${pattern}/**`)
      },
      (err, matches) => {
        if (err) reject(err)
        else resolve(matches)
      }
    )
  })

  const fileResults = await Promise.all(
    files.map(async (filePath: string) => {
      try {
        return await analyzeFile(filePath, options, workspaceRoot)
      } catch (error) {
        return Object.freeze({
          filePath,
          relativePath: path.relative(workspaceRoot, filePath),
          size: 0,
          lines: 0,
          methods: Object.freeze([]),
          circuitBreakers: Object.freeze([]),
          interfaces: Object.freeze([]),
          imports: Object.freeze([]),
          exports: Object.freeze([]),
          errors: Object.freeze([(error as Error).message])
        })
      }
    })
  )

  const totalMethods = fileResults.reduce(
    (sum: number, file: FileAnalysisResult) => sum + file.methods.length,
    0
  )
  const totalCircuitBreakers = fileResults.reduce(
    (sum: number, file: FileAnalysisResult) => sum + file.circuitBreakers.length,
    0
  )
  const totalInterfaces = fileResults.reduce(
    (sum: number, file: FileAnalysisResult) => sum + file.interfaces.length,
    0
  )
  const totalLines = fileResults.reduce(
    (sum: number, file: FileAnalysisResult) => sum + file.lines,
    0
  )

  return Object.freeze({
    pattern: options.pattern,
    totalFiles: fileResults.length,
    totalLines,
    totalMethods,
    totalCircuitBreakers,
    totalInterfaces,
    files: Object.freeze(fileResults),
    executionTime: Date.now() - startTime
  })
}

/**
 * F:analyzeFile - Analyze a single file for patterns and symbols
 * @notation P:filePath,options,workspaceRoot F:analyzeFile CB:none I:FileAnalysisResult DB:none
 */
export const analyzeFile = async (
  filePath: string,
  options: BatchAnalysisOptions,
  workspaceRoot: string
): Promise<FileAnalysisResult> => {
  const content = await fs.promises.readFile(filePath, 'utf-8')
  const lines = content.split('\n')

  const allMethods = extractMethods(lines)
  const methods = options.methods?.length
    ? allMethods.filter(m => options.methods!.some(pattern => m.name.includes(pattern)))
    : allMethods

  const allCircuitBreakers = extractCircuitBreakers(content, lines)
  const circuitBreakers = options.circuitBreakers?.length
    ? allCircuitBreakers.filter(cb =>
        options.circuitBreakers!.some(pattern => cb.name.includes(pattern))
      )
    : allCircuitBreakers

  const allInterfaces = extractInterfaces(lines)
  const interfaces = options.interfaces?.length
    ? allInterfaces.filter(i => options.interfaces!.some(pattern => i.name.includes(pattern)))
    : allInterfaces

  const imports = options.includeImports ? extractImports(lines) : []
  const exports = options.includeExports ? extractExports(lines) : []

  return Object.freeze({
    filePath,
    relativePath: path.relative(workspaceRoot, filePath),
    size: content.length,
    lines: lines.length,
    methods: Object.freeze(methods),
    circuitBreakers: Object.freeze(circuitBreakers),
    interfaces: Object.freeze(interfaces),
    imports: Object.freeze(imports),
    exports: Object.freeze(exports),
    errors: Object.freeze([])
  })
}

/**
 * F:extractMethods - Extract method information from file lines
 * @notation P:lines F:extractMethods CB:none I:MethodInfo DB:none
 */
export const extractMethods = (lines: readonly string[]): readonly MethodInfo[] => {
  const methods: MethodInfo[] = []

  lines.forEach((line, index) => {
    METHOD_PATTERNS.forEach(pattern => {
      const match = line.match(pattern)
      if (match) {
        const [, visibility, asyncKeyword, name, returnType] = match
        const paramMatch = line.match(PARAMETER_PATTERN)
        const parameters = paramMatch
          ? paramMatch[1]
              .split(',')
              .map(p => p.trim())
              .filter(Boolean)
          : []

        methods.push(
          Object.freeze({
            name: name || 'anonymous',
            lineStart: index + 1,
            lineEnd: index + 1, // Simplified - would need AST for accurate end
            parameters: Object.freeze(parameters),
            returnType: returnType?.trim() || 'unknown',
            isAsync: Boolean(asyncKeyword),
            visibility: (visibility as 'public' | 'private' | 'protected') || 'public'
          })
        )
      }
    })
  })

  return Object.freeze(methods)
}

/**
 * F:extractCircuitBreakers - Extract circuit breaker patterns from content
 * @notation P:content,lines F:extractCircuitBreakers CB:none I:CircuitBreakerInfo DB:none
 */
export const extractCircuitBreakers = (
  content: string,
  lines: readonly string[]
): readonly CircuitBreakerInfo[] => {
  const circuitBreakers: CircuitBreakerInfo[] = []

  CIRCUIT_BREAKER_PATTERNS.forEach(pattern => {
    let match
    while ((match = pattern.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split('\n').length
      const handler = KNOWN_HANDLERS.find(h => content.includes(h)) || 'unknown'

      circuitBreakers.push(
        Object.freeze({
          name: match[1] || 'unnamed',
          lineNumber,
          pattern: pattern.source,
          handler
        })
      )
    }
  })

  return Object.freeze(circuitBreakers)
}

/**
 * F:extractInterfaces - Extract interface definitions from lines
 * @notation P:lines F:extractInterfaces CB:none I:InterfaceInfo DB:none
 */
export const extractInterfaces = (lines: readonly string[]): readonly InterfaceInfo[] => {
  const interfaces: InterfaceInfo[] = []
  let currentInterface: InterfaceInfo | null = null
  let braceCount = 0

  lines.forEach((line, index) => {
    const interfaceMatch = line.match(INTERFACE_PATTERN)
    if (interfaceMatch) {
      const [, name, extendsClause] = interfaceMatch
      const extendsArray = extendsClause ? extendsClause.split(',').map(e => e.trim()) : []

      currentInterface = {
        name,
        lineStart: index + 1,
        lineEnd: index + 1,
        properties: [],
        extends: Object.freeze(extendsArray)
      }
      braceCount = 1
    } else if (currentInterface && braceCount > 0) {
      const openBraces = (line.match(/\{/g) || []).length
      const closeBraces = (line.match(/\}/g) || []).length
      braceCount += openBraces - closeBraces

      const propertyMatch = line.match(PROPERTY_PATTERN)
      if (propertyMatch) {
        const [, name, optional, type] = propertyMatch
        const properties = [
          ...currentInterface.properties,
          Object.freeze({
            name,
            type: type.trim(),
            optional: Boolean(optional),
            lineNumber: index + 1
          })
        ]
        currentInterface = { ...currentInterface, properties: Object.freeze(properties) }
      }

      if (braceCount === 0) {
        interfaces.push(
          Object.freeze({
            ...currentInterface,
            lineEnd: index + 1
          })
        )
        currentInterface = null
      }
    }
  })

  return Object.freeze(interfaces)
}

/**
 * F:extractImports - Extract import statements from lines
 * @notation P:lines F:extractImports CB:none I:ImportInfo DB:none
 */
export const extractImports = (lines: readonly string[]): readonly ImportInfo[] => {
  const imports: ImportInfo[] = []

  lines.forEach((line, index) => {
    const importMatch = line.match(IMPORT_PATTERN)
    if (importMatch) {
      const [, namedImports, defaultImport, module] = importMatch
      const isTypeOnly = line.includes('import type')
      const importsList = namedImports
        ? namedImports.split(',').map(i => i.trim())
        : defaultImport
          ? [defaultImport]
          : []

      imports.push(
        Object.freeze({
          module,
          imports: Object.freeze(importsList),
          lineNumber: index + 1,
          isTypeOnly
        })
      )
    }
  })

  return Object.freeze(imports)
}

/**
 * F:extractExports - Extract export statements from lines
 * @notation P:lines F:extractExports CB:none I:ExportInfo DB:none
 */
export const extractExports = (lines: readonly string[]): readonly ExportInfo[] => {
  const exports: ExportInfo[] = []

  lines.forEach((line, index) => {
    EXPORT_PATTERNS.forEach(({ pattern, type }) => {
      const match = line.match(pattern)
      if (match) {
        exports.push(
          Object.freeze({
            name: match[1] || 'default',
            type,
            lineNumber: index + 1
          })
        )
      }
    })
  })

  return Object.freeze(exports)
}
