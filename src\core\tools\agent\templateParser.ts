/**
 * Template Parser - AI-Optimized Pure Functions
 * @notation P:core/tools/agent/templateParser F:parsePromptTemplate,validatePromptAnalysis CB:parsePromptTemplate I:PromptAnalysis,TemplateParseResult DB:templates
 */

import * as fs from 'fs'
import * as path from 'path'

export type PromptAnalysis = {
  readonly goal: string
  readonly traits: readonly string[]
  readonly tools: readonly string[]
}

export type TemplateParseResult = {
  readonly success: boolean
  readonly analysis: PromptAnalysis | null
  readonly error?: string
  readonly filePath: string
  readonly timestamp: number
  readonly processingTime: number
}

export type TemplateValidationResult = {
  readonly isValid: boolean
  readonly errors: readonly string[]
  readonly warnings: readonly string[]
  readonly score: number
}

export type TemplateParseOptions = {
  readonly encoding: BufferEncoding
  readonly validateResult: boolean
  readonly requireGoal: boolean
  readonly requireTraits: boolean
  readonly requireTools: boolean
}

const DEFAULT_PARSE_OPTIONS: TemplateParseOptions = Object.freeze({
  encoding: 'utf-8',
  validateResult: true,
  requireGoal: true,
  requireTraits: false,
  requireTools: false
})

const TEMPLATE_PATTERNS = Object.freeze({
  goal: /Goal:\s*(.+)/i,
  traits: /Required Traits:\s*(.+)/i,
  tools: /Required Tools:\s*(.+)/i,
  multilineGoal: /Goal:\s*\n((?:.|\n)*?)(?=\n\w+:|$)/i,
  multilineTraits: /Required Traits:\s*\n((?:.|\n)*?)(?=\n\w+:|$)/i,
  multilineTools: /Required Tools:\s*\n((?:.|\n)*?)(?=\n\w+:|$)/i
})

/**
 * F:validatePromptAnalysis - Validate prompt analysis structure
 * @notation P:analysis F:validatePromptAnalysis CB:none I:boolean DB:none
 */
export const validatePromptAnalysis = (analysis: unknown): analysis is PromptAnalysis => {
  if (!analysis || typeof analysis !== 'object') return false

  const a = analysis as Record<string, unknown>

  return (
    typeof a.goal === 'string' &&
    Array.isArray(a.traits) &&
    a.traits.every(t => typeof t === 'string') &&
    Array.isArray(a.tools) &&
    a.tools.every(t => typeof t === 'string')
  )
}

/**
 * F:validateTemplateContent - Validate template content quality
 * @notation P:analysis,options F:validateTemplateContent CB:none I:TemplateValidationResult DB:none
 */
export const validateTemplateContent = (
  analysis: PromptAnalysis,
  options: TemplateParseOptions = DEFAULT_PARSE_OPTIONS
): TemplateValidationResult => {
  const errors: string[] = []
  const warnings: string[] = []
  let score = 100

  if (options.requireGoal && !analysis.goal.trim()) {
    errors.push('Goal is required but missing or empty')
    score -= 30
  }

  if (options.requireTraits && analysis.traits.length === 0) {
    errors.push('Traits are required but missing')
    score -= 20
  }

  if (options.requireTools && analysis.tools.length === 0) {
    errors.push('Tools are required but missing')
    score -= 20
  }

  if (analysis.goal.length < 10) {
    warnings.push('Goal is very short, consider adding more detail')
    score -= 5
  }

  if (analysis.goal.length > 500) {
    warnings.push('Goal is very long, consider making it more concise')
    score -= 5
  }

  if (analysis.traits.length > 10) {
    warnings.push('Many traits specified, consider focusing on the most important ones')
    score -= 5
  }

  if (analysis.tools.length > 15) {
    warnings.push('Many tools specified, consider focusing on essential tools')
    score -= 5
  }

  const emptyTraits = analysis.traits.filter(t => !t.trim())
  if (emptyTraits.length > 0) {
    warnings.push(`${emptyTraits.length} empty trait(s) found`)
    score -= emptyTraits.length * 2
  }

  const emptyTools = analysis.tools.filter(t => !t.trim())
  if (emptyTools.length > 0) {
    warnings.push(`${emptyTools.length} empty tool(s) found`)
    score -= emptyTools.length * 2
  }

  return Object.freeze({
    isValid: errors.length === 0,
    errors: Object.freeze(errors),
    warnings: Object.freeze(warnings),
    score: Math.max(0, score)
  })
}

/**
 * F:extractGoal - Extract goal from template content
 * @notation P:content F:extractGoal CB:none I:string DB:none
 */
export const extractGoal = (content: string): string => {
  const singleLineMatch = content.match(TEMPLATE_PATTERNS.goal)
  if (singleLineMatch?.[1]) {
    return singleLineMatch[1].trim()
  }

  const multiLineMatch = content.match(TEMPLATE_PATTERNS.multilineGoal)
  if (multiLineMatch?.[1]) {
    return multiLineMatch[1].trim().replace(/\n\s*/g, ' ')
  }

  return ''
}

/**
 * F:extractTraits - Extract traits from template content
 * @notation P:content F:extractTraits CB:none I:array DB:none
 */
export const extractTraits = (content: string): readonly string[] => {
  const singleLineMatch = content.match(TEMPLATE_PATTERNS.traits)
  if (singleLineMatch?.[1]) {
    return Object.freeze(
      singleLineMatch[1]
        .split(',')
        .map(t => t.trim())
        .filter(t => t.length > 0)
    )
  }

  const multiLineMatch = content.match(TEMPLATE_PATTERNS.multilineTraits)
  if (multiLineMatch?.[1]) {
    return Object.freeze(
      multiLineMatch[1]
        .split(/[,\n]/)
        .map(t => t.trim())
        .filter(t => t.length > 0)
    )
  }

  return Object.freeze([])
}

/**
 * F:extractTools - Extract tools from template content
 * @notation P:content F:extractTools CB:none I:array DB:none
 */
export const extractTools = (content: string): readonly string[] => {
  const singleLineMatch = content.match(TEMPLATE_PATTERNS.tools)
  if (singleLineMatch?.[1]) {
    return Object.freeze(
      singleLineMatch[1]
        .split(',')
        .map(t => t.trim())
        .filter(t => t.length > 0)
    )
  }

  const multiLineMatch = content.match(TEMPLATE_PATTERNS.multilineTools)
  if (multiLineMatch?.[1]) {
    return Object.freeze(
      multiLineMatch[1]
        .split(/[,\n]/)
        .map(t => t.trim())
        .filter(t => t.length > 0)
    )
  }

  return Object.freeze([])
}

/**
 * F:parseTemplateContent - Parse template content into analysis
 * @notation P:content,options F:parseTemplateContent CB:none I:PromptAnalysis DB:none
 */
export const parseTemplateContent = (
  content: string,
  options: TemplateParseOptions = DEFAULT_PARSE_OPTIONS
): PromptAnalysis => {
  const goal = extractGoal(content)
  const traits = extractTraits(content)
  const tools = extractTools(content)

  return Object.freeze({
    goal,
    traits,
    tools
  })
}

/**
 * F:parsePromptTemplate - Parse prompt template file with comprehensive error handling
 * @notation P:filepath,options F:parsePromptTemplate CB:parsePromptTemplate I:TemplateParseResult DB:templates
 */
export const parsePromptTemplate = (
  filepath: string,
  options: TemplateParseOptions = DEFAULT_PARSE_OPTIONS
): TemplateParseResult => {
  const startTime = Date.now()

  try {
    if (!filepath || typeof filepath !== 'string') {
      throw new Error('Invalid file path provided')
    }

    if (!fs.existsSync(filepath)) {
      throw new Error(`Template file not found: ${filepath}`)
    }

    const stats = fs.statSync(filepath)
    if (!stats.isFile()) {
      throw new Error(`Path is not a file: ${filepath}`)
    }

    const content = fs.readFileSync(filepath, options.encoding)

    const analysis = parseTemplateContent(content, options)

    if (options.validateResult) {
      const validation = validateTemplateContent(analysis, options)
      if (!validation.isValid) {
        throw new Error(`Template validation failed: ${validation.errors.join(', ')}`)
      }
    }

    return Object.freeze({
      success: true,
      analysis,
      filePath: path.resolve(filepath),
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      analysis: null,
      error: (error as Error).message,
      filePath: filepath,
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  }
}

/**
 * F:createEmptyPromptAnalysis - Create empty prompt analysis
 * @notation P:none F:createEmptyPromptAnalysis CB:none I:PromptAnalysis DB:none
 */
export const createEmptyPromptAnalysis = (): PromptAnalysis => {
  return Object.freeze({
    goal: '',
    traits: Object.freeze([]),
    tools: Object.freeze([])
  })
}

/**
 * F:mergePromptAnalyses - Merge multiple prompt analyses
 * @notation P:analyses F:mergePromptAnalyses CB:none I:PromptAnalysis DB:none
 */
export const mergePromptAnalyses = (analyses: readonly PromptAnalysis[]): PromptAnalysis => {
  const goals = analyses.map(a => a.goal).filter(g => g.trim().length > 0)
  const allTraits = analyses.flatMap(a => a.traits)
  const allTools = analyses.flatMap(a => a.tools)

  return Object.freeze({
    goal: goals.join(' | '),
    traits: Object.freeze(Array.from(new Set(allTraits))),
    tools: Object.freeze(Array.from(new Set(allTools)))
  })
}

/**
 * F:parsePromptTemplateLegacy - Legacy compatibility wrapper
 * @notation P:filepath F:parsePromptTemplateLegacy CB:parsePromptTemplateLegacy I:PromptAnalysis DB:templates
 */
export const parsePromptTemplateLegacy = (filepath: string): PromptAnalysis => {
  const result = parsePromptTemplate(filepath)
  if (!result.success) {
    throw new Error(result.error || 'Template parsing failed')
  }
  return result.analysis!
}

export { parsePromptTemplateLegacy as default }
