# AI-Optimized Task Notation System

## Augster MCP Framework Standard

### CORE ACTION SYMBOLS

| Symbol | Action | Description |
|--------|--------|-------------|
| 🔎 | Extract/Analyze | Read, analyze, document code/data |
| ⚡ | Implement | Add, create, build new functionality |
| 🧪 | Test | Execute tests, validate functionality |
| ✅ | Validate | Verify, confirm, check correctness |
| 📝 | Document | Update docs, create summaries |
| 🔧 | Configure | Modify settings, update schemas |
| 🚀 | Deploy | Start servers, build production |
| 🛑 | Stop | Stop servers, halt processes |
| 📊 | Measure | Performance testing, metrics collection |
| 🔄 | Coordinate | Multi-handler workflows |
| 💾 | Compile | Build, save, persist data |
| ⚠️ | Awareness | intent, progress, task, sub-task |
| ♾️ | Refactor, improve, optimize | reflect, verify, update documentation |

### PARAMETER NOTATION

| Prefix | Parameter Type | Example |
|--------|----------------|---------|
| P: | File Path | P:mem.ts, P:BaseHandler.ts |
| L: | Line Range | L:1-200, L:201-400 |
| C: | Class Name | C:BaseHandler, C:MemoryHandler |
| F: | Function Name | F:executeOperation, F:getHealthStatus |
| V: | Property Name | V:healthStatus, V:performanceMetrics |
| L: | Level Type | L:class, L:function, L:property |
| M: | Method Name | M:executeOperation, M:getHealthStatus |
| CB: | Circuit Breaker | CB:memory-insert, CB:file-read |
| I: | Interface | I:HealthStatus, I:PerformanceMetrics |
| S: | Server/Port | S:8081, S:8082 |
| DB: | Database | DB:operational, DB:240+records |
| H: | Handler | H:memory, H:file, H:github |
| A: | Action | A:handlers, A:performance, A:coordinate |
| T: | Target Time | T:<100ms, T:1-4ms, T:227-318ms |

### AUGMENT RICH NOTATION

| Prefix | Parameter Type | Example |
|--------|----------------|---------|
| R: | File Reference | R:src/mcp/handlers/*.ts, M:insert, L:1-200 |

### ENHANCED TOOL NOTATION

| Prefix | Tool Type | Example | Purpose |
|--------|-----------|---------|---------|
| BA: | Batch Analyzer | BA:src/mcp/handlers/*.ts | Pattern-based file analysis |
| TS: | TypeScript Tool | TS:validate P:BaseHandler.ts | Real-time TS validation |
| DB: | Database Migration | DB:migrate schema.ts→performance_baselines | Schema change safety |

### HANDLER ABBREVIATIONS

| Handler | Abbrev | Operations Count |
|---------|--------|------------------|
| memory | mem | 3 ops |
| file | fil | 8 ops |
| database | db | 6 ops |
| github | gh | 15 ops |
| monitoring | mon | 4 ops |
| coordination | coord | 11 ops |
| fetch | fet | 3 ops |
| time | tim | 7 ops |
| git | git | 11 ops |
| terminal | term | 5 ops |

### CIRCUIT BREAKER NOTATION

| Pattern | Meaning |
|---------|---------|
| CB:mem-* | Memory handler CBs (3 total) |
| CB:fil-* | File handler CBs (8 total) |
| CB:db-* | Database handler CBs (6 total) |
| CB:gh-* | GitHub handler CBs (15 total) |
| CB:74total | All system circuit breakers |

### COMMON TECHNICAL TERMS

| Term | Symbol/Abbrev |
|------|---------------|
| Circuit Breaker | CB |
| Operations | ops |
| Configuration | cfg |
| Implementation | impl |
| Interface | I: |
| Schema | schema |
| Validation | val |
| Performance | perf |
| Baseline | base |
| Enhancement | enh |

### EXAMPLE TRANSFORMATIONS

**BEFORE:** "Extract memory.ts insert(), CB 'memory-insert', SQL patterns"
**AFTER:** "🔎 P:mem.ts M:insert CB:mem-insert SQL"

**BEFORE:** "Impl getHealthStatus() in BaseHandler.ts"
**AFTER:** "⚡ P:BaseHandler.ts M:getHealthStatus"

**BEFORE:** "Test health monitoring vs live operational DB"
**AFTER:** "🧪 health mon vs DB:operational"

**BEFORE:** "Add 'handlers' to MonitoringCommand interface"
**AFTER:** "⚡ A:handlers→I:MonitoringCommand"

### SYSTEM COVERAGE

- Handlers with abbreviated names (mem, fil, db, gh, mon, coord, fet, tim, git, term)
- Circuit breakers with CB: notation
- Dual-server architecture with S: notation (8081/8082)
- Operational database with DB: notation (240+ records)
- Performance baselines with T: notation (1-4ms, 227-318ms)

### EXECUTION ENVIRONMENT

| Component | Production | Development |
|-----------|------------|-------------|
| Server Port | S:8081 | S:8082 |
| Database | DB:.augment/db/augster.db | DB:.augment/db/augster-dev.db |
| Node Env | NODE_ENV=production | NODE_ENV=development |
| Build Req | npm run build required | ts-node runtime |
| Start Cmd | node dist/mcp/server.js | npm run mcp:dev |

### PREREQUISITE VALIDATION

| Check | Command | Expected |
|-------|---------|----------|
| Node Version | node --version | >=18.0.0 |
| TypeScript | npx tsc --version | ^5.8.3 |
| Database Exists | ls .augment/db/ | augster.db present |
| Build Status | ls dist/ | Compiled JS files |
| Env Files | ls .env* | .env.dev, .env.memory |

### TOOL EXECUTION CONTEXT

| Tool | Required Context | Validation |
|------|------------------|------------|
| view | P: file paths | File exists check |
| str-replace-editor | P: + L: ranges | Line range validation |
| launch-process | S: server ports | Port availability |
| codebase-retrieval | Workspace root | /workspaces/augster_mvp_scaffold |

### ERROR PREVENTION METADATA

| Risk | Prevention | Validation |
|------|------------|------------|
| File Not Found | P: notation mandatory | ✅ File exists |
| Port Conflicts | S: notation explicit | ✅ Port available |
| Build Failures | Pre-build validation | ✅ TypeScript compiles |
| DB Corruption | Backup before changes | ✅ DB integrity |
| Schema Mismatch | Schema validation | ✅ JSON schema valid |

### DEPENDENCY VALIDATION

| Component | Version | Validation Command |
|-----------|---------|-------------------|
| Node.js | >=18.0.0 | node --version |
| TypeScript | ^5.8.3 | npx tsc --version |
| SQLite3 | ^5.1.7 | npm list sqlite3 |
| AJV | ^8.17.1 | npm list ajv |
| FS-Extra | ^11.3.0 | npm list fs-extra |

### SCHEMA VALIDATION PATHS

| Handler | Schema Path | Validation |
|---------|-------------|------------|
| memory | .augment/config/validation/memory.schema.json | ajv validate |
| file | .augment/config/validation/file.schema.json | ajv validate |
| database | .augment/config/validation/database.schema.json | ajv validate |
| github | .augment/config/validation/github.schema.json | ajv validate |
| monitoring | .augment/config/validation/monitoring.schema.json | ajv validate |
| coordination | .augment/config/validation/coordination.schema.json | ajv validate |

### TASK EXECUTION SEQUENCE

1. **Pre-execution**: Validate environment, dependencies, file paths
2. **Execution**: Apply symbolic notation, use P:M:CB:I: parameters
3. **Validation**: Verify outputs, check schema compliance
4. **Post-execution**: Update operational database, log results

### TASK VALIDATION PATTERNS

| Pattern | Required Elements | Example |
|---------|------------------|---------|
| Deep Analysis | 🔎 + P: + L: + specific target | 🔎 P:mem.ts L:1-200 M:insert CB:mem-insert SQL |
| File Analysis | 🔎 + P: + specific target | 🔎 P:mem.ts M:insert CB:mem-insert |
| Batch Analysis | 🔎 + BA: + pattern | 🔎 BA:src/mcp/handlers/*.ts M:executeOperation CB:*-* |
| Implementation | ⚡ + P: + M: + target location | ⚡ M:getHealthStatus→P:BaseHandler.ts |
| TS Implementation | ⚡ + TS: + P: + validation | ⚡ TS:validate M:getHealthStatus→P:BaseHandler.ts |
| Testing | 🧪 + specific criteria + T: | 🧪 H:mem CB:3 T:<100ms |
| Validation | ✅ + verification method | ✅ CB total: 74/75+gaps |
| TS Validation | ✅ + TS: + validation check | ✅ TS:validate P:BaseHandler.ts syntax |
| Documentation | 📝 + P: + content type | 📝 A:handlers docs→API reference |
| DB Migration | 🔧 + DB:migrate + schema change | 🔧 DB:migrate schema.ts→performance_baselines |

### STANDARDIZED TASK METADATA

| Field | Format | Validation Rule |
|-------|--------|-----------------|
| UUID | [a-zA-Z0-9]{22} | Unique identifier |
| Status | [ ], [/], [x], [-] | Valid status only |
| Name | Action + Target | <50 chars, descriptive |
| Description | Symbol + Parameters | P:M:CB:I: notation |

### EXECUTION RELIABILITY CHECKLIST

- [ ] All P: paths validated before execution
- [ ] All M: methods exist in target files
- [ ] All CB: names match circuit breaker registry
- [ ] All I: interfaces defined in TypeScript
- [ ] All S: servers available on specified ports
- [ ] All DB: databases accessible and intact
- [ ] All T: targets achievable within constraints

### ENHANCED NOTATION EXAMPLES

``` typescript
Individual Analysis: 🔎 P:mem.ts M:insert CB:mem-insert SQL
Batch Analysis:     🔎 BA:src/mcp/handlers/*.ts M:insert CB:*-insert SQL

Standard Implementation: ⚡ M:getHealthStatus→P:BaseHandler.ts
TS Validated:           ⚡ TS:validate M:getHealthStatus→P:BaseHandler.ts

Manual Schema Change: 🔧 performance_baselines→schema.ts
Safe Migration:       🔧 DB:migrate schema.ts→performance_baselines
```

### USAGE GUIDELINES

1. **Use BA:** for analyzing multiple files with patterns (*.ts,*.js)
2. **Use TS:** for all implementation tasks requiring TypeScript validation
3. **Use DB:migrate** for any schema changes to operational database
4. **Combine tools** for complex workflows: BA: → TS: → DB:migrate
5. **Validate notation** using task validation patterns before execution

## SYSTEMATIC AI TASK MANAGEMENT

"Some inputted task prompt" -> { PROMPT FOR CLARIFYING QUESTIONS REQUIRED TO ENSURE ACCURATE RESPONSE } -> { TASK DECOMPOSITION } -> { TASK-COST-ESTIMATION } -> { TASK-EXECUTION-PLAN } ->  { TOOL-USAGE } || { LOCAL-RESOURCES } || { WORKFLOW CREATION } -> { CREATE-TASK-TREE}  

## { CREATE-TASK-TREE} OUTPUT

[ ] UUID:1omivVDUtBX69koPqcDkBN NAME:{ PARENT-1 } DESCRIPTION:{ CUSTOM-ENHANCED-NOTATION-1 W/. DEP COMPREHENSIVE MAPPING + AI CRITICAL METADATA }
-[ ] UUID:2P44Dcku1kDa6Lxrg5TeZc NAME:{ CHILD-1 }: Current System Analysis DESCRIPTION:{ CUSTOM-ENHANCED-NOTATION-2 + AI CRITICAL METADATA }
--[ ] UUID:mU5yGNwJ9Sg3xZNrWwjSSk NAME:{ GRANDCHILD-1 } DESCRIPTION:{ CUSTOM-ENHANCED-NOTATION-3 + AI CRITICAL METADATA }
---[ ] UUID:amb46G7ea8PfZaZM289pUx NAME:{ GREAT-GRANDCHILD-1 } DESCRIPTION:{ CUSTOM ENHANCED NOTATION-4 + AI CRITICAL METADATA}

## INTERACT WITH AUGMENT TO CREATE TASK W/. UUID + META + CONTEXT + INTENT =>> ONLY AI READABLE FORMAT !! NO HUMAN READABLE NEEDED FOR MAX CONTEXT to QUALITY + EFFICIENCY
