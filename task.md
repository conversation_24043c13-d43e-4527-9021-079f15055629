[x] NAME:🔎 Universal Codebase Discovery + Full-System Task Decomposition
DESCRIPTION:🔎 Use BA:* to recursively discover all files in the workspace (core, scripts, runtime, config, schema, augment-specific). Group findings by layer (runtime, memory, mcp, planning, infrastructure, templates, test, cli, config). Present user with file summary → then begin full task decomposition using MCP memory and trait logging to track scope, purpose, and quality impact. 

-[ ] NAME:🔎 Discover All Files by Purpose
DESCRIPTION:🔎 Use BA:**/* and classify as: runtime, coordination, memory, file IO, MCP handler, CLI, config, docs, templates, schema, .augment. Group by system function + location. Log total file count, file types, and missing docs/tests.

-[ ] NAME:⚡ Augment-Compatible Task Decomposition for Full Stack
DESCRIPTION:⚡ Generate a complete, AI-symbolic task tree across all discovered files. Each subtask must:
- Respect 150-line edit limits
- Use scoped notation (P:, L:, M:, CB:, I:)
- Enforce trait-evidence capture
- Track via MCP memory.insert
--[ ] NAME:🔎 Identify Runtime Pipeline Entrypoints
DESCRIPTION:🔎 Trace execution path starting from `launch.ts`, through CLI/init/coordination to planner. Identify F:init, F:plan, F:handleCommand. Record trace path using CB:term-* and CB:coord-* notations. MCP-log trace to mcp_calls.

--[ ] NAME:♾️ Validate Trait Integration Across Runtime
DESCRIPTION:♾️ Evaluate SelfCorrecting and ExecutionAccountable usage across: `src/runtime`, `src/agent/executionPlanner`, `src/cli/`, `src/mcp/router.ts`. Confirm CB:mem-insert+reflection used on error branches. Quality ≥ 0.7 or trigger reflections.

--[ ] NAME:🛠 System Utility File Classification
DESCRIPTION:🛠 Classify files as core, support, utility, fallback. Detect any unused, duplicated, or overly large files. Create MCP-log with type=UtilityClassification + justification.

--[ ] NAME:✅ Validate Coverage of .augment + VSCode-Specifics
DESCRIPTION:✅ Review `.augment-guidelines`, `.augment/config/`, `.vscode/settings.json`, `.env*`. Validate DB:, S:, TS: versions. Log any inconsistent schema, launch commands, or invalid syntax in Prettier, ESLint configs.

-[ ] NAME:⚠️ Insert Reflection Chain for Weak Areas
DESCRIPTION:⚠️ Log all inconsistencies, schema drift, undocumented traits or unused tools as failures → auto-generate reflection entries with references to templates and traits. Score each with impact/urgency → sort into action plan.