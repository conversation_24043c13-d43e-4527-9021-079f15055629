/**
 * Time Handler - AI-Optimized Pure Functions
 * @notation P:core/handlers/time F:timeHand<PERSON>,executeTimeCommand CB:executeTimeCommand I:TimeCommand,TimeResult DB:time
 */

import { Database } from 'sqlite3'
import { processMultiHandlerTemplate } from '../tools'
import {
  Handler<PERSON>onfig,
  OperationResult,
  createHandlerConfig,
  executeWithResilience
} from './executeCommand'

export type TimeCommand = {
  readonly action:
    | 'current'
    | 'convert'
    | 'format'
    | 'parse'
    | 'add'
    | 'subtract'
    | 'diff'
    | 'template'
  readonly timezone?: string
  readonly timestamp?: number | string
  readonly path?: string
  readonly content?: string
  readonly format?: string
  readonly fromTimezone?: string
  readonly toTimezone?: string
  readonly amount?: number
  readonly unit?: 'seconds' | 'minutes' | 'hours' | 'days' | 'weeks' | 'months' | 'years'
  readonly timestamp1?: number | string
  readonly timestamp2?: number | string
  readonly options?: {
    readonly templateEngine?: 'simple' | 'mustache'
    readonly templateVars?: Record<string, unknown>
    readonly templateOutputPath?: string
  }
}

export type TimeResult = {
  readonly success: boolean
  readonly data?: {
    readonly timestamp: number
    readonly iso: string
    readonly formatted: string
    readonly timezone: string
    readonly utc: string
    readonly local: string
    readonly unix: number
    readonly result?: unknown
  }
  readonly error?: string
  readonly timestamp: number
  readonly processingTime: number
}

/**
 * F:createTimeConfig - Create time handler configuration
 * @notation P:none F:createTimeConfig CB:none I:HandlerConfig DB:none
 */
export const createTimeConfig = (): HandlerConfig => {
  return createHandlerConfig(
    'time',
    ['current', 'convert', 'format', 'parse', 'add', 'subtract', 'diff', 'template'],
    {
      failureThreshold: 5,
      recoveryTimeout: 30000,
      maxRetries: 3,
      baseDelay: 500
    }
  )
}

/**
 * F:validateTimeCommand - Validate time command structure
 * @notation P:command F:validateTimeCommand CB:none I:boolean DB:none
 */
export const validateTimeCommand = (command: unknown): command is TimeCommand => {
  if (!command || typeof command !== 'object') return false

  const cmd = command as Record<string, unknown>

  return (
    typeof cmd.action === 'string' &&
    ['current', 'convert', 'format', 'parse', 'add', 'subtract', 'diff', 'template'].includes(
      cmd.action
    )
  )
}

/**
 * F:executeTimeCurrent - Get current time in specified timezone
 * @notation P:timezone F:executeTimeCurrent CB:executeTimeCurrent I:TimeResult DB:time
 */
export const executeTimeCurrent = async (timezone?: string): Promise<TimeResult> => {
  const startTime = Date.now()

  try {
    const now = new Date()
    const tz = timezone || 'UTC'

    try {
      new Intl.DateTimeFormat('en', { timeZone: tz })
    } catch (error) {
      throw new Error(`Invalid timezone: ${tz}`)
    }

    const result = Object.freeze({
      timestamp: now.getTime(),
      iso: now.toISOString(),
      formatted: now.toLocaleString('en-US', { timeZone: tz }),
      timezone: tz,
      utc: now.toUTCString(),
      local: now.toLocaleString(),
      unix: Math.floor(now.getTime() / 1000)
    })

    return Object.freeze({
      success: true,
      data: result,
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message,
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  }
}

/**
 * F:executeTimeConvert - Convert timestamp between timezones
 * @notation P:timestamp,fromTimezone,toTimezone F:executeTimeConvert CB:executeTimeConvert I:TimeResult DB:time
 */
export const executeTimeConvert = async (
  timestamp: number | string,
  fromTimezone: string = 'UTC',
  toTimezone: string = 'UTC'
): Promise<TimeResult> => {
  const startTime = Date.now()

  try {
    let date: Date
    if (typeof timestamp === 'string') {
      date = new Date(timestamp)
    } else {
      date = new Date(timestamp)
    }

    if (isNaN(date.getTime())) {
      throw new Error('Invalid timestamp')
    }

    try {
      new Intl.DateTimeFormat('en', { timeZone: fromTimezone })
      new Intl.DateTimeFormat('en', { timeZone: toTimezone })
    } catch (error) {
      throw new Error(`Invalid timezone: ${fromTimezone} or ${toTimezone}`)
    }

    const result = Object.freeze({
      timestamp: date.getTime(),
      iso: date.toISOString(),
      formatted: date.toLocaleString('en-US', { timeZone: toTimezone }),
      timezone: toTimezone,
      utc: date.toUTCString(),
      local: date.toLocaleString(),
      unix: Math.floor(date.getTime() / 1000),
      result: Object.freeze({
        from: date.toLocaleString('en-US', { timeZone: fromTimezone }),
        to: date.toLocaleString('en-US', { timeZone: toTimezone }),
        fromTimezone,
        toTimezone
      })
    })

    return Object.freeze({
      success: true,
      data: result,
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message,
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  }
}

/**
 * F:executeTimeFormat - Format timestamp with custom format
 * @notation P:timestamp,format,timezone F:executeTimeFormat CB:executeTimeFormat I:TimeResult DB:time
 */
export const executeTimeFormat = async (
  timestamp: number | string,
  format?: string,
  timezone?: string
): Promise<TimeResult> => {
  const startTime = Date.now()

  try {
    let date: Date
    if (typeof timestamp === 'string') {
      date = new Date(timestamp)
    } else {
      date = new Date(timestamp)
    }

    if (isNaN(date.getTime())) {
      throw new Error('Invalid timestamp')
    }

    const tz = timezone || 'UTC'

    try {
      new Intl.DateTimeFormat('en', { timeZone: tz })
    } catch (error) {
      throw new Error(`Invalid timezone: ${tz}`)
    }

    const formatOptions: Intl.DateTimeFormatOptions = {}

    if (format) {
      if (format.includes('YYYY')) formatOptions.year = 'numeric'
      if (format.includes('MM')) formatOptions.month = '2-digit'
      if (format.includes('DD')) formatOptions.day = '2-digit'
      if (format.includes('HH')) formatOptions.hour = '2-digit'
      if (format.includes('mm')) formatOptions.minute = '2-digit'
      if (format.includes('ss')) formatOptions.second = '2-digit'
    } else {
      formatOptions.year = 'numeric'
      formatOptions.month = '2-digit'
      formatOptions.day = '2-digit'
      formatOptions.hour = '2-digit'
      formatOptions.minute = '2-digit'
      formatOptions.second = '2-digit'
    }

    formatOptions.timeZone = tz

    const result = Object.freeze({
      timestamp: date.getTime(),
      iso: date.toISOString(),
      formatted: date.toLocaleString('en-US', formatOptions),
      timezone: tz,
      utc: date.toUTCString(),
      local: date.toLocaleString(),
      unix: Math.floor(date.getTime() / 1000),
      result: Object.freeze({
        customFormat: format || 'default',
        formatted: date.toLocaleString('en-US', formatOptions)
      })
    })

    return Object.freeze({
      success: true,
      data: result,
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message,
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  }
}

/**
 * F:executeTimeAdd - Add time to timestamp
 * @notation P:timestamp,amount,unit F:executeTimeAdd CB:executeTimeAdd I:TimeResult DB:time
 */
export const executeTimeAdd = async (
  timestamp: number | string,
  amount: number,
  unit: string
): Promise<TimeResult> => {
  const startTime = Date.now()

  try {
    let date: Date
    if (typeof timestamp === 'string') {
      date = new Date(timestamp)
    } else {
      date = new Date(timestamp)
    }

    if (isNaN(date.getTime())) {
      throw new Error('Invalid timestamp')
    }

    let milliseconds = 0
    switch (unit) {
      case 'seconds':
        milliseconds = amount * 1000
        break
      case 'minutes':
        milliseconds = amount * 60 * 1000
        break
      case 'hours':
        milliseconds = amount * 60 * 60 * 1000
        break
      case 'days':
        milliseconds = amount * 24 * 60 * 60 * 1000
        break
      case 'weeks':
        milliseconds = amount * 7 * 24 * 60 * 60 * 1000
        break
      case 'months':
        milliseconds = amount * 30 * 24 * 60 * 60 * 1000
        break
      case 'years':
        milliseconds = amount * 365 * 24 * 60 * 60 * 1000
        break
      default:
        throw new Error(`Invalid unit: ${unit}`)
    }

    const newDate = new Date(date.getTime() + milliseconds)

    const result = Object.freeze({
      timestamp: newDate.getTime(),
      iso: newDate.toISOString(),
      formatted: newDate.toLocaleString(),
      timezone: 'UTC',
      utc: newDate.toUTCString(),
      local: newDate.toLocaleString(),
      unix: Math.floor(newDate.getTime() / 1000),
      result: Object.freeze({
        original: date.toISOString(),
        added: `${amount} ${unit}`,
        result: newDate.toISOString()
      })
    })

    return Object.freeze({
      success: true,
      data: result,
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message,
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  }
}

/**
 * F:executeTimeDiff - Calculate difference between two timestamps
 * @notation P:timestamp1,timestamp2 F:executeTimeDiff CB:executeTimeDiff I:TimeResult DB:time
 */
export const executeTimeDiff = async (
  timestamp1: number | string,
  timestamp2: number | string
): Promise<TimeResult> => {
  const startTime = Date.now()

  try {
    let date1: Date, date2: Date

    if (typeof timestamp1 === 'string') {
      date1 = new Date(timestamp1)
    } else {
      date1 = new Date(timestamp1)
    }

    if (typeof timestamp2 === 'string') {
      date2 = new Date(timestamp2)
    } else {
      date2 = new Date(timestamp2)
    }

    if (isNaN(date1.getTime()) || isNaN(date2.getTime())) {
      throw new Error('Invalid timestamp(s)')
    }

    const diffMs = Math.abs(date2.getTime() - date1.getTime())

    const result = Object.freeze({
      timestamp: Date.now(),
      iso: new Date().toISOString(),
      formatted: new Date().toLocaleString(),
      timezone: 'UTC',
      utc: new Date().toUTCString(),
      local: new Date().toLocaleString(),
      unix: Math.floor(Date.now() / 1000),
      result: Object.freeze({
        timestamp1: date1.toISOString(),
        timestamp2: date2.toISOString(),
        differenceMs: diffMs,
        differenceSeconds: Math.floor(diffMs / 1000),
        differenceMinutes: Math.floor(diffMs / (1000 * 60)),
        differenceHours: Math.floor(diffMs / (1000 * 60 * 60)),
        differenceDays: Math.floor(diffMs / (1000 * 60 * 60 * 24))
      })
    })

    return Object.freeze({
      success: true,
      data: result,
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message,
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  }
}

/**
 * F:executeTimeCommand - Execute time command with resilience
 * @notation P:command F:executeTimeCommand CB:executeTimeCommand I:OperationResult DB:time
 */
export const executeTimeCommand = async (
  command: TimeCommand
): Promise<OperationResult<TimeResult>> => {
  const config = createTimeConfig()

  return await executeWithResilience(command, config, async cmd => {
    const { action } = cmd

    switch (action) {
      case 'current':
        return await executeTimeCurrent(cmd.timezone)

      case 'convert':
        if (!cmd.timestamp) throw new Error('Timestamp is required for convert action')
        return await executeTimeConvert(cmd.timestamp, cmd.fromTimezone, cmd.toTimezone)

      case 'format':
        if (!cmd.timestamp) throw new Error('Timestamp is required for format action')
        return await executeTimeFormat(cmd.timestamp, cmd.format, cmd.timezone)

      case 'add':
        if (!cmd.timestamp || cmd.amount === undefined || !cmd.unit) {
          throw new Error('Timestamp, amount, and unit are required for add action')
        }
        return await executeTimeAdd(cmd.timestamp, cmd.amount, cmd.unit)

      case 'subtract':
        if (!cmd.timestamp || cmd.amount === undefined || !cmd.unit) {
          throw new Error('Timestamp, amount, and unit are required for subtract action')
        }
        return await executeTimeAdd(cmd.timestamp, -cmd.amount, cmd.unit)

      case 'diff':
        if (!cmd.timestamp1 || !cmd.timestamp2) {
          throw new Error('Both timestamp1 and timestamp2 are required for diff action')
        }
        return await executeTimeDiff(cmd.timestamp1, cmd.timestamp2)

      case 'template':
        const templateSource = cmd.content || cmd.path
        if (!templateSource)
          throw new Error('Either path or content must be provided for template action')
        return await executeTimeTemplate(
          templateSource,
          cmd.options?.templateVars || {},
          cmd.options?.templateEngine || 'simple'
        )

      default:
        throw new Error(`Unknown time action: ${action}`)
    }
  })
}

/**
 * F:executeTimeTemplate - Execute time template operation
 * @notation P:templateSource,vars,engine F:executeTimeTemplate CB:executeTimeTemplate I:TimeResult DB:none
 */
export const executeTimeTemplate = async (
  templateSource: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple'
): Promise<TimeResult> => {
  try {
    const result = await processMultiHandlerTemplate(templateSource, vars, engine)

    return Object.freeze({
      success: true,
      data: Object.freeze({
        timestamp: Date.now(),
        iso: new Date().toISOString(),
        formatted: new Date().toLocaleString(),
        timezone: 'UTC',
        utc: new Date().toUTCString(),
        local: new Date().toLocaleString(),
        unix: Math.floor(Date.now() / 1000),
        result: result.content
      }),
      timestamp: Date.now(),
      processingTime: 0
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message,
      timestamp: Date.now(),
      processingTime: 0
    })
  }
}

/**
 * F:timeHandler - Create time handler function
 * @notation P:db F:timeHandler CB:none I:function DB:time
 */
export const timeHandler = (_db: Database) => {
  return {
    execute: async (input: unknown): Promise<TimeResult> => {
      if (!validateTimeCommand(input)) {
        return Object.freeze({
          success: false,
          error: 'Invalid time command structure',
          timestamp: Date.now(),
          processingTime: 0
        })
      }

      const result = await executeTimeCommand(input)
      return result.success
        ? result.data
        : Object.freeze({
            success: false,
            error: result.error,
            timestamp: Date.now(),
            processingTime: 0
          })
    }
  }
}
