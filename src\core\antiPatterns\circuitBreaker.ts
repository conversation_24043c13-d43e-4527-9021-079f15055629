/**
 * Circuit Breaker - AI-Optimized Pure Functions
 * Migrated from class-based to pure function architecture
 *
 * @notation P:core/antiPatterns/circuitBreaker F:createCircuitBreaker,executeWithCircuitBreaker CB:executeWithCircuitBreaker I:CircuitBreakerState,CircuitBreakerConfig DB:circuitBreaker
 */

export type CircuitBreakerState = 'CLOSED' | 'OPEN' | 'HALF_OPEN'

export type CircuitBreakerConfig = {
  readonly failureThreshold: number
  readonly recoveryTimeout: number
  readonly monitoringWindow: number
  readonly halfOpenMaxCalls: number
}

export type CircuitBreakerMetrics = {
  readonly state: CircuitBreakerState
  readonly failureCount: number
  readonly successCount: number
  readonly lastFailureTime: number
  readonly lastSuccessTime: number
  readonly totalCalls: number
  readonly rejectedCalls: number
}

export type CircuitBreakerInstance = {
  readonly name: string
  readonly config: CircuitBreakerConfig
  readonly state: CircuitBreakerState
  readonly failureCount: number
  readonly successCount: number
  readonly lastFailureTime: number
  readonly lastSuccessTime: number
  readonly totalCalls: number
  readonly rejectedCalls: number
  readonly halfOpenCalls: number
}

/**
 * F:createDefaultCircuitBreakerConfig - Create default circuit breaker configuration
 * @notation P:none F:createDefaultCircuitBreakerConfig CB:none I:CircuitBreakerConfig DB:none
 */
export const createDefaultCircuitBreakerConfig = (): CircuitBreakerConfig => {
  return Object.freeze({
    failureThreshold: 5,
    recoveryTimeout: 30000, // 30 seconds
    monitoringWindow: 60000, // 1 minute
    halfOpenMaxCalls: 3
  })
}

/**
 * F:createCircuitBreaker - Create new circuit breaker instance
 * @notation P:name,config F:createCircuitBreaker CB:none I:CircuitBreakerInstance DB:circuitBreaker
 */
export const createCircuitBreaker = (
  name: string,
  config: Partial<CircuitBreakerConfig> = {}
): CircuitBreakerInstance => {
  const fullConfig = Object.freeze({
    ...createDefaultCircuitBreakerConfig(),
    ...config
  })

  return Object.freeze({
    name,
    config: fullConfig,
    state: 'CLOSED' as CircuitBreakerState,
    failureCount: 0,
    successCount: 0,
    lastFailureTime: 0,
    lastSuccessTime: 0,
    totalCalls: 0,
    rejectedCalls: 0,
    halfOpenCalls: 0
  })
}

/**
 * F:shouldAttemptReset - Check if circuit breaker should attempt reset
 * @notation P:instance F:shouldAttemptReset CB:none I:boolean DB:none
 */
export const shouldAttemptReset = (instance: CircuitBreakerInstance): boolean => {
  return Date.now() - instance.lastFailureTime >= instance.config.recoveryTimeout
}

/**
 * F:transitionToHalfOpen - Transition circuit breaker to half-open state
 * @notation P:instance F:transitionToHalfOpen CB:none I:CircuitBreakerInstance DB:circuitBreaker
 */
export const transitionToHalfOpen = (instance: CircuitBreakerInstance): CircuitBreakerInstance => {
  return Object.freeze({
    ...instance,
    state: 'HALF_OPEN' as CircuitBreakerState,
    halfOpenCalls: 0
  })
}

/**
 * F:onCircuitBreakerSuccess - Handle successful operation
 * @notation P:instance F:onCircuitBreakerSuccess CB:none I:CircuitBreakerInstance DB:circuitBreaker
 */
export const onCircuitBreakerSuccess = (
  instance: CircuitBreakerInstance
): CircuitBreakerInstance => {
  const updatedInstance = Object.freeze({
    ...instance,
    successCount: instance.successCount + 1,
    lastSuccessTime: Date.now()
  })

  if (instance.state === 'HALF_OPEN') {
    return Object.freeze({
      ...updatedInstance,
      state: 'CLOSED' as CircuitBreakerState,
      failureCount: 0,
      halfOpenCalls: 0
    })
  }

  return updatedInstance
}

/**
 * F:onCircuitBreakerFailure - Handle failed operation
 * @notation P:instance F:onCircuitBreakerFailure CB:none I:CircuitBreakerInstance DB:circuitBreaker
 */
export const onCircuitBreakerFailure = (
  instance: CircuitBreakerInstance
): CircuitBreakerInstance => {
  const updatedInstance = Object.freeze({
    ...instance,
    failureCount: instance.failureCount + 1,
    lastFailureTime: Date.now()
  })

  if (instance.state === 'HALF_OPEN') {
    return Object.freeze({
      ...updatedInstance,
      state: 'OPEN' as CircuitBreakerState,
      halfOpenCalls: 0
    })
  }

  if (instance.state === 'CLOSED') {
    const cleanedInstance = cleanupOldFailures(updatedInstance)

    if (cleanedInstance.failureCount >= instance.config.failureThreshold) {
      return Object.freeze({
        ...cleanedInstance,
        state: 'OPEN' as CircuitBreakerState
      })
    }

    return cleanedInstance
  }

  return updatedInstance
}

/**
 * F:cleanupOldFailures - Clean up old failures outside monitoring window
 * @notation P:instance F:cleanupOldFailures CB:none I:CircuitBreakerInstance DB:none
 */
export const cleanupOldFailures = (instance: CircuitBreakerInstance): CircuitBreakerInstance => {
  const cutoffTime = Date.now() - instance.config.monitoringWindow

  if (instance.lastFailureTime < cutoffTime) {
    return Object.freeze({
      ...instance,
      failureCount: 0
    })
  }

  return instance
}

/**
 * F:getCircuitBreakerMetrics - Get circuit breaker metrics
 * @notation P:instance F:getCircuitBreakerMetrics CB:none I:CircuitBreakerMetrics DB:none
 */
export const getCircuitBreakerMetrics = (
  instance: CircuitBreakerInstance
): CircuitBreakerMetrics => {
  return Object.freeze({
    state: instance.state,
    failureCount: instance.failureCount,
    successCount: instance.successCount,
    lastFailureTime: instance.lastFailureTime,
    lastSuccessTime: instance.lastSuccessTime,
    totalCalls: instance.totalCalls,
    rejectedCalls: instance.rejectedCalls
  })
}

/**
 * F:resetCircuitBreaker - Reset circuit breaker to initial state
 * @notation P:instance F:resetCircuitBreaker CB:none I:CircuitBreakerInstance DB:circuitBreaker
 */
export const resetCircuitBreaker = (instance: CircuitBreakerInstance): CircuitBreakerInstance => {
  return Object.freeze({
    ...instance,
    state: 'CLOSED' as CircuitBreakerState,
    failureCount: 0,
    successCount: 0,
    halfOpenCalls: 0,
    rejectedCalls: 0
  })
}

/**
 * F:forceOpenCircuitBreaker - Force circuit breaker to open state
 * @notation P:instance F:forceOpenCircuitBreaker CB:none I:CircuitBreakerInstance DB:circuitBreaker
 */
export const forceOpenCircuitBreaker = (
  instance: CircuitBreakerInstance
): CircuitBreakerInstance => {
  return Object.freeze({
    ...instance,
    state: 'OPEN' as CircuitBreakerState,
    lastFailureTime: Date.now()
  })
}

export class CircuitBreakerError extends Error {
  constructor(
    message: string,
    public readonly metrics: CircuitBreakerMetrics
  ) {
    super(message)
    this.name = 'CircuitBreakerError'
  }
}

/**
 * F:executeWithCircuitBreaker - Execute operation with circuit breaker protection
 * @notation P:instance,operation F:executeWithCircuitBreaker CB:executeWithCircuitBreaker I:object DB:circuitBreaker
 */
export const executeWithCircuitBreaker = async <T>(
  instance: CircuitBreakerInstance,
  operation: () => Promise<T>
): Promise<{ result: T; updatedInstance: CircuitBreakerInstance }> => {
  let currentInstance = Object.freeze({
    ...instance,
    totalCalls: instance.totalCalls + 1
  })

  if (currentInstance.state === 'OPEN') {
    if (shouldAttemptReset(currentInstance)) {
      currentInstance = transitionToHalfOpen(currentInstance)
    } else {
      const rejectedInstance = Object.freeze({
        ...currentInstance,
        rejectedCalls: currentInstance.rejectedCalls + 1
      })

      throw new CircuitBreakerError(
        `Circuit breaker '${currentInstance.name}' is OPEN. Last failure: ${new Date(currentInstance.lastFailureTime).toISOString()}`,
        getCircuitBreakerMetrics(rejectedInstance)
      )
    }
  }

  if (currentInstance.state === 'HALF_OPEN') {
    if (currentInstance.halfOpenCalls >= currentInstance.config.halfOpenMaxCalls) {
      const rejectedInstance = Object.freeze({
        ...currentInstance,
        rejectedCalls: currentInstance.rejectedCalls + 1
      })

      throw new CircuitBreakerError(
        `Circuit breaker '${currentInstance.name}' is HALF_OPEN and at call limit`,
        getCircuitBreakerMetrics(rejectedInstance)
      )
    }

    currentInstance = Object.freeze({
      ...currentInstance,
      halfOpenCalls: currentInstance.halfOpenCalls + 1
    })
  }

  try {
    const result = await operation()
    const successInstance = onCircuitBreakerSuccess(currentInstance)
    return { result, updatedInstance: successInstance }
  } catch (error) {
    const failureInstance = onCircuitBreakerFailure(currentInstance)
    throw error
  }
}

/**
 * F:createCircuitBreakerRegistry - Create circuit breaker registry
 * @notation P:none F:createCircuitBreakerRegistry CB:none I:Map DB:circuitBreaker
 */
export const createCircuitBreakerRegistry = (): Map<string, CircuitBreakerInstance> => {
  return new Map()
}

/**
 * F:getOrCreateCircuitBreaker - Get or create circuit breaker in registry
 * @notation P:registry,name,config F:getOrCreateCircuitBreaker CB:none I:CircuitBreakerInstance DB:circuitBreaker
 */
export const getOrCreateCircuitBreaker = (
  registry: Map<string, CircuitBreakerInstance>,
  name: string,
  config?: Partial<CircuitBreakerConfig>
): CircuitBreakerInstance => {
  if (!registry.has(name)) {
    registry.set(name, createCircuitBreaker(name, config))
  }
  return registry.get(name)!
}

/**
 * F:getAllCircuitBreakerMetrics - Get all circuit breaker metrics from registry
 * @notation P:registry F:getAllCircuitBreakerMetrics CB:none I:object DB:none
 */
export const getAllCircuitBreakerMetrics = (
  registry: Map<string, CircuitBreakerInstance>
): Record<string, CircuitBreakerMetrics> => {
  const metrics: Record<string, CircuitBreakerMetrics> = {}
  for (const [name, instance] of registry) {
    metrics[name] = getCircuitBreakerMetrics(instance)
  }
  return Object.freeze(metrics)
}

/**
 * F:resetAllCircuitBreakers - Reset all circuit breakers in registry
 * @notation P:registry F:resetAllCircuitBreakers CB:none I:void DB:circuitBreaker
 */
export const resetAllCircuitBreakers = (registry: Map<string, CircuitBreakerInstance>): void => {
  for (const [name, instance] of registry) {
    registry.set(name, resetCircuitBreaker(instance))
  }
}
