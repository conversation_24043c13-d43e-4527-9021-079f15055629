{"$schema": "http://json-schema.org/draft-07/schema#", "title": "File Operations Schema", "description": "Validation schema for file operations handler commands", "type": "object", "properties": {"action": {"type": "string", "enum": ["read", "write", "exists", "list", "search", "analyze", "backup", "template"]}, "path": {"type": "string", "minLength": 1, "description": "File or directory path relative to workspace root"}, "content": {"type": "string", "description": "File content or template variables (JSON string for template action)"}, "encoding": {"type": "string", "enum": ["utf8", "binary", "base64"], "default": "utf8"}, "options": {"type": "object", "properties": {"recursive": {"type": "boolean", "default": false}, "filter": {"type": "string"}, "maxDepth": {"type": "integer", "minimum": 1, "maximum": 10, "default": 5}, "atomic": {"type": "boolean", "default": true}, "searchPattern": {"type": "string"}, "searchRegex": {"type": "boolean", "default": false}, "searchCaseSensitive": {"type": "boolean", "default": false}, "searchIncludeLineNumbers": {"type": "boolean", "default": false}, "searchMaxResults": {"type": "integer", "minimum": 1, "maximum": 1000, "default": 100}, "backupDir": {"type": "string"}, "backupVersions": {"type": "integer", "minimum": 1, "maximum": 100, "default": 5}, "backupCompress": {"type": "boolean", "default": false}, "templateVars": {"type": "object", "description": "Template variables for substitution"}, "templateEngine": {"type": "string", "enum": ["simple", "mustache"], "default": "simple"}, "templateOutputPath": {"type": "string", "description": "Output path for processed template"}, "analyzeMetrics": {"type": "array", "items": {"type": "string", "enum": ["size", "lines", "words", "chars", "complexity", "dependencies"]}, "default": ["size", "lines"]}, "analyzeLanguage": {"type": "string"}}}}, "required": ["action", "path"], "additionalProperties": false}