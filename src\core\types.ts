/* eslint-disable no-unused-vars */
/**
 * Core Types - AI-Optimized TypeScript
 * Machine-only types for AI consumption and mutation
 *
 * @notation P:core/types F:all CB:none I:all DB:none
 */

export type AINotationSymbol = 'P' | 'F' | 'CB' | 'I' | 'S' | 'DB'

/**
 * @I:TaskType - AI notation task types for execution planning
 * @notation P:none F:none CB:none I:TaskType DB:none
 */
export const TaskType = Object.freeze({
  EXTRACT: 'EXTRACT',
  IMPLEMENT: 'IMPLEMENT',
  TEST: 'TEST',
  VALIDATE: 'VALIDATE',
  DOCUMENT: 'DOCUMENT',
  CONFIGURE: 'CONFIGURE',
  DEPLOY: 'DEPLOY',
  STOP: 'STOP',
  MEASURE: 'MEASURE',
  COORDINATE: 'COORDINATE',
  COMPILE: 'COMPILE'
} as const)

export type TaskType = (typeof TaskType)[keyof typeof TaskType]

/**
 * @I:ParameterType - Parameter type mapping for AI notation
 * @notation P:none F:none CB:none I:ParameterType DB:none
 */
export const ParameterType = Object.freeze({
  PATH: 'P',
  METHOD: 'M',
  CIRCUIT_BREAKER: 'CB',
  INTERFACE: 'I',
  STRING: 'S',
  DATABASE: 'DB',
  HANDLER: 'H',
  ARGUMENT: 'A',
  TYPE: 'T'
} as const)

export type ParameterType = (typeof ParameterType)[keyof typeof ParameterType]

/**
 * @I:PARAMETER_KEY_MAP - Parameter key mapping for extraction
 * @notation P:none F:none CB:none I:PARAMETER_KEY_MAP DB:none
 */
export const PARAMETER_KEY_MAP = Object.freeze({
  [ParameterType.PATH]: 'path',
  [ParameterType.METHOD]: 'method',
  [ParameterType.CIRCUIT_BREAKER]: 'circuitBreaker',
  [ParameterType.INTERFACE]: 'interface',
  [ParameterType.STRING]: 'string',
  [ParameterType.DATABASE]: 'database',
  [ParameterType.HANDLER]: 'handler',
  [ParameterType.ARGUMENT]: 'argument',
  [ParameterType.TYPE]: 'type'
} as const)

/**
 * @I:AgentStatus - Agent status types for coordination
 * @notation P:none F:none CB:none I:AgentStatus DB:coordination
 */
export const AgentStatus = Object.freeze({
  ACTIVE: 'active',
  BUSY: 'busy',
  IDLE: 'idle',
  OFFLINE: 'offline'
} as const)

export type AgentStatus = (typeof AgentStatus)[keyof typeof AgentStatus]

/**
 * @I:MessageType - Message types for coordination
 * @notation P:none F:none CB:none I:MessageType DB:coordination
 */
export const MessageType = Object.freeze({
  TASK: 'task',
  STATUS: 'status',
  DATA: 'data',
  CONTROL: 'control'
} as const)

export type MessageType = (typeof MessageType)[keyof typeof MessageType]

/**
 * @I:MessagePriority - Message priority levels
 * @notation P:none F:none CB:none I:MessagePriority DB:coordination
 */
export const MessagePriority = Object.freeze({
  LOW: 'low',
  NORMAL: 'normal',
  HIGH: 'high',
  URGENT: 'urgent'
} as const)

export type MessagePriority = (typeof MessagePriority)[keyof typeof MessagePriority]

/**
 * @I:ExecutionPlanStatus - Execution plan status types
 * @notation P:none F:none CB:none I:ExecutionPlanStatus DB:execution
 */
export const ExecutionPlanStatus = Object.freeze({
  PLANNED: 'PLANNED',
  EXECUTING: 'EXECUTING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED'
} as const)

export type ExecutionPlanStatus = (typeof ExecutionPlanStatus)[keyof typeof ExecutionPlanStatus]

/**
 * @I:TransformationType - Context transformation types
 * @notation P:none F:none CB:none I:TransformationType DB:feedback
 */
export const TransformationType = Object.freeze({
  SUCCESS_PATTERN: 'SUCCESS_PATTERN',
  ERROR_PATTERN: 'ERROR_PATTERN',
  PERFORMANCE_INSIGHT: 'PERFORMANCE_INSIGHT',
  DEPENDENCY_DISCOVERY: 'DEPENDENCY_DISCOVERY',
  RESOURCE_OPTIMIZATION: 'RESOURCE_OPTIMIZATION'
} as const)

export type TransformationType = (typeof TransformationType)[keyof typeof TransformationType]

/**
 * @I:RefinementType - Execution refinement types
 * @notation P:none F:none CB:none I:RefinementType DB:feedback
 */
export const RefinementType = Object.freeze({
  TASK_REORDERING: 'TASK_REORDERING',
  RESOURCE_REALLOCATION: 'RESOURCE_REALLOCATION',
  RETRY_POLICY_ADJUSTMENT: 'RETRY_POLICY_ADJUSTMENT',
  DEPENDENCY_MODIFICATION: 'DEPENDENCY_MODIFICATION',
  PARALLELIZATION_OPTIMIZATION: 'PARALLELIZATION_OPTIMIZATION'
} as const)

export type RefinementType = (typeof RefinementType)[keyof typeof RefinementType]

export type AINotationMetadata = {
  readonly parametersDetected: Record<string, readonly string[]>
  readonly toolsDetected: Record<string, readonly string[]>
  readonly workflowCapable: boolean
  readonly validationRequired: boolean
}

export type HandlerConfig = {
  readonly name: string
  readonly operations: readonly OperationConfig[]
  readonly circuitBreakers: readonly CircuitBreakerConfig[]
  readonly retryPolicies: readonly RetryPolicy[]
}

export type OperationConfig = {
  readonly name: string
  readonly timeout: number
  readonly retryable: boolean
  readonly circuitBreakerKey: string
}

export type CircuitBreakerConfig = {
  readonly key: string
  readonly failureThreshold: number
  readonly recoveryTimeout: number
  readonly monitoringWindow: number
}

export type RetryPolicy = {
  readonly operationName: string
  readonly maxRetries: number
  readonly baseDelay: number
  readonly maxDelay: number
  readonly retryableErrors: readonly string[]
}

export type OperationResult<TData = unknown> =
  | {
      readonly success: true
      readonly data: TData
      readonly processingTime: number
      readonly timestamp: number
    }
  | {
      readonly success: false
      readonly error: string
      readonly processingTime: number
      readonly timestamp: number
    }

export type HandlerFunction<TCommand, TResult> = (
  command: TCommand,
  config: HandlerConfig
) => Promise<OperationResult<TResult>>

export type MetricsState = {
  readonly metrics: readonly PerformanceMetrics[]
  readonly maxSize: number
}

export type ActionState = {
  readonly handlers: ReadonlyMap<string, ActionHandler<unknown, unknown>>
}

export type PerformanceMetrics = {
  readonly timestamp: number
  readonly operation: string
  readonly handler: string
  readonly executionTime: number
  readonly success: boolean
  readonly circuitBreakerState?: string
  readonly retryAttempts?: number
  readonly memoryUsage?: NodeJS.MemoryUsage
}

export type ActionHandler<T = unknown, R = unknown> = (input: T) => Promise<R> | R

export type EnvironmentConfig = {
  readonly isDev: boolean
  readonly nodeEnv: 'development' | 'production'
  readonly mcpPort: string
  readonly dbPath: string
  readonly envFile: string
  readonly logLevel: 'debug' | 'info' | 'warn' | 'error'
  readonly logFile: string
}

export type EnvironmentTemplate = {
  readonly NODE_ENV: string
  readonly MCP_PORT: string
  readonly MCP_DB_PATH: string
  readonly LOG_LEVEL: string
  readonly LOG_FILE: string
}

/**
 * @I:BatchAnalysisOptions - Batch analysis configuration options
 * @notation P:none F:none CB:chunkSize_typedef_added I:BatchAnalysisOptions DB:analysis
 */
export type BatchAnalysisOptions = {
  readonly pattern: string
  readonly methods?: readonly string[]
  readonly circuitBreakers?: readonly string[]
  readonly interfaces?: readonly string[]
  readonly includeImports?: boolean
  readonly includeExports?: boolean
  readonly chunkSize?: number
  readonly includeMetrics?: boolean
}

export type FileAnalysisResult = {
  readonly filePath: string
  readonly relativePath: string
  readonly size: number
  readonly lines: number
  readonly methods: readonly MethodInfo[]
  readonly circuitBreakers: readonly CircuitBreakerInfo[]
  readonly interfaces: readonly InterfaceInfo[]
  readonly imports: readonly ImportInfo[]
  readonly exports: readonly ExportInfo[]
  readonly errors: readonly string[]
}

export type BatchAnalysisResult = {
  readonly pattern: string
  readonly totalFiles: number
  readonly totalLines: number
  readonly totalMethods: number
  readonly totalCircuitBreakers: number
  readonly totalInterfaces: number
  readonly files: readonly FileAnalysisResult[]
  readonly executionTime: number
}

export type MethodInfo = {
  readonly name: string
  readonly lineStart: number
  readonly lineEnd: number
  readonly parameters: readonly string[]
  readonly returnType: string
  readonly isAsync: boolean
  readonly visibility: 'public' | 'private' | 'protected'
}

export type CircuitBreakerInfo = {
  readonly name: string
  readonly lineNumber: number
  readonly pattern: string
  readonly handler: string
}

export type InterfaceInfo = {
  readonly name: string
  readonly lineStart: number
  readonly lineEnd: number
  readonly properties: readonly PropertyInfo[]
  readonly extends: readonly string[]
}

export type PropertyInfo = {
  readonly name: string
  readonly type: string
  readonly optional: boolean
  readonly lineNumber: number
}

export type ImportInfo = {
  readonly module: string
  readonly imports: readonly string[]
  readonly lineNumber: number
  readonly isTypeOnly: boolean
}

export type ExportInfo = {
  readonly name: string
  readonly type: 'function' | 'class' | 'interface' | 'const' | 'default'
  readonly lineNumber: number
}

export type TypeScriptAnalysis = {
  readonly filePath: string
  readonly functions: readonly string[]
  readonly classes: readonly string[]
  readonly interfaces: readonly string[]
  readonly imports: readonly string[]
  readonly exports: readonly string[]
  readonly errors: readonly string[]
}

export type ChunkedFileOptions = {
  readonly path: string
  readonly content: string
  readonly chunkSize?: number
}

export type ChunkedFileResult = {
  readonly success: boolean
  readonly chunksProcessed: number
  readonly totalSize: number
  readonly processingTime: number
  readonly errors: readonly string[]
  readonly atomicOperationUsed: boolean
}

export type EnhancedToolCommand = {
  readonly tool: 'BA'
  readonly action: string
  readonly parameters: Record<string, unknown>
}

export type EnhancedToolResult = {
  readonly success: boolean
  readonly tool: string
  readonly action: string
  readonly executionTime: number
  readonly data: unknown
  readonly errors: readonly string[]
  readonly warnings: readonly string[]
  readonly metadata: {
    readonly charactersSaved: number
    readonly executionStepsReduced: number
    readonly reliabilityImprovement: number
  }
}

export type TransformationRecord = {
  readonly timestamp: number
  readonly operation: string
  readonly sourceFile: string
  readonly targetFile?: string
  readonly changes: readonly string[]
  readonly success: boolean
  readonly rollbackData?: unknown
}

export type RefactorManifest = {
  readonly version: string
  readonly timestamp: number
  readonly transformations: readonly TransformationRecord[]
  readonly symbolMoves: readonly SymbolMove[]
  readonly totalChanges: number
}

export type SymbolMove = {
  readonly symbol: string
  readonly fromFile: string
  readonly toFile: string
  readonly type: 'function' | 'class' | 'interface' | 'type' | 'const'
  readonly notation: AINotationSymbol
}

/**
 * @I:CoordinationCommand - Coordination command structure
 * @notation P:none F:none CB:none I:CoordinationCommand DB:coordination
 */
export type CoordinationCommand = {
  readonly action:
    | 'discover'
    | 'register'
    | 'heartbeat'
    | 'message'
    | 'delegate'
    | 'sync'
    | 'resolve'
    | 'status'
    | 'agent_proposal'
    | 'agent_vote'
    | 'agent_status'
    | 'template'
  readonly agentId?: string
  readonly options?: Record<string, unknown>
  readonly content?: string
  readonly path?: string
}

/**
 * @I:Agent - Agent structure for coordination
 * @notation P:none F:none CB:none I:Agent DB:coordination
 */
export type Agent = {
  readonly id: string
  readonly capabilities: readonly string[]
  readonly priority: number
  readonly maxLoad: number
  readonly currentLoad: number
  readonly status: AgentStatus
  readonly lastHeartbeat?: number
}

/**
 * @I:Message - Message structure for coordination
 * @notation P:none F:none CB:none I:Message DB:coordination
 */
export type Message = {
  readonly id: string
  readonly type: MessageType
  readonly priority: MessagePriority
  readonly fromAgent: string
  readonly toAgent?: string
  readonly content: unknown
  readonly timestamp: number
}

/**
 * @I:WorkspaceResource - Workspace resource structure
 * @notation P:none F:none CB:none I:WorkspaceResource DB:coordination
 */
export type WorkspaceResource = {
  readonly id: string
  readonly type: string
  readonly path: string
  readonly status: 'available' | 'locked' | 'processing'
  readonly lockedBy?: string
  readonly lastModified: number
}

/**
 * @I:AgentProposal - Agent proposal structure
 * @notation P:none F:none CB:none I:AgentProposal DB:coordination
 */
export type AgentProposal = {
  readonly id: string
  readonly agentId: string
  readonly proposal: unknown
  readonly votes: readonly string[]
  readonly status: 'pending' | 'approved' | 'rejected'
  readonly timestamp: number
}

/**
 * @I:FileCommand - File command structure
 * @notation P:none F:none CB:none I:FileCommand DB:file
 */
export type FileCommand = {
  readonly action:
    | 'read'
    | 'write'
    | 'exists'
    | 'list'
    | 'search'
    | 'backup'
    | 'analyze'
    | 'template'
  readonly path: string
  readonly content?: string
  readonly encoding?: 'utf8' | 'binary' | 'base64'
  readonly options?: FileCommandOptions
}

/**
 * @I:FileResponse - File response structure
 * @notation P:none F:none CB:none I:FileResponse DB:file
 */
export type FileResponse = {
  readonly success: boolean
  readonly data?: unknown
  readonly content?: string
  readonly exists?: boolean
  readonly files?: readonly string[]
  readonly error?: string
  readonly processingTime?: number
  readonly timestamp?: number
}

/**
 * @I:FileCommandOptions - File command options
 * @notation P:none F:none CB:none I:FileCommandOptions DB:file
 */
export type FileCommandOptions = {
  readonly recursive?: boolean
  readonly filter?: string
  readonly maxDepth?: number
  readonly atomic?: boolean
  readonly searchPattern?: string
  readonly searchRegex?: boolean
  readonly searchCaseSensitive?: boolean
  readonly searchIncludeLineNumbers?: boolean
  readonly searchMaxResults?: number
  readonly metrics?: readonly string[]
  readonly language?: string
  readonly templateEngine?: 'simple' | 'mustache'
  readonly templateVars?: Record<string, unknown>
  readonly analyzeMetrics?: readonly string[]
  readonly analyzeLanguage?: string
  readonly backupDir?: string
  readonly backupVersions?: number
  readonly backupCompress?: boolean
}

/**
 * @I:GitHubCommand - GitHub command structure
 * @notation P:none F:none CB:none I:GitHubCommand DB:github
 */
export type GitHubCommand = {
  readonly action:
    | 'repo'
    | 'issues'
    | 'commits'
    | 'branches'
    | 'pulls'
    | 'files'
    | 'search'
    | 'create-issue'
    | 'template'
  readonly owner?: string
  readonly repo?: string
  readonly path?: string
  readonly options?: Record<string, unknown>
  readonly content?: string
  readonly templateVars?: Record<string, unknown>
}

/**
 * @I:GitHubResponse - GitHub response structure
 * @notation P:none F:none CB:none I:GitHubResponse DB:github
 */
export type GitHubResponse = {
  readonly success: boolean
  readonly data?: unknown
  readonly error?: string
  readonly rateLimit?: GitHubRateLimit
  readonly processingTime?: number
  readonly timestamp?: number
}

/**
 * @I:GitHubRateLimit - GitHub rate limit information
 * @notation P:none F:none CB:none I:GitHubRateLimit DB:github
 */
export type GitHubRateLimit = {
  readonly remaining: number
  readonly reset: number
  readonly limit: number
}

/**
 * @I:DatabaseCommand - Database command structure
 * @notation P:none F:none CB:none I:DatabaseCommand DB:database
 */
export type DatabaseCommand = {
  readonly action: 'connect' | 'execute' | 'query' | 'schema' | 'backup' | 'migrate' | 'template'
  readonly path: string
  readonly sql?: string
  readonly content?: string
  readonly options?: DatabaseCommandOptions
}

/**
 * @I:DatabaseResponse - Database response structure
 * @notation P:none F:none CB:none I:DatabaseResponse DB:database
 */
export type DatabaseResponse = {
  readonly success: boolean
  readonly data?: unknown
  readonly error?: string
  readonly connected?: boolean
  readonly databasePath?: string
  readonly rows?: readonly unknown[]
  readonly rowCount?: number
  readonly columns?: readonly string[]
  readonly schema?: unknown
  readonly tables?: readonly string[]
  readonly backupPath?: string
  readonly backupSize?: number
  readonly migrationApplied?: boolean
  readonly currentVersion?: string
  readonly processingTime?: number
  readonly timestamp?: number
}

/**
 * @I:DatabaseCommandOptions - Database command options
 * @notation P:none F:none CB:none I:DatabaseCommandOptions DB:database
 */
export type DatabaseCommandOptions = {
  readonly mode?: 'readonly' | 'readwrite' | 'create'
  readonly params?: readonly unknown[]
  readonly limit?: number
  readonly backupPath?: string
  readonly migrationSql?: readonly string[]
  readonly version?: string
  readonly templateVars?: Record<string, unknown>
  readonly templateEngine?: 'simple' | 'mustache'
}

/**
 * @I:AINotationResponse - AI notation response structure
 * @notation P:none F:none CB:none I:AINotationResponse DB:ai
 */
export type AINotationResponse = {
  readonly success: boolean
  readonly data?: unknown
  readonly notation?: string
  readonly metadata?: Record<string, unknown>
  readonly processingTime?: number
  readonly timestamp?: number
}

/**
 * @I:TaskConfig - Immutable task configuration
 * @notation P:none F:none CB:none I:TaskConfig DB:execution
 */
export type TaskConfig = {
  readonly handler: string
  readonly action: string
  readonly priority: number
  readonly duration: number
  readonly maxRetries: number
}

/**
 * @I:ExecutionTask - Immutable execution task
 * @notation P:none F:none CB:none I:ExecutionTask DB:execution
 */
export type ExecutionTask = {
  readonly id: string
  readonly type: TaskType
  readonly priority: number
  readonly dependencies: readonly string[]
  readonly resources: {
    readonly handler: string
    readonly action: string
    readonly parameters: Record<string, unknown>
  }
  readonly estimatedDuration: number
  readonly retryPolicy: {
    readonly maxRetries: number
    readonly backoffMultiplier: number
  }
  readonly metadata?: AINotationMetadata
}

/**
 * @I:ExecutionPlan - Immutable execution plan
 * @notation P:none F:none CB:none I:ExecutionPlan DB:execution
 */
export type ExecutionPlan = {
  readonly id: string
  readonly sessionId: string
  readonly tasks: readonly ExecutionTask[]
  readonly totalEstimatedDuration: number
  readonly resourceRequirements: {
    readonly handlers: readonly string[]
    readonly circuitBreakers: readonly string[]
    readonly databases: readonly string[]
  }
  readonly executionSequence: readonly string[][]
  readonly createdAt: number
  readonly status: ExecutionPlanStatus
}

/**
 * @I:ExecutionContext - Execution context (mutable for compatibility)
 * @notation P:none F:none CB:none I:ExecutionContext DB:execution
 */
export type ExecutionContext = {
  sessionId: string
  workspaceRoot: string
  database: import('sqlite3').Database
  router: (input: unknown) => Promise<unknown>
  logger: unknown // CentralLoggingDispatcher
  circuitBreakerRegistry: unknown
  retryManagerRegistry: unknown
}

/**
 * @I:ToolExecutionResult - Tool execution result
 * @notation P:none F:none CB:none I:ToolExecutionResult DB:execution
 */
export type ToolExecutionResult = {
  readonly taskId: string
  readonly planId: string
  readonly handler: string
  readonly action: string
  readonly success: boolean
  readonly output: unknown
  readonly error?: Error
  readonly executionTime: number
  readonly timestamp: number
  readonly metadata?: {
    readonly circuitBreakerState?: string
    readonly retryAttempts?: number
    readonly resourceUsage?: Record<string, unknown>
  }
}

/**
 * @I:ContextTransformation - Context transformation
 * @notation P:none F:none CB:none I:ContextTransformation DB:feedback
 */
export type ContextTransformation = {
  readonly id: string
  readonly sourceTaskId: string
  readonly transformationType: TransformationType
  readonly insights: {
    readonly pattern: string
    readonly confidence: number
    readonly applicability: readonly string[]
    readonly recommendations: readonly string[]
  }
  readonly createdAt: number
}

/**
 * @I:ExecutionRefinement - Execution refinement
 * @notation P:none F:none CB:none I:ExecutionRefinement DB:feedback
 */
export type ExecutionRefinement = {
  readonly planId: string
  readonly refinementType: RefinementType
  readonly changes: {
    readonly before: unknown
    readonly after: unknown
    readonly reasoning: string
  }
  readonly expectedImprovement: {
    readonly performanceGain: number
    readonly reliabilityIncrease: number
    readonly resourceEfficiency: number
  }
  readonly appliedAt: number
}

/**
 * @I:FeedbackContext - Feedback context
 * @notation P:none F:none CB:none I:FeedbackContext DB:feedback
 */
export type FeedbackContext = {
  readonly sessionId: string
  readonly database: import('sqlite3').Database
  readonly logger: unknown // CentralLoggingDispatcher
  readonly workspaceRoot: string
}

/**
 * @I:ActionConfig - Action configuration
 * @notation P:none F:none CB:none I:ActionConfig DB:actions
 */
export type ActionConfig<TInput = unknown, TOutput = unknown> = {
  readonly handler: ActionHandler<TInput, TOutput>
  readonly description?: string
  readonly validation?: (input: TInput) => boolean
  readonly metadata?: Record<string, unknown>
}

/**
 * @I:RegistryMetrics - Registry metrics
 * @notation P:none F:none CB:none I:RegistryMetrics DB:metrics
 */
export type RegistryMetrics = {
  readonly totalActions: number
  readonly executionCount: number
  readonly errorCount: number
  readonly averageExecutionTime: number
  readonly lastExecuted?: number
}

/**
 * @I:ValidationConfig - Validation configuration
 * @notation P:none F:none CB:none I:ValidationConfig DB:validation
 */
export type ValidationConfig = {
  readonly validator: (context: unknown) => Promise<{
    success: boolean
    details: string
    qualityScore: number
    evidence: Record<string, unknown>
  }>
  readonly description: string
  readonly dependencies?: readonly string[]
}

/**
 * @I:PatternAnalysisConfig - Pattern analysis configuration
 * @notation P:none F:none CB:none I:PatternAnalysisConfig DB:analysis
 */
export type PatternAnalysisConfig = {
  readonly analyzer: (result: unknown) => Promise<unknown>
  readonly confidence: number
  readonly applicability: readonly string[]
}
