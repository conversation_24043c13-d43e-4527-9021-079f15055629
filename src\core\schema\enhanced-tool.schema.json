{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Enhanced Tool Operations Schema", "description": "Validation schema for enhanced tool operations (BA:, TS:, DB:migrate)", "type": "object", "properties": {"action": {"type": "string", "enum": ["BA", "TS", "DB", "template"]}, "notation": {"type": "string", "description": "Enhanced notation string (e.g., BA:src/mcp/handlers/*.ts M:executeOperation)"}, "tool": {"type": "string", "enum": ["BA", "TS", "DB"]}, "parameters": {"type": "object", "properties": {"pattern": {"type": "string"}, "filePath": {"type": "string"}, "methods": {"type": "array", "items": {"type": "string"}}, "circuitBreakers": {"type": "array", "items": {"type": "string"}}, "interfaces": {"type": "array", "items": {"type": "string"}}, "sourceSchema": {"type": "string"}, "targetSchema": {"type": "string"}, "validateSyntax": {"type": "boolean"}, "validateTypes": {"type": "boolean"}, "dryRun": {"type": "boolean"}}}, "context": {"type": "object", "properties": {"workspaceRoot": {"type": "string"}, "serverPort": {"type": "integer"}, "environment": {"type": "string", "enum": ["development", "production"]}}}, "path": {"type": "string", "description": "Template path for template action"}, "content": {"type": "string", "description": "Template content for template action"}, "options": {"type": "object", "properties": {"templateEngine": {"type": "string", "enum": ["simple", "mustache"], "default": "simple"}, "templateVars": {"type": "object", "description": "Template variables for substitution"}, "templateOutputPath": {"type": "string", "description": "Output path for processed template"}}}}, "required": ["action"], "additionalProperties": false}