# OUTDATED AND NOT REFLECTIVE OF CURRENT STATE

## Augster AI Agent

:toc:

## Overview

Augster is a local, constraint-aware AI agent designed to work with Augment AI via MCP (Modular Command Protocol). It routes instructions through tool interfaces, maintains memory via SQLite, and structures outputs for distributed validation and traceability.

## Features

- Compatible with Augment AI MCP
- Tool-controlled memory access with operational database
- File operations with comprehensive safety checks and advanced features
- Database operations with multi-database support and migrations
- GitHub integration with rate limiting, PR automation, and enhanced operations
- Real-time monitoring dashboard with system health metrics
- Multi-agent coordination framework with load balancing
- TRACE → STATUS → SUMMARY output format
- Template-driven prompt parsing with global template directory
- Typed SQLite schema with persistent session history

## Running the Agent

1. Build the TypeScript code:
   $ npx tsc

2. Launch the MCP server:
   $ node dist/mcp/server.js --db-path .augment/db/augster.db --port 8081

## File Structure

- `src/agent/` – Execution engine and parser
- `src/mcp/` – Tool command router and MCP entry
- `src/mcp/handlers/` – BaseHand<PERSON> and 11 specialized handlers (memory, file, database, github, monitoring, coordination, fetch, time, git, terminal)
- `src/resilience/` – Circuit breakers, retry managers, and resilience monitoring (74 circuit breakers)
- `src/db/` – SQLite init and schema logic with operational database (240+ records)
- `logs/` – Stores per-session execution traces
- `.augment/` – Augment-specific config, db, templates, validation schemas
- `.vscode/` – VS Code workspace configuration with dual-server debugging
- `docs/` – Comprehensive documentation for AI agent consumption

## Integration

Configure `.augment-guidelines` and `settings.json` as documented in Augment MCP docs.
