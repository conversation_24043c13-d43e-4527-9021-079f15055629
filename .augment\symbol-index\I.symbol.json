[{"name": "CircuitBreakerState", "file": "src\\core\\antiPatterns\\circuitBreaker.ts", "notation": "P:core/antiPatterns/circuitBreaker F:createCircuitBreaker,executeWithCircuitBreaker CB:executeWithCircuitBreaker I:CircuitBreakerState,CircuitBreakerConfig DB:circuitBreaker", "block": "/**\n * Circuit Breaker - AI-Optimized Pure Functions\n * Migrated from class-based to pure function a..."}, {"name": "CircuitBreakerConfig", "file": "src\\core\\antiPatterns\\circuitBreaker.ts", "notation": "P:none F:createDefaultCircuitBreakerConfig CB:none I:CircuitBreakerConfig DB:none", "block": "/**\n * F:createDefaultCircuitBreakerConfig - Create default circuit breaker configuration\n * @notati..."}, {"name": "CircuitBreakerInstance", "file": "src\\core\\antiPatterns\\circuitBreaker.ts", "notation": "P:name,config F:createCircuitBreaker CB:none I:CircuitBreakerInstance DB:circuitBreaker", "block": "/**\n * F:createCircuitBreaker - Create new circuit breaker instance\n * @notation P:name,config F:cre..."}, {"name": "boolean", "file": "src\\core\\antiPatterns\\circuitBreaker.ts", "notation": "P:instance F:shouldAttemptReset CB:none I:boolean DB:none", "block": "/**\n * F:shouldAttemptReset - Check if circuit breaker should attempt reset\n * @notation P:instance ..."}, {"name": "CircuitBreakerInstance", "file": "src\\core\\antiPatterns\\circuitBreaker.ts", "notation": "P:instance F:transitionToHalfOpen CB:none I:CircuitBreakerInstance DB:circuitBreaker", "block": "/**\n * F:transitionToHalfOpen - Transition circuit breaker to half-open state\n * @notation P:instanc..."}, {"name": "CircuitBreakerInstance", "file": "src\\core\\antiPatterns\\circuitBreaker.ts", "notation": "P:instance F:onCircuitBreakerSuccess CB:none I:CircuitBreakerInstance DB:circuitBreaker", "block": "/**\n * F:onCircuitBreakerSuccess - Handle successful operation\n * @notation P:instance F:onCircuitBr..."}, {"name": "CircuitBreakerInstance", "file": "src\\core\\antiPatterns\\circuitBreaker.ts", "notation": "P:instance F:onCircuitBreakerFailure CB:none I:CircuitBreakerInstance DB:circuitBreaker", "block": "/**\n * F:onCircuitBreakerFailure - <PERSON><PERSON> failed operation\n * @notation P:instance F:onCircuitBreake..."}, {"name": "CircuitBreakerInstance", "file": "src\\core\\antiPatterns\\circuitBreaker.ts", "notation": "P:instance F:cleanupOldFailures CB:none I:CircuitBreakerInstance DB:none", "block": "/**\n * F:cleanupOldFailures - Clean up old failures outside monitoring window\n * @notation P:instanc..."}, {"name": "CircuitBreakerMetrics", "file": "src\\core\\antiPatterns\\circuitBreaker.ts", "notation": "P:instance F:getCircuitBreakerMetrics CB:none I:CircuitBreakerMetrics DB:none", "block": "/**\n * F:getCircuitBreakerMetrics - Get circuit breaker metrics\n * @notation P:instance F:getCircuit..."}, {"name": "CircuitBreakerInstance", "file": "src\\core\\antiPatterns\\circuitBreaker.ts", "notation": "P:instance F:resetCircuitBreaker CB:none I:CircuitBreakerInstance DB:circuitBreaker", "block": "/**\n * F:resetCircuitBreaker - Reset circuit breaker to initial state\n * @notation P:instance F:rese..."}, {"name": "CircuitBreakerInstance", "file": "src\\core\\antiPatterns\\circuitBreaker.ts", "notation": "P:instance F:forceOpenCircuitBreaker CB:none I:CircuitBreakerInstance DB:circuitBreaker", "block": "/**\n * F:forceOpenCircuitBreaker - Force circuit breaker to open state\n * @notation P:instance F:for..."}, {"name": "object", "file": "src\\core\\antiPatterns\\circuitBreaker.ts", "notation": "P:instance,operation F:executeWithCircuitBreaker CB:executeWithCircuitBreaker I:object DB:circuitBreaker", "block": "/**\n * F:executeWithCircuitBreaker - Execute operation with circuit breaker protection\n * @notation ..."}, {"name": "Map", "file": "src\\core\\antiPatterns\\circuitBreaker.ts", "notation": "P:none F:createCircuitBreakerRegistry CB:none I:Map DB:circuitBreaker", "block": "/**\n * F:createCircuitBreakerRegistry - Create circuit breaker registry\n * @notation P:none F:create..."}, {"name": "CircuitBreakerInstance", "file": "src\\core\\antiPatterns\\circuitBreaker.ts", "notation": "P:registry,name,config F:getOrCreateCircuitBreaker CB:none I:CircuitBreakerInstance DB:circuitBreaker", "block": "/**\n * F:getOrCreateCircuitBreaker - Get or create circuit breaker in registry\n * @notation P:regist..."}, {"name": "object", "file": "src\\core\\antiPatterns\\circuitBreaker.ts", "notation": "P:registry F:getAllCircuitBreakerMetrics CB:none I:object DB:none", "block": "/**\n * F:getAllCircuitBreakerMetrics - Get all circuit breaker metrics from registry\n * @notation P:..."}, {"name": "void", "file": "src\\core\\antiPatterns\\circuitBreaker.ts", "notation": "P:registry F:resetAllCircuitBreakers CB:none I:void DB:circuitBreaker", "block": "/**\n * F:resetAllCircuitBreakers - Reset all circuit breakers in registry\n * @notation P:registry F:..."}, {"name": "AntiPatternDetection", "file": "src\\core\\antiPatterns\\detector.ts", "notation": "P:core/antiPatterns/detector F:detectAntiPatterns,generateFixPlan,applyFixes CB:none I:AntiPatternDetection,FixPlan DB:none", "block": "/**\n * Anti-Pattern Detector - AI-Optimized Pattern Detection\n * Pure functions for detecting and fi..."}, {"name": "AntiPatternReport", "file": "src\\core\\antiPatterns\\detector.ts", "notation": "P:sourceCode,filePath F:detectAntiPatterns CB:none I:AntiPatternReport DB:none", "block": "/**\n * F:detectAntiPatterns - Detect all anti-patterns in source code\n * @notation P:sourceCode,file..."}, {"name": "FixPlan", "file": "src\\core\\antiPatterns\\detector.ts", "notation": "P:report F:generateFixPlan CB:none I:FixPlan DB:none", "block": "/**\n * F:generateFixPlan - Generate a plan to fix detected anti-patterns\n * @notation P:report F:gen..."}, {"name": "string", "file": "src\\core\\antiPatterns\\detector.ts", "notation": "P:sourceCode,fixPlan F:applyFixes CB:none I:string DB:none", "block": "/**\n * F:applyFixes - Apply fixes to source code\n * @notation P:sourceCode,fixPlan F:applyFixes CB:n..."}, {"name": "all", "file": "src\\core\\antiPatterns\\index.ts", "notation": "P:core/antiPatterns/index F:all CB:none I:all DB:resilience", "block": "/**\n * Core Anti-Patterns - AI-Optimized Anti-Pattern Exports\n * Machine-only exports for AI consump..."}, {"name": "object", "file": "src\\core\\antiPatterns\\index.ts", "notation": "P:circuitBreakerConfig,retryConfig,monitorConfig F:createUnifiedResilienceSystem CB:none I:object DB:resilience", "block": "/**\n * F:createUnifiedResilienceSystem - Create unified resilience system with all components\n * @no..."}, {"name": "SystemHealthMetrics", "file": "src\\core\\antiPatterns\\resilienceMonitor.ts", "notation": "P:core/antiPatterns/resilienceMonitor F:createResilienceMonitor,recordErrorRecoveryEvent CB:recordErrorRecoveryEvent I:SystemHealthMetrics,ErrorRecoveryEvent DB:resilience", "block": "/**\n * Resilience Monitor - AI-Optimized Pure Functions\n * Migrated from class-based to pure functio..."}, {"name": "ResilienceMonitorInstance", "file": "src\\core\\antiPatterns\\resilienceMonitor.ts", "notation": "P:maxEventHistory F:createResilienceMonitor CB:none I:ResilienceMonitorInstance DB:resilience", "block": "/**\n * F:createResilienceMonitor - Create new resilience monitor instance\n * @notation P:maxEventHis..."}, {"name": "ResilienceMonitorInstance", "file": "src\\core\\antiPatterns\\resilienceMonitor.ts", "notation": "P:instance,event F:recordErrorRecoveryEvent CB:recordErrorRecoveryEvent I:ResilienceMonitorInstance DB:resilience", "block": "/**\n * F:recordErrorRecoveryEvent - Record error recovery event\n * @notation P:instance,event F:reco..."}, {"name": "array", "file": "src\\core\\antiPatterns\\resilienceMonitor.ts", "notation": "P:instance,timeWindowMs F:getRecentErrorRecoveryEvents CB:none I:array DB:none", "block": "/**\n * F:getRecentErrorRecoveryEvents - Get recent error recovery events within time window\n * @nota..."}, {"name": "string", "file": "src\\core\\antiPatterns\\resilienceMonitor.ts", "notation": "P:circuitBreakers,retryManagers F:calculateSystemStatus CB:none I:string DB:none", "block": "/**\n * F:calculateSystemStatus - Calculate system status based on metrics\n * @notation P:circuitBrea..."}, {"name": "number", "file": "src\\core\\antiPatterns\\resilienceMonitor.ts", "notation": "P:instance F:calculateAvailability CB:none I:number DB:none", "block": "/**\n * F:calculateAvailability - Calculate system availability percentage\n * @notation P:instance F:..."}, {"name": "SystemHealthMetrics", "file": "src\\core\\antiPatterns\\resilienceMonitor.ts", "notation": "P:instance,circuitBreakers,retryManagers F:getSystemHealthMetrics CB:none I:SystemHealthMetrics DB:resilience", "block": "/**\n * F:getSystemHealthMetrics - Get comprehensive system health metrics\n * @notation P:instance,ci..."}, {"name": "string", "file": "src\\core\\antiPatterns\\resilienceMonitor.ts", "notation": "P:metrics F:exportMetricsForPrometheus CB:none I:string DB:none", "block": "/**\n * F:exportMetricsForPrometheus - Export metrics in Prometheus format\n * @notation P:metrics F:e..."}, {"name": "string", "file": "src\\core\\antiPatterns\\resilienceMonitor.ts", "notation": "P:metrics F:getSummaryReport CB:none I:string DB:none", "block": "/**\n * F:getSummaryReport - Get summary report for logging\n * @notation P:metrics F:getSummaryReport..."}, {"name": "void", "file": "src\\core\\antiPatterns\\resilienceMonitor.ts", "notation": "P:metrics F:performHealthCheck CB:none I:void DB:resilience", "block": "/**\n * F:performHealthCheck - Perform health check and log results\n * @notation P:metrics F:performH..."}, {"name": "void", "file": "src\\core\\antiPatterns\\resilienceMonitor.ts", "notation": "P:event F:logErrorRecoveryEvent CB:none I:void DB:none", "block": "/**\n * F:logErrorRecoveryEvent - Log error recovery event\n * @notation P:event F:logErrorRecoveryEve..."}, {"name": "RetryConfig", "file": "src\\core\\antiPatterns\\retryManager.ts", "notation": "P:core/antiPatterns/retryManager F:createRetryManager,executeWithRetry CB:executeWithRetry I:RetryConfig,RetryMetrics DB:retry", "block": "/**\n * Retry Manager - AI-Optimized Pure Functions\n * Migrated from class-based to pure function arc..."}, {"name": "RetryConfig", "file": "src\\core\\antiPatterns\\retryManager.ts", "notation": "P:none F:createDefaultRetryConfig CB:none I:RetryConfig DB:none", "block": "/**\n * F:createDefaultRetryConfig - Create default retry configuration\n * @notation P:none F:createD..."}, {"name": "RetryInstance", "file": "src\\core\\antiPatterns\\retryManager.ts", "notation": "P:config F:createRetryManager CB:none I:RetryInstance DB:retry", "block": "/**\n * F:createRetryManager - Create new retry manager instance\n * @notation P:config F:createRetryM..."}, {"name": "boolean", "file": "src\\core\\antiPatterns\\retryManager.ts", "notation": "P:error,config F:isRetryableError CB:none I:boolean DB:none", "block": "/**\n * F:isRetryableError - Check if error is retryable\n * @notation P:error,config F:isRetryableErr..."}, {"name": "number", "file": "src\\core\\antiPatterns\\retryManager.ts", "notation": "P:attempt,config F:calculateDelay CB:none I:number DB:none", "block": "/**\n * F:calculateDelay - Calculate delay with exponential backoff and jitter\n * @notation P:attempt..."}, {"name": "number", "file": "src\\core\\antiPatterns\\retryManager.ts", "notation": "P:delay,jitterPercent F:calculateJitter CB:none I:number DB:none", "block": "/**\n * F:calculateJitter - Calculate jitter for delay\n * @notation P:delay,jitterPercent F:calculate..."}, {"name": "Promise", "file": "src\\core\\antiPatterns\\retryManager.ts", "notation": "P:ms F:sleep CB:none I:Promise DB:none", "block": "/**\n * F:sleep - Sleep for specified milliseconds\n * @notation P:ms F:sleep CB:none I:Promise DB:non..."}, {"name": "RetryMetrics", "file": "src\\core\\antiPatterns\\retryManager.ts", "notation": "P:metrics,attempt,success F:updateRetryMetrics CB:none I:RetryMetrics DB:retry", "block": "/**\n * F:updateRetryMetrics - Update retry metrics\n * @notation P:metrics,attempt,success F:updateRe..."}, {"name": "object", "file": "src\\core\\antiPatterns\\retryManager.ts", "notation": "P:instance,operation,operationName F:executeWithRetry CB:executeWithRetry I:object DB:retry", "block": "/**\n * F:executeWithRetry - Execute operation with retry logic\n * @notation P:instance,operation,ope..."}, {"name": "Promise", "file": "src\\core\\antiPatterns\\retryManager.ts", "notation": "P:operation,config,operationName F:retryWithBackoff CB:retryWithBackoff I:Promise DB:retry", "block": "/**\n * F:retryWithBackoff - Simple retry operation utility\n * @notation P:operation,config,operation..."}, {"name": "Map", "file": "src\\core\\antiPatterns\\retryManager.ts", "notation": "P:none F:createRetryManagerRegistry CB:none I:Map DB:retry", "block": "/**\n * F:createRetryManagerRegistry - Create retry manager registry\n * @notation P:none F:createRetr..."}, {"name": "RetryInstance", "file": "src\\core\\antiPatterns\\retryManager.ts", "notation": "P:registry,name,config F:getOrCreateRetryManager CB:none I:RetryInstance DB:retry", "block": "/**\n * F:getOrCreateRetryManager - Get or create retry manager in registry\n * @notation P:registry,n..."}, {"name": "object", "file": "src\\core\\antiPatterns\\retryManager.ts", "notation": "P:registry F:getAllRetryMetrics CB:none I:object DB:none", "block": "/**\n * F:getAllRetryMetrics - Get all retry metrics from registry\n * @notation P:registry F:getAllRe..."}, {"name": "void", "file": "src\\core\\antiPatterns\\retryManager.ts", "notation": "P:registry F:resetAllRetryMetrics CB:none I:void DB:retry", "block": "/**\n * F:resetAllRetryMetrics - Reset all retry metrics in registry\n * @notation P:registry F:resetA..."}, {"name": "all", "file": "src\\core\\constants.ts", "notation": "P:core/constants F:none CB:none I:all DB:none", "block": "/**\n * Core Constants - AI-Optimized Frozen Configuration\n * Machine-only constants for AI consumpti..."}, {"name": "HandlerConfig", "file": "src\\core\\handlers\\BaseHandler.ts", "notation": "P:core/handlers/BaseHandler F:executeOperation,createHandlerState CB:executeOperation I:HandlerConfig,OperationResult DB:handlers", "block": "/**\n * Base Handler - AI-Optimized Pure Functions\n * Migrated from class-based to pure function arch..."}, {"name": "Map", "file": "src\\core\\handlers\\BaseHandler.ts", "notation": "P:none F:none CB:none I:Map DB:handlers", "block": "/**\n * @F:globalCircuitBreakerRegistry - Global circuit breaker registry\n * @notation P:none F:none ..."}, {"name": "Map", "file": "src\\core\\handlers\\BaseHandler.ts", "notation": "P:none F:none CB:none I:Map DB:handlers", "block": "/**\n * @F:globalRetryManagerRegistry - Global retry manager registry\n * @notation P:none F:none CB:n..."}, {"name": "ResilienceMonitorInstance", "file": "src\\core\\handlers\\BaseHandler.ts", "notation": "P:none F:none CB:none I:ResilienceMonitorInstance DB:handlers", "block": "/**\n * @F:globalResilienceMonitor - Global resilience monitor\n * @notation P:none F:none CB:none I:R..."}, {"name": "HandlerConfig", "file": "src\\core\\handlers\\BaseHandler.ts", "notation": "P:none F:none CB:none I:HandlerConfig DB:handlers", "block": "/**\n * @I:HandlerConfig - Handler configuration with operations\n * @notation P:none F:none CB:none I..."}, {"name": "OperationConfig", "file": "src\\core\\handlers\\BaseHandler.ts", "notation": "P:none F:none CB:none I:OperationConfig DB:handlers", "block": "/**\n * @I:OperationConfig - Individual operation configuration\n * @notation P:none F:none CB:none I:..."}, {"name": "OperationResult", "file": "src\\core\\handlers\\BaseHandler.ts", "notation": "P:none F:none CB:none I:OperationResult DB:handlers", "block": "/**\n * @I:OperationResult - Operation execution result\n * @notation P:none F:none CB:none I:Operatio..."}, {"name": "HandlerState", "file": "src\\core\\handlers\\BaseHandler.ts", "notation": "P:none F:none CB:none I:HandlerState DB:handlers", "block": "/**\n * @I:HandlerState - Handler state management\n * @notation P:none F:none CB:none I:HandlerState ..."}, {"name": "HandlerState", "file": "src\\core\\handlers\\BaseHandler.ts", "notation": "P:config F:createHandlerState CB:none I:HandlerState DB:handlers", "block": "/**\n * F:createHandlerState - Create initial handler state\n * @notation P:config F:createHandlerStat..."}, {"name": "OperationConfig", "file": "src\\core\\handlers\\BaseHandler.ts", "notation": "P:state,operationName F:getOperationConfig CB:none I:OperationConfig DB:none", "block": "/**\n * F:getOperationConfig - Get operation configuration from state\n * @notation P:state,operationN..."}, {"name": "array", "file": "src\\core\\handlers\\BaseHandler.ts", "notation": "P:state F:getOperations CB:none I:array DB:none", "block": "/**\n * F:getOperations - Get all configured operations from state\n * @notation P:state F:getOperatio..."}, {"name": "void", "file": "src\\core\\handlers\\BaseHandler.ts", "notation": "P:handler<PERSON><PERSON>,operation F:initializeOperation CB:initializeOperation I:void DB:handlers", "block": "/**\n * F:initializeOperation - Initialize circuit breaker and retry manager for operation\n * @notati..."}, {"name": "void", "file": "src\\core\\handlers\\BaseHandler.ts", "notation": "P:state F:initializeAllOperations CB:initializeAllOperations I:void DB:handlers", "block": "/**\n * F:initializeAllOperations - Initialize all operations for a handler\n * @notation P:state F:in..."}, {"name": "OperationResult", "file": "src\\core\\handlers\\BaseHandler.ts", "notation": "P:handler<PERSON><PERSON>,operationN<PERSON>,operationFn,context F:executeOperation CB:executeOperation I:OperationResult DB:handlers", "block": "/**\n * F:executeOperation - Execute operation with circuit breaker and retry protection\n * @notation..."}, {"name": "string", "file": "src\\core\\handlers\\BaseHandler.ts", "notation": "P:handler<PERSON><PERSON>,operationName F:createOperationKey CB:none I:string DB:none", "block": "/**\n * F:createOperationKey - Create operation key for circuit breaker/retry\n * @notation P:handlerN..."}, {"name": "object", "file": "src\\core\\handlers\\BaseHandler.ts", "notation": "P:overrides F:createDefaultCircuitBreakerConfig CB:none I:object DB:none", "block": "/**\n * F:createDefaultCircuitBreakerConfig - Create default circuit breaker configuration\n * @notati..."}, {"name": "object", "file": "src\\core\\handlers\\BaseHandler.ts", "notation": "P:overrides F:createDefaultRetryConfig CB:none I:object DB:none", "block": "/**\n * F:createDefaultRetryConfig - Create default retry configuration\n * @notation P:overrides F:cr..."}, {"name": "OperationResult", "file": "src\\core\\handlers\\BaseHandler.ts", "notation": "P:success,data,error,processingTime F:createOperationResult CB:none I:OperationResult DB:none", "block": "/**\n * F:createOperationResult - Create operation result with proper typing\n * @notation P:success,d..."}, {"name": "LogEntry", "file": "src\\core\\handlers\\CentralLoggingDispatcher.ts", "notation": "P:core/handlers/CentralLoggingDispatcher F:autoLog,logMCPCall CB:autoLog I:LogEntry,LogContext DB:logs", "block": "/**\n * Central Logging Dispatcher - AI-Optimized Pure Functions\n * Migrated from class-based to pure..."}, {"name": "LogEntry", "file": "src\\core\\handlers\\CentralLoggingDispatcher.ts", "notation": "P:none F:none CB:none I:LogEntry DB:logs", "block": "/**\n * @I:LogEntry - Log entry structure\n * @notation P:none F:none CB:none I:LogEntry DB:logs\n */..."}, {"name": "LogContext", "file": "src\\core\\handlers\\CentralLoggingDispatcher.ts", "notation": "P:none F:none CB:none I:LogContext DB:logs", "block": "/**\n * @I:LogContext - Log context information\n * @notation P:none F:none CB:none I:LogContext DB:lo..."}, {"name": "MCPCallLogData", "file": "src\\core\\handlers\\CentralLoggingDispatcher.ts", "notation": "P:none F:none CB:none I:MCPCallLogData DB:logs", "block": "/**\n * @I:MCPCallLogData - MCP call log data\n * @notation P:none F:none CB:none I:MCPCallLogData DB:..."}, {"name": "TraitLogData", "file": "src\\core\\handlers\\CentralLoggingDispatcher.ts", "notation": "P:none F:none CB:none I:TraitLogData DB:traits", "block": "/**\n * @I:TraitLogData - Trait log data\n * @notation P:none F:none CB:none I:TraitLogData DB:traits\n..."}, {"name": "FailureLogData", "file": "src\\core\\handlers\\CentralLoggingDispatcher.ts", "notation": "P:none F:none CB:none I:FailureLogData DB:failures", "block": "/**\n * @I:FailureLogData - Failure log data\n * @notation P:none F:none CB:none I:FailureLogData DB:f..."}, {"name": "LoggingState", "file": "src\\core\\handlers\\CentralLoggingDispatcher.ts", "notation": "P:none F:none CB:none I:LoggingState DB:logs", "block": "/**\n * @I:LoggingState - Logging state management\n * @notation P:none F:none CB:none I:LoggingState ..."}, {"name": "LoggingState", "file": "src\\core\\handlers\\CentralLoggingDispatcher.ts", "notation": "P:database,sessionId F:createLoggingState CB:none I:LoggingState DB:logs", "block": "/**\n * F:createLoggingState - Create initial logging state\n * @notation P:database,sessionId F:creat..."}, {"name": "string", "file": "src\\core\\handlers\\CentralLoggingDispatcher.ts", "notation": "P:none F:generateSessionId CB:none I:string DB:none", "block": "/**\n * F:generateSessionId - Generate unique session ID\n * @notation P:none F:generateSessionId CB:n..."}, {"name": "LoggingState", "file": "src\\core\\handlers\\CentralLoggingDispatcher.ts", "notation": "P:state,entry F:addToLogQueue CB:none I:LoggingState DB:logs", "block": "/**\n * F:addToLogQueue - Add entry to log queue\n * @notation P:state,entry F:addToLogQueue CB:none I..."}, {"name": "LoggingState", "file": "src\\core\\handlers\\CentralLoggingDispatcher.ts", "notation": "P:state F:clearLogQueue CB:none I:LoggingState DB:logs", "block": "/**\n * F:clearLogQueue - Clear log queue\n * @notation P:state F:clearLogQueue CB:none I:LoggingState..."}, {"name": "LoggingState", "file": "src\\core\\handlers\\CentralLoggingDispatcher.ts", "notation": "P:state,isProcessing F:setProcessingState CB:none I:LoggingState DB:logs", "block": "/**\n * F:setProcessingState - Set processing state\n * @notation P:state,isProcessing F:setProcessing..."}, {"name": "Promise", "file": "src\\core\\handlers\\CentralLoggingDispatcher.ts", "notation": "P:state,entry F:autoLog CB:autoLog I:Promise DB:logs", "block": "/**\n * F:autoLog - Auto-log based on entry type\n * @notation P:state,entry F:autoLog CB:autoLog I:Pr..."}, {"name": "Promise", "file": "src\\core\\handlers\\CentralLoggingDispatcher.ts", "notation": "P:state,data,context F:logMCPCall CB:logMCPCall I:Promise DB:logs", "block": "/**\n * F:logMCPCall - Log MCP call execution\n * @notation P:state,data,context F:logMCPCall CB:logMC..."}, {"name": "Promise", "file": "src\\core\\handlers\\CentralLoggingDispatcher.ts", "notation": "P:state,data,context F:logTrait CB:logTrait I:Promise DB:traits", "block": "/**\n * F:logTrait - Log trait discovery\n * @notation P:state,data,context F:logTrait CB:logTrait I:P..."}, {"name": "Promise", "file": "src\\core\\handlers\\CentralLoggingDispatcher.ts", "notation": "P:state,data,context F:logFailure CB:logFailure I:Promise DB:failures", "block": "/**\n * F:logFailure - Log failure event\n * @notation P:state,data,context F:logFailure CB:logFailure..."}, {"name": "Promise", "file": "src\\core\\handlers\\CentralLoggingDispatcher.ts", "notation": "P:state F:flushLogs CB:flushLogs I:Promise DB:logs", "block": "/**\n * F:flushLogs - Flush queued logs to database\n * @notation P:state F:flushLogs CB:flushLogs I:P..."}, {"name": "Promise", "file": "src\\core\\handlers\\CentralLoggingDispatcher.ts", "notation": "P:database,entries F:processBatch CB:processBatch I:Promise DB:logs", "block": "/**\n * F:processBatch - Process a batch of log entries\n * @notation P:database,entries F:processBatc..."}, {"name": "void", "file": "src\\core\\handlers\\CentralLoggingDispatcher.ts", "notation": "P:database,entry,callback F:insertLogEntry CB:insertLogEntry I:void DB:logs", "block": "/**\n * F:insertLogEntry - Insert single log entry into database\n * @notation P:database,entry,callba..."}, {"name": "CoordinationCommand", "file": "src\\core\\handlers\\coordination.ts", "notation": "P:core/handlers/coordination F:coordination<PERSON><PERSON><PERSON>,executeCoordinationCommand CB:executeCoordinationCommand I:CoordinationCommand,CoordinationResult DB:coordination", "block": "/**\n * Coordination Handler - AI-Optimized Pure Functions\n * Migrated from BaseHandler class to pure..."}, {"name": "HandlerConfig", "file": "src\\core\\handlers\\coordination.ts", "notation": "P:none F:createCoordinationConfig CB:none I:HandlerConfig DB:none", "block": "/**\n * F:createCoordinationConfig - Create coordination handler configuration\n * @notation P:none F:..."}, {"name": "boolean", "file": "src\\core\\handlers\\coordination.ts", "notation": "P:command F:validateCoordinationCommand CB:none I:boolean DB:none", "block": "/**\n * F:validateCoordinationCommand - Validate coordination command structure\n * @notation P:comman..."}, {"name": "string", "file": "src\\core\\handlers\\coordination.ts", "notation": "P:prefix F:generateId CB:none I:string DB:none", "block": "/**\n * F:generateId - Generate unique ID with prefix\n * @notation P:prefix F:generateId CB:none I:st..."}, {"name": "void", "file": "src\\core\\handlers\\coordination.ts", "notation": "P:agentId,operation F:validateAgent CB:none I:void DB:none", "block": "/**\n * F:validateAgent - Validate agent ID for operation\n * @notation P:agentId,operation F:validate..."}, {"name": "CoordinationResult", "file": "src\\core\\handlers\\coordination.ts", "notation": "P:none F:executeCoordinationDiscover CB:executeCoordinationDiscover I:CoordinationResult DB:coordination", "block": "/**\n * F:executeCoordinationDiscover - Execute coordination discover operation\n * @notation P:none F..."}, {"name": "CoordinationResult", "file": "src\\core\\handlers\\coordination.ts", "notation": "P:agentId,options F:executeCoordinationRegister CB:executeCoordinationRegister I:CoordinationResult DB:coordination", "block": "/**\n * F:executeCoordinationRegister - Execute coordination register operation\n * @notation P:agentI..."}, {"name": "CoordinationResult", "file": "src\\core\\handlers\\coordination.ts", "notation": "P:agentId F:executeCoordinationHeartbeat CB:executeCoordinationHeartbeat I:CoordinationResult DB:coordination", "block": "/**\n * F:executeCoordinationHeartbeat - Execute coordination heartbeat operation\n * @notation P:agen..."}, {"name": "CoordinationResult", "file": "src\\core\\handlers\\coordination.ts", "notation": "P:none F:executeCoordinationStatus CB:executeCoordinationStatus I:CoordinationResult DB:coordination", "block": "/**\n * F:executeCoordinationStatus - Execute coordination status operation\n * @notation P:none F:exe..."}, {"name": "OperationResult", "file": "src\\core\\handlers\\coordination.ts", "notation": "P:command F:executeCoordinationCommand CB:executeCoordinationCommand I:OperationResult DB:coordination", "block": "/**\n * F:executeCoordinationCommand - Execute coordination command with resilience\n * @notation P:co..."}, {"name": "CoordinationResult", "file": "src\\core\\handlers\\coordination.ts", "notation": "P:templateSource,vars,engine F:executeCoordinationTemplate CB:executeCoordinationTemplate I:CoordinationResult DB:none", "block": "/**\n * F:executeCoordinationTemplate - Execute coordination template operation\n * @notation P:templa..."}, {"name": "function", "file": "src\\core\\handlers\\coordination.ts", "notation": "P:db F:coordinationHandler CB:none I:function DB:coordination", "block": "/**\n * F:coordinationH<PERSON>ler - Create coordination handler function\n * @notation P:db F:coordination..."}, {"name": "DatabaseCommand", "file": "src\\core\\handlers\\database.ts", "notation": "P:core/handlers/database F:database<PERSON><PERSON><PERSON>,executeDatabaseCommand CB:executeDatabaseCommand I:DatabaseCommand,DatabaseResult DB:database", "block": "/**\n * Database Handler - AI-Optimized Pure Functions\n * Migrated from BaseHandler class to pure fun..."}, {"name": "string", "file": "src\\core\\handlers\\database.ts", "notation": "P:none F:none CB:none I:string DB:none", "block": "/**\n * F:SCHEMA_QUERY_SQL - SQL query for schema inspection\n * @notation P:none F:none CB:none I:str..."}, {"name": "HandlerConfig", "file": "src\\core\\handlers\\database.ts", "notation": "P:none F:createDatabaseConfig CB:none I:HandlerConfig DB:none", "block": "/**\n * F:createDatabaseConfig - Create database handler configuration\n * @notation P:none F:createDa..."}, {"name": "boolean", "file": "src\\core\\handlers\\database.ts", "notation": "P:command F:validateDatabaseCommand CB:none I:boolean DB:none", "block": "/**\n * F:validateDatabaseCommand - Validate database command structure\n * @notation P:command F:vali..."}, {"name": "object", "file": "src\\core\\handlers\\database.ts", "notation": "P:dbPath F:validateDatabasePath CB:none I:object DB:none", "block": "/**\n * F:validateDatabasePath - Validate database path\n * @notation P:dbPath F:validateDatabasePath ..."}, {"name": "object", "file": "src\\core\\handlers\\database.ts", "notation": "P:dbPath,mode F:getDatabaseConnection CB:getDatabaseConnection I:object DB:database", "block": "/**\n * F:getDatabaseConnection - Get or create database connection\n * @notation P:dbPath,mode F:getD..."}, {"name": "DatabaseResult", "file": "src\\core\\handlers\\database.ts", "notation": "P:db<PERSON><PERSON>,options F:executeDatabaseConnect CB:executeDatabaseConnect I:DatabaseResult DB:database", "block": "/**\n * F:executeDatabaseConnect - Execute database connect operation\n * @notation P:dbPath,options F..."}, {"name": "DatabaseResult", "file": "src\\core\\handlers\\database.ts", "notation": "P:db<PERSON><PERSON>,sql,options F:executeDatabaseExecute CB:executeDatabaseExecute I:DatabaseResult DB:database", "block": "/**\n * F:executeDatabaseExecute - Execute database execute operation (simplified for AI-optimization..."}, {"name": "DatabaseResult", "file": "src\\core\\handlers\\database.ts", "notation": "P:db<PERSON><PERSON>,sql,options F:executeDatabaseQuery CB:executeDatabaseQuery I:DatabaseResult DB:database", "block": "/**\n * F:executeDatabaseQuery - Execute database query operation (simplified for AI-optimization)\n *..."}, {"name": "DatabaseResult", "file": "src\\core\\handlers\\database.ts", "notation": "P:db<PERSON><PERSON>,options F:executeDatabaseSchema CB:executeDatabaseSchema I:DatabaseResult DB:database", "block": "/**\n * F:executeDatabaseSchema - Execute database schema operation (simplified for AI-optimization)\n..."}, {"name": "DatabaseResult", "file": "src\\core\\handlers\\database.ts", "notation": "P:db<PERSON><PERSON>,options F:executeDatabaseBackup CB:executeDatabaseBackup I:DatabaseResult DB:database", "block": "/**\n * F:executeDatabaseBackup - Execute database backup operation (simplified for AI-optimization)\n..."}, {"name": "DatabaseResult", "file": "src\\core\\handlers\\database.ts", "notation": "P:db<PERSON><PERSON>,options F:executeDatabaseMigrate CB:executeDatabaseMigrate I:DatabaseResult DB:database", "block": "/**\n * F:executeDatabaseMigrate - Execute database migrate operation (simplified for AI-optimization..."}, {"name": "OperationResult", "file": "src\\core\\handlers\\database.ts", "notation": "P:command F:executeDatabaseCommand CB:executeDatabaseCommand I:OperationResult DB:database", "block": "/**\n * F:executeDatabaseCommand - Execute database command with resilience\n * @notation P:command F:..."}, {"name": "DatabaseResult", "file": "src\\core\\handlers\\database.ts", "notation": "P:templateSource,vars,engine F:executeDatabaseTemplate CB:executeDatabaseTemplate I:DatabaseResult DB:none", "block": "/**\n * F:executeDatabaseTemplate - Execute database template operation\n * @notation P:templateSource..."}, {"name": "function", "file": "src\\core\\handlers\\database.ts", "notation": "P:none F:databaseHandler CB:none I:function DB:database", "block": "/**\n * F:databaseHandler - Create database handler function\n * @notation P:none F:databaseHandler CB..."}, {"name": "EnhancedToolCommand", "file": "src\\core\\handlers\\enhancedTool.ts", "notation": "P:core/handlers/enhancedTool F:executeEnhancedTool,createEnhancedToolConfig CB:executeEnhancedTool I:EnhancedToolCommand,EnhancedToolResult DB:enhanced_tools", "block": "/**\n * Enhanced <PERSON><PERSON> Handler - AI-Optimized Pure Functions\n * Migrated from class-based to pure func..."}, {"name": "EnhancedToolCommand", "file": "src\\core\\handlers\\enhancedTool.ts", "notation": "P:none F:none CB:none I:EnhancedToolCommand DB:enhanced_tools", "block": "/**\n * @I:EnhancedToolCommand - Enhanced tool command structure\n * @notation P:none F:none CB:none I..."}, {"name": "EnhancedToolResult", "file": "src\\core\\handlers\\enhancedTool.ts", "notation": "P:none F:none CB:none I:EnhancedToolResult DB:enhanced_tools", "block": "/**\n * @I:EnhancedToolResult - Enhanced tool result structure\n * @notation P:none F:none CB:none I:E..."}, {"name": "ParsedNotation", "file": "src\\core\\handlers\\enhancedTool.ts", "notation": "P:none F:none CB:none I:ParsedNotation DB:none", "block": "/**\n * @I:ParsedNotation - Parsed enhanced notation structure\n * @notation P:none F:none CB:none I:P..."}, {"name": "HandlerConfig", "file": "src\\core\\handlers\\enhancedTool.ts", "notation": "P:none F:createEnhancedToolConfig CB:none I:HandlerConfig DB:none", "block": "/**\n * F:createEnhancedToolConfig - Create enhanced tool handler configuration\n * @notation P:none F..."}, {"name": "EnhancedToolResult", "file": "src\\core\\handlers\\enhancedTool.ts", "notation": "P:command F:executeEnhancedToolBA CB:executeEnhancedToolBA I:EnhancedToolResult DB:enhanced_tools", "block": "/**\n * F:executeEnhancedToolBA - Execute BA (Batch Analysis) operation\n * @notation P:command F:exec..."}, {"name": "EnhancedToolResult", "file": "src\\core\\handlers\\enhancedTool.ts", "notation": "P:command F:executeEnhancedToolTemplate CB:executeEnhancedToolTemplate I:EnhancedToolResult DB:templates", "block": "/**\n * F:executeEnhancedToolTemplate - Execute template processing operation\n * @notation P:command ..."}, {"name": "EnhancedToolResult", "file": "src\\core\\handlers\\enhancedTool.ts", "notation": "P:command F:executeEnhancedToolTS CB:executeEnhancedToolTS I:EnhancedToolResult DB:enhanced_tools", "block": "/**\n * F:executeEnhancedToolTS - Execute TS (TypeScript) operation\n * @notation P:command F:executeE..."}, {"name": "EnhancedToolResult", "file": "src\\core\\handlers\\enhancedTool.ts", "notation": "P:command F:executeEnhancedToolDB CB:executeEnhancedToolDB I:EnhancedToolResult DB:enhanced_tools", "block": "/**\n * F:executeEnhancedToolDB - Execute DB (Database) operation\n * @notation P:command F:executeEnh..."}, {"name": "ParsedNotation", "file": "src\\core\\handlers\\enhancedTool.ts", "notation": "P:notation F:parseEnhancedNotation CB:none I:ParsedNotation DB:none", "block": "/**\n * F:parseEnhancedNotation - Parse enhanced notation string\n * @notation P:notation F:parseEnhan..."}, {"name": "boolean", "file": "src\\core\\handlers\\enhancedTool.ts", "notation": "P:input F:validateEnhancedToolCommand CB:none I:boolean DB:none", "block": "/**\n * F:validateEnhancedToolCommand - Validate enhanced tool command structure\n * @notation P:input..."}, {"name": "OperationResult", "file": "src\\core\\handlers\\enhancedTool.ts", "notation": "P:command F:executeEnhancedToolCommand CB:executeEnhancedToolCommand I:OperationResult DB:enhanced_tools", "block": "/**\n * F:executeEnhancedToolCommand - Execute enhanced tool command with resilience\n * @notation P:c..."}, {"name": "function", "file": "src\\core\\handlers\\enhancedTool.ts", "notation": "P:database F:enhancedToolHandler CB:none I:function DB:enhanced_tools", "block": "/**\n * F:enhancedToolHandler - Create enhanced tool handler function\n * @notation P:database F:enhan..."}, {"name": "object", "file": "src\\core\\handlers\\enhancedTool.ts", "notation": "P:database F:createEnhancedToolHandler CB:none I:object DB:enhanced_tools", "block": "/**\n * F:createEnhancedToolHandler - Create enhanced tool handler (compatibility function)\n * @notat..."}, {"name": "HandlerConfig", "file": "src\\core\\handlers\\executeCommand.ts", "notation": "P:core/handlers/executeCommand F:executeCommand,createHandlerConfig,executeWithResilience CB:executeCommand I:HandlerConfig,OperationResult DB:none", "block": "/**\n * Command Executor - AI-Optimized Pure Handler Functions\n * Pure functions for executing comman..."}, {"name": "HandlerConfig", "file": "src\\core\\handlers\\executeCommand.ts", "notation": "P:name,operations,options F:createHandlerConfig CB:none I:HandlerConfig DB:none", "block": "/**\n * F:createHandlerConfig - Create handler configuration for operations\n * @notation P:name,opera..."}, {"name": "OperationResult", "file": "src\\core\\handlers\\executeCommand.ts", "notation": "P:command,config,implementation F:executeWithResilience CB:executeWithResilience I:OperationResult DB:none", "block": "/**\n * F:executeWithResilience - Execute command with circuit breaker and retry protection\n * @notat..."}, {"name": "HandlerFunction", "file": "src\\core\\handlers\\executeCommand.ts", "notation": "P:config,implementation F:createHandler CB:none I:HandlerFunction DB:none", "block": "/**\n * F:createH<PERSON>ler - Create a handler function with resilience patterns\n * @notation P:config,im..."}, {"name": "HandlerConfig", "file": "src\\core\\handlers\\executeCommand.ts", "notation": "P:none F:createMemoryHandlerConfig CB:none I:HandlerConfig DB:none", "block": "/**\n * F:createMemoryHandlerConfig - Create memory handler configuration\n * @notation P:none F:creat..."}, {"name": "HandlerConfig", "file": "src\\core\\handlers\\executeCommand.ts", "notation": "P:none F:createFileHandlerConfig CB:none I:HandlerConfig DB:none", "block": "/**\n * F:createFileHandlerConfig - Create file handler configuration\n * @notation P:none F:createFil..."}, {"name": "HandlerConfig", "file": "src\\core\\handlers\\executeCommand.ts", "notation": "P:none F:createGitHubHandlerConfig CB:none I:HandlerConfig DB:none", "block": "/**\n * F:createGitHubHandlerConfig - Create GitHub handler configuration\n * @notation P:none F:creat..."}, {"name": "HandlerConfig", "file": "src\\core\\handlers\\executeCommand.ts", "notation": "P:none F:createDatabaseHandlerConfig CB:none I:HandlerConfig DB:none", "block": "/**\n * F:createDatabaseHandlerConfig - Create database handler configuration\n * @notation P:none F:c..."}, {"name": "HandlerConfig", "file": "src\\core\\handlers\\executeCommand.ts", "notation": "P:none F:createMonitoringHandlerConfig CB:none I:HandlerConfig DB:none", "block": "/**\n * F:createMonitoringHandlerConfig - Create monitoring handler configuration\n * @notation P:none..."}, {"name": "OperationResult", "file": "src\\core\\handlers\\executeCommand.ts", "notation": "P:handler<PERSON><PERSON>,command,handlers,config F:executeCommand CB:executeCommand I:OperationResult DB:none", "block": "/**\n * F:executeCommand - Execute a command with the appropriate handler\n * @notation P:handlerName,..."}, {"name": "ReadonlyMap", "file": "src\\core\\handlers\\executeCommand.ts", "notation": "P:handlers F:createHandlerRegistry CB:none I:ReadonlyMap DB:none", "block": "/**\n * F:createHandlerRegistry - Create a registry of handlers\n * @notation P:handlers F:createHandl..."}, {"name": "boolean", "file": "src\\core\\handlers\\executeCommand.ts", "notation": "P:command,schema F:validateCommand CB:none I:boolean DB:none", "block": "/**\n * F:validateCommand - Validate command structure\n * @notation P:command,schema F:validateComman..."}, {"name": "function", "file": "src\\core\\handlers\\executeCommand.ts", "notation": "P:schema F:createCommandValidator CB:none I:function DB:none", "block": "/**\n * F:createCommandValidator - Create a command validator function\n * @notation P:schema F:create..."}, {"name": "FetchCommand", "file": "src\\core\\handlers\\fetch.ts", "notation": "P:core/handlers/fetch F:fetch<PERSON><PERSON><PERSON>,executeFetchCommand CB:executeFetchCommand I:FetchCommand,FetchResult DB:fetch", "block": "/**\n * <PERSON><PERSON> Handler - AI-Optimized Pure Functions\n * Migrated from BaseHandler class to pure functi..."}, {"name": "HandlerConfig", "file": "src\\core\\handlers\\fetch.ts", "notation": "P:none F:createFetchConfig CB:none I:HandlerConfig DB:none", "block": "/**\n * F:createFetchConfig - Create fetch handler configuration\n * @notation P:none F:createFetchCon..."}, {"name": "boolean", "file": "src\\core\\handlers\\fetch.ts", "notation": "P:command F:validateFetchCommand CB:none I:boolean DB:none", "block": "/**\n * F:validateFetchCommand - Validate fetch command structure\n * @notation P:command F:validateFe..."}, {"name": "FetchResult", "file": "src\\core\\handlers\\fetch.ts", "notation": "P:url,options F:executeFetchOperation CB:executeFetchOperation I:FetchResult DB:fetch", "block": "/**\n * F:executeFetchOperation - Execute HTTP fetch operation\n * @notation P:url,options F:executeFe..."}, {"name": "FetchResult", "file": "src\\core\\handlers\\fetch.ts", "notation": "P:url,options F:executeFetchText CB:executeFetchText I:FetchResult DB:fetch", "block": "/**\n * F:executeFetchText - Execute fetch with text content parsing\n * @notation P:url,options F:exe..."}, {"name": "FetchResult", "file": "src\\core\\handlers\\fetch.ts", "notation": "P:url,options F:execute<PERSON>etchJson CB:executeFetchJson I:FetchResult DB:fetch", "block": "/**\n * F:executeFetchJson - Execute fetch with JSON parsing\n * @notation P:url,options F:executeFetc..."}, {"name": "FetchResult", "file": "src\\core\\handlers\\fetch.ts", "notation": "P:templateSource,vars,engine F:executeFetchTemplate CB:executeFetchTemplate I:FetchResult DB:none", "block": "/**\n * F:executeFetchTemplate - Execute fetch template operation\n * @notation P:templateSource,vars,..."}, {"name": "OperationResult", "file": "src\\core\\handlers\\fetch.ts", "notation": "P:command F:executeFetchCommand CB:executeFetchCommand I:OperationResult DB:fetch", "block": "/**\n * F:executeFetchCommand - Execute fetch command with resilience\n * @notation P:command F:execut..."}, {"name": "function", "file": "src\\core\\handlers\\fetch.ts", "notation": "P:db F:fetchHandler CB:none I:function DB:fetch", "block": "/**\n * F:fetchHandler - Create fetch handler function\n * @notation P:db F:fetchHandler CB:none I:fun..."}, {"name": "FileCommand", "file": "src\\core\\handlers\\file.ts", "notation": "P:core/handlers/file F:fileHand<PERSON>,executeFileCommand CB:executeFileCommand I:FileCommand,FileResult DB:file", "block": "/**\n * File Handler - AI-Optimized Pure Functions\n * Migrated from BaseHandler class to pure functio..."}, {"name": "HandlerConfig", "file": "src\\core\\handlers\\file.ts", "notation": "P:none F:createFileConfig CB:none I:HandlerConfig DB:none", "block": "/**\n * F:createFileConfig - Create file handler configuration\n * @notation P:none F:createFileConfig..."}, {"name": "boolean", "file": "src\\core\\handlers\\file.ts", "notation": "P:command F:validateFileCommand CB:none I:boolean DB:none", "block": "/**\n * F:validateFileCommand - Validate file command structure\n * @notation P:command F:validateFile..."}, {"name": "string", "file": "src\\core\\handlers\\file.ts", "notation": "P:filePath F:validateWorkspacePath CB:none I:string DB:none", "block": "/**\n * F:validateWorkspacePath - Validate and resolve workspace path\n * @notation P:filePath F:valid..."}, {"name": "string", "file": "src\\core\\handlers\\file.ts", "notation": "P:filePath F:getFileExtension CB:none I:string DB:none", "block": "/**\n * F:getFileExtension - Get file extension\n * @notation P:filePath F:getFileExtension CB:none I:..."}, {"name": "string", "file": "src\\core\\handlers\\file.ts", "notation": "P:filePath F:getLanguageFromExtension CB:none I:string DB:none", "block": "/**\n * F:getLanguageFromExtension - Get programming language from file extension\n * @notation P:file..."}, {"name": "void", "file": "src\\core\\handlers\\file.ts", "notation": "P:absolutePath,content,encoding F:writeFileWithEncoding CB:writeFileWithEncoding I:void DB:file", "block": "/**\n * F:writeFileWithEncoding - Write file with specific encoding\n * @notation P:absolutePath,conte..."}, {"name": "FileResult", "file": "src\\core\\handlers\\file.ts", "notation": "P:filePath,encoding F:executeFileRead CB:executeFileRead I:FileResult DB:file", "block": "/**\n * F:executeFileRead - Execute file read operation\n * @notation P:filePath,encoding F:executeFil..."}, {"name": "FileResult", "file": "src\\core\\handlers\\file.ts", "notation": "P:filePath,content,encoding,atomic F:executeFileWrite CB:executeFileWrite I:FileResult DB:file", "block": "/**\n * F:executeFileWrite - Execute file write operation\n * @notation P:filePath,content,encoding,at..."}, {"name": "FileResult", "file": "src\\core\\handlers\\file.ts", "notation": "P:filePath F:executeFileExists CB:executeFileExists I:FileResult DB:file", "block": "/**\n * F:executeFileExists - Execute file exists check operation\n * @notation P:filePath F:executeFi..."}, {"name": "FileResult", "file": "src\\core\\handlers\\file.ts", "notation": "P:dirPath,recursive,filter,maxDepth F:executeFileList CB:executeFileList I:FileResult DB:file", "block": "/**\n * F:executeFileList - Execute file list operation (simplified for AI-optimization)\n * @notation..."}, {"name": "FileResult", "file": "src\\core\\handlers\\file.ts", "notation": "P:searchPath,searchPattern,options F:executeFileSearch CB:executeFileSearch I:FileResult DB:file", "block": "/**\n * F:executeFileSearch - Execute file search operation (simplified for AI-optimization)\n * @nota..."}, {"name": "FileResult", "file": "src\\core\\handlers\\file.ts", "notation": "P:filePath,metrics,language F:executeFileAnalyze CB:executeFileAnalyze I:FileResult DB:file", "block": "/**\n * F:executeFileAnalyze - Execute file analyze operation (simplified for AI-optimization)\n * @no..."}, {"name": "FileResult", "file": "src\\core\\handlers\\file.ts", "notation": "P:filePath,options F:executeFileBackup CB:executeFileBackup I:FileResult DB:file", "block": "/**\n * F:executeFileBackup - Execute file backup operation (simplified for AI-optimization)\n * @nota..."}, {"name": "FileResult", "file": "src\\core\\handlers\\file.ts", "notation": "P:templateSource,vars,engine F:executeFileTemplate CB:executeFileTemplate I:FileResult DB:none", "block": "/**\n * F:executeFileTemplate - Execute file template operation\n * @notation P:templateSource,vars,en..."}, {"name": "OperationResult", "file": "src\\core\\handlers\\file.ts", "notation": "P:command F:executeFileCommand CB:executeFileCommand I:OperationResult DB:file", "block": "/**\n * F:executeFileCommand - Execute file command with resilience\n * @notation P:command F:executeF..."}, {"name": "function", "file": "src\\core\\handlers\\file.ts", "notation": "P:db F:fileHandler CB:none I:function DB:file", "block": "/**\n * F:fileHandler - Create file handler function\n * @notation P:db F:fileHandler CB:none I:functi..."}, {"name": "GitCommand", "file": "src\\core\\handlers\\git.ts", "notation": "P:core/handlers/git F:git<PERSON><PERSON><PERSON>,executeGitCommand CB:executeGitCommand I:GitCommand,GitResult DB:git", "block": "/**\n * <PERSON><PERSON>ler - AI-Optimized Pure Functions\n * Migrated from BaseHandler class to pure function..."}, {"name": "HandlerConfig", "file": "src\\core\\handlers\\git.ts", "notation": "P:none F:createGitConfig CB:none I:HandlerConfig DB:none", "block": "/**\n * F:createGitConfig - Create git handler configuration\n * @notation P:none F:createGitConfig CB..."}, {"name": "boolean", "file": "src\\core\\handlers\\git.ts", "notation": "P:command F:validateGitCommand CB:none I:boolean DB:none", "block": "/**\n * F:validateGitCommand - Validate git command structure\n * @notation P:command F:validateGitCom..."}, {"name": "GitResult", "file": "src\\core\\handlers\\git.ts", "notation": "P:command,repository,args F:executeGitOperation CB:executeGitOperation I:GitResult DB:git", "block": "/**\n * F:executeGitOperation - Execute Git command safely with security validation\n * @notation P:co..."}, {"name": "GitResult", "file": "src\\core\\handlers\\git.ts", "notation": "P:repository F:executeGitStatus CB:executeGitStatus I:GitResult DB:git", "block": "/**\n * F:executeGitStatus - Get repository status\n * @notation P:repository F:executeGitStatus CB:ex..."}, {"name": "GitResult", "file": "src\\core\\handlers\\git.ts", "notation": "P:repository,limit F:executeGitLog CB:executeGitLog I:GitResult DB:git", "block": "/**\n * F:executeGitLog - Get commit log\n * @notation P:repository,limit F:executeGitLog CB:executeGi..."}, {"name": "GitResult", "file": "src\\core\\handlers\\git.ts", "notation": "P:repository,files F:executeGitDiff CB:executeGitDiff I:GitResult DB:git", "block": "/**\n * F:executeGitDiff - Get diff\n * @notation P:repository,files F:executeGitDiff CB:executeGitDif..."}, {"name": "GitResult", "file": "src\\core\\handlers\\git.ts", "notation": "P:repository,all F:executeGitBranch CB:executeGitBranch I:GitResult DB:git", "block": "/**\n * F:executeGitBranch - List branches\n * @notation P:repository,all F:executeGitBranch CB:execut..."}, {"name": "GitResult", "file": "src\\core\\handlers\\git.ts", "notation": "P:branch,repository,create F:executeGitCheckout CB:executeGitCheckout I:GitResult DB:git", "block": "/**\n * F:executeGitCheckout - Checkout branch\n * @notation P:branch,repository,create F:executeGitCh..."}, {"name": "GitResult", "file": "src\\core\\handlers\\git.ts", "notation": "P:message,repository,files F:executeGitCommit CB:executeGitCommit I:GitResult DB:git", "block": "/**\n * F:executeGitCommit - Commit changes\n * @notation P:message,repository,files F:executeGitCommi..."}, {"name": "GitResult", "file": "src\\core\\handlers\\git.ts", "notation": "P:repository,remote,branch F:executeGitPush CB:executeGitPush I:GitResult DB:git", "block": "/**\n * F:executeGitPush - Push changes\n * @notation P:repository,remote,branch F:executeGitPush CB:e..."}, {"name": "GitResult", "file": "src\\core\\handlers\\git.ts", "notation": "P:repository,remote,branch F:executeGitPull CB:executeGitPull I:GitResult DB:git", "block": "/**\n * F:executeGitPull - Pull changes\n * @notation P:repository,remote,branch F:executeGitPull CB:e..."}, {"name": "GitResult", "file": "src\\core\\handlers\\git.ts", "notation": "P:repository F:executeGitRemote CB:executeGitRemote I:GitResult DB:git", "block": "/**\n * F:executeGitRemote - Get remote information\n * @notation P:repository F:executeGitRemote CB:e..."}, {"name": "GitResult", "file": "src\\core\\handlers\\git.ts", "notation": "P:files,repository F:executeGitAdd CB:executeGitAdd I:GitResult DB:git", "block": "/**\n * F:executeGitAdd - Add files to staging\n * @notation P:files,repository F:executeGitAdd CB:exe..."}, {"name": "GitResult", "file": "src\\core\\handlers\\git.ts", "notation": "P:repository,files,hard F:executeGitReset CB:executeGitReset I:GitResult DB:git", "block": "/**\n * F:executeGitReset - Reset changes\n * @notation P:repository,files,hard F:executeGitReset CB:e..."}, {"name": "OperationResult", "file": "src\\core\\handlers\\git.ts", "notation": "P:command F:executeGitCommand CB:executeGitCommand I:OperationResult DB:git", "block": "/**\n * F:executeGitCommand - Execute git command with resilience\n * @notation P:command F:executeGit..."}, {"name": "GitResult", "file": "src\\core\\handlers\\git.ts", "notation": "P:templateSource,vars,engine F:executeGitTemplate CB:executeGitTemplate I:GitResult DB:none", "block": "/**\n * F:executeGitTemplate - Execute git template operation\n * @notation P:templateSource,vars,engi..."}, {"name": "function", "file": "src\\core\\handlers\\git.ts", "notation": "P:db F:gitHandler CB:none I:function DB:git", "block": "/**\n * F:gitHandler - Create git handler function\n * @notation P:db F:gitHandler CB:none I:function ..."}, {"name": "GitHubCommand", "file": "src\\core\\handlers\\github.ts", "notation": "P:core/handlers/github F:gith<PERSON><PERSON><PERSON><PERSON>,executeGitHubCommand CB:executeGitHubCommand I:GitHubCommand,GitHubResult DB:github", "block": "/**\n * GitHub Handler - AI-Optimized Pure Functions\n * Migrated from BaseHandler class to pure funct..."}, {"name": "HandlerConfig", "file": "src\\core\\handlers\\github.ts", "notation": "P:none F:createGitHubConfig CB:none I:HandlerConfig DB:none", "block": "/**\n * F:createGitHubConfig - Create GitHub handler configuration\n * @notation P:none F:createGitHub..."}, {"name": "boolean", "file": "src\\core\\handlers\\github.ts", "notation": "P:command F:validateGitHubCommand CB:none I:boolean DB:none", "block": "/**\n * F:validateGitHubCommand - Validate GitHub command structure\n * @notation P:command F:validate..."}, {"name": "OperationResult", "file": "src\\core\\handlers\\github.ts", "notation": "P:command F:executeGitHubCommand CB:executeGitHubCommand I:OperationResult DB:github", "block": "/**\n * F:executeGitHubCommand - Execute GitHub command with resilience\n * @notation P:command F:exec..."}, {"name": "GitHubResult", "file": "src\\core\\handlers\\github.ts", "notation": "P:templateSource,vars,engine F:executeGitHubTemplate CB:executeGitHubTemplate I:GitHubResult DB:none", "block": "/**\n * F:executeGitHubTemplate - Execute GitHub template operation\n * @notation P:templateSource,var..."}, {"name": "function", "file": "src\\core\\handlers\\github.ts", "notation": "P:db F:githubHandler CB:none I:function DB:github", "block": "/**\n * F:githubHandler - Create GitHub handler function\n * @notation P:db F:githubHandler CB:none I:..."}, {"name": "all", "file": "src\\core\\handlers\\index.ts", "notation": "P:core/handlers/index F:all CB:none I:all DB:none", "block": "/**\n * Core Handlers - AI-Optimized Pure Function Exports\n * Machine-only exports for AI consumption..."}, {"name": "MemoryCommand", "file": "src\\core\\handlers\\memory.ts", "notation": "P:core/handlers/memory F:memory<PERSON><PERSON><PERSON>,executeMemoryCommand CB:executeMemoryCommand I:MemoryCommand,MemoryResult DB:memory", "block": "/**\n * Memory Handler - AI-Optimized Pure Functions\n * Migrated from BaseHandler class to pure funct..."}, {"name": "HandlerConfig", "file": "src\\core\\handlers\\memory.ts", "notation": "P:none F:createMemoryConfig CB:none I:HandlerConfig DB:none", "block": "/**\n * F:createMemoryConfig - Create memory handler configuration\n * @notation P:none F:createMemory..."}, {"name": "boolean", "file": "src\\core\\handlers\\memory.ts", "notation": "P:command F:validateMemoryCommand CB:none I:boolean DB:none", "block": "/**\n * F:validateMemoryCommand - Validate memory command structure\n * @notation P:command F:validate..."}, {"name": "MemoryResult", "file": "src\\core\\handlers\\memory.ts", "notation": "P:db,table,data F:executeMemoryInsert CB:executeMemoryInsert I:MemoryResult DB:memory", "block": "/**\n * F:executeMemoryInsert - Execute memory insert operation\n * @notation P:db,table,data F:execut..."}, {"name": "MemoryResult", "file": "src\\core\\handlers\\memory.ts", "notation": "P:db,table,filters,limit F:executeMemoryQuery CB:executeMemoryQuery I:MemoryResult DB:memory", "block": "/**\n * F:executeMemoryQuery - Execute memory query operation\n * @notation P:db,table,filters,limit F..."}, {"name": "MemoryResult", "file": "src\\core\\handlers\\memory.ts", "notation": "P:db,table,data,filters F:executeMemoryUpdate CB:executeMemoryUpdate I:MemoryResult DB:memory", "block": "/**\n * F:executeMemoryUpdate - Execute memory update operation\n * @notation P:db,table,data,filters ..."}, {"name": "MemoryResult", "file": "src\\core\\handlers\\memory.ts", "notation": "P:templateSource,vars,engine F:executeMemoryTemplate CB:executeMemoryTemplate I:MemoryResult DB:none", "block": "/**\n * F:executeMemoryTemplate - Execute memory template operation\n * @notation P:templateSource,var..."}, {"name": "OperationResult", "file": "src\\core\\handlers\\memory.ts", "notation": "P:db,command F:executeMemoryCommand CB:executeMemoryCommand I:OperationResult DB:memory", "block": "/**\n * F:executeMemoryCommand - Execute memory command with resilience\n * @notation P:db,command F:e..."}, {"name": "function", "file": "src\\core\\handlers\\memory.ts", "notation": "P:db F:memoryHandler CB:none I:function DB:memory", "block": "/**\n * F:memoryHandler - Create memory handler function\n * @notation P:db F:memoryHandler CB:none I:..."}, {"name": "MonitoringCommand", "file": "src\\core\\handlers\\monitoring.ts", "notation": "P:core/handlers/monitoring F:monitoring<PERSON><PERSON><PERSON>,executeMonitoringCommand CB:executeMonitoringCommand I:MonitoringCommand,MonitoringResult DB:monitoring", "block": "/**\n * Monitoring Handler - AI-Optimized Pure Functions\n * Migrated from BaseHandler class to pure f..."}, {"name": "HandlerConfig", "file": "src\\core\\handlers\\monitoring.ts", "notation": "P:none F:createMonitoringConfig CB:none I:HandlerConfig DB:none", "block": "/**\n * F:createMonitoringConfig - Create monitoring handler configuration\n * @notation P:none F:crea..."}, {"name": "boolean", "file": "src\\core\\handlers\\monitoring.ts", "notation": "P:command F:validateMonitoringCommand CB:none I:boolean DB:none", "block": "/**\n * F:validateMonitoringCommand - Validate monitoring command structure\n * @notation P:command F:..."}, {"name": "MonitoringResult", "file": "src\\core\\handlers\\monitoring.ts", "notation": "P:options F:executeMonitoringMetrics CB:executeMonitoringMetrics I:MonitoringResult DB:monitoring", "block": "/**\n * F:executeMonitoringMetrics - Execute monitoring metrics operation\n * @notation P:options F:ex..."}, {"name": "MonitoringResult", "file": "src\\core\\handlers\\monitoring.ts", "notation": "P:options F:executeMonitoringHealth CB:executeMonitoringHealth I:MonitoringResult DB:monitoring", "block": "/**\n * F:executeMonitoringHealth - Execute monitoring health operation\n * @notation P:options F:exec..."}, {"name": "MonitoringResult", "file": "src\\core\\handlers\\monitoring.ts", "notation": "P:options F:executeMonitoringDashboard CB:executeMonitoringDashboard I:MonitoringResult DB:monitoring", "block": "/**\n * F:executeMonitoringDashboard - Execute monitoring dashboard operation\n * @notation P:options ..."}, {"name": "OperationResult", "file": "src\\core\\handlers\\monitoring.ts", "notation": "P:command F:executeMonitoringCommand CB:executeMonitoringCommand I:OperationResult DB:monitoring", "block": "/**\n * F:executeMonitoringCommand - Execute monitoring command with resilience\n * @notation P:comman..."}, {"name": "MonitoringResult", "file": "src\\core\\handlers\\monitoring.ts", "notation": "P:templateSource,vars,engine F:executeMonitoringTemplate CB:executeMonitoringTemplate I:MonitoringResult DB:none", "block": "/**\n * F:executeMonitoringTemplate - Execute monitoring template operation\n * @notation P:templateSo..."}, {"name": "function", "file": "src\\core\\handlers\\monitoring.ts", "notation": "P:db F:monitoringHandler CB:none I:function DB:monitoring", "block": "/**\n * F:monitoringHandler - Create monitoring handler function\n * @notation P:db F:monitoringHandle..."}, {"name": "TerminalCommand", "file": "src\\core\\handlers\\terminal.ts", "notation": "P:core/handlers/terminal F:terminal<PERSON><PERSON><PERSON>,executeTerminalCommand CB:executeTerminalCommand I:TerminalCommand,TerminalResult DB:terminal", "block": "/**\n * Terminal Handler - AI-Optimized Pure Functions\n * Migrated from BaseHandler class to pure fun..."}, {"name": "HandlerConfig", "file": "src\\core\\handlers\\terminal.ts", "notation": "P:none F:createTerminalConfig CB:none I:HandlerConfig DB:none", "block": "/**\n * F:createTerminalConfig - Create terminal handler configuration\n * @notation P:none F:createTe..."}, {"name": "boolean", "file": "src\\core\\handlers\\terminal.ts", "notation": "P:command F:validateTerminalCommand CB:none I:boolean DB:none", "block": "/**\n * F:validateTerminalCommand - Validate terminal command structure\n * @notation P:command F:vali..."}, {"name": "object", "file": "src\\core\\handlers\\terminal.ts", "notation": "P:command F:validateTerminalCommandSecurity CB:validateTerminalCommandSecurity I:object DB:terminal", "block": "/**\n * F:validateTerminalCommandSecurity - Validate command for security using enhanced CommandSanit..."}, {"name": "object", "file": "src\\core\\handlers\\terminal.ts", "notation": "P:workingDir F:validateWorkingDirectory CB:none I:object DB:none", "block": "/**\n * F:validateWorkingDirectory - Validate working directory\n * @notation P:workingDir F:validateW..."}, {"name": "TerminalResult", "file": "src\\core\\handlers\\terminal.ts", "notation": "P:command,workingDirectory,options F:executeTerminalCommandSafely CB:executeTerminalCommandSafely I:TerminalResult DB:terminal", "block": "/**\n * F:executeTerminalCommandSafely - Execute terminal command safely\n * @notation P:command,worki..."}, {"name": "TerminalResult", "file": "src\\core\\handlers\\terminal.ts", "notation": "P:none F:executeTerminalPwd CB:executeTerminalPwd I:TerminalResult DB:terminal", "block": "/**\n * F:executeTerminalPwd - Get current working directory\n * @notation P:none F:executeTerminalPwd..."}, {"name": "TerminalResult", "file": "src\\core\\handlers\\terminal.ts", "notation": "P:command F:executeTerminalWhich CB:executeTerminalWhich I:TerminalResult DB:terminal", "block": "/**\n * F:executeTerminalWhich - Check if command exists\n * @notation P:command F:executeTerminalWhic..."}, {"name": "TerminalResult", "file": "src\\core\\handlers\\terminal.ts", "notation": "P:none F:executeTerminalEnv CB:executeTerminalEnv I:TerminalResult DB:terminal", "block": "/**\n * F:executeTerminalEnv - Get environment variables (filtered for security)\n * @notation P:none ..."}, {"name": "OperationResult", "file": "src\\core\\handlers\\terminal.ts", "notation": "P:command F:executeTerminalCommand CB:executeTerminalCommand I:OperationResult DB:terminal", "block": "/**\n * F:executeTerminalCommand - Execute terminal command with resilience\n * @notation P:command F:..."}, {"name": "TerminalResult", "file": "src\\core\\handlers\\terminal.ts", "notation": "P:templateSource,vars,engine F:executeTerminalTemplate CB:executeTerminalTemplate I:TerminalResult DB:none", "block": "/**\n * F:executeTerminalTemplate - Execute terminal template operation\n * @notation P:templateSource..."}, {"name": "function", "file": "src\\core\\handlers\\terminal.ts", "notation": "P:db F:terminalHandler CB:none I:function DB:terminal", "block": "/**\n * F:terminalHandler - Create terminal handler function\n * @notation P:db F:terminalHandler CB:n..."}, {"name": "TimeCommand", "file": "src\\core\\handlers\\time.ts", "notation": "P:core/handlers/time F:timeH<PERSON><PERSON>,executeTimeCommand CB:executeTimeCommand I:TimeCommand,TimeResult DB:time", "block": "/**\n * Time Handler - AI-Optimized Pure Functions\n * Migrated from BaseHandler class to pure functio..."}, {"name": "HandlerConfig", "file": "src\\core\\handlers\\time.ts", "notation": "P:none F:createTimeConfig CB:none I:HandlerConfig DB:none", "block": "/**\n * F:createTimeConfig - Create time handler configuration\n * @notation P:none F:createTimeConfig..."}, {"name": "boolean", "file": "src\\core\\handlers\\time.ts", "notation": "P:command F:validateTimeCommand CB:none I:boolean DB:none", "block": "/**\n * F:validateTimeCommand - Validate time command structure\n * @notation P:command F:validateTime..."}, {"name": "TimeResult", "file": "src\\core\\handlers\\time.ts", "notation": "P:timezone F:executeTimeCurrent CB:executeTimeCurrent I:TimeResult DB:time", "block": "/**\n * F:executeTimeCurrent - Get current time in specified timezone\n * @notation P:timezone F:execu..."}, {"name": "TimeResult", "file": "src\\core\\handlers\\time.ts", "notation": "P:timestamp,fromTimezone,toTimezone F:executeTimeConvert CB:executeTimeConvert I:TimeResult DB:time", "block": "/**\n * F:executeTimeConvert - Convert timestamp between timezones\n * @notation P:timestamp,fromTimez..."}, {"name": "TimeResult", "file": "src\\core\\handlers\\time.ts", "notation": "P:timestamp,format,timezone F:executeTimeFormat CB:executeTimeFormat I:TimeResult DB:time", "block": "/**\n * F:executeTimeFormat - Format timestamp with custom format\n * @notation P:timestamp,format,tim..."}, {"name": "TimeResult", "file": "src\\core\\handlers\\time.ts", "notation": "P:timestamp,amount,unit F:executeTimeAdd CB:executeTimeAdd I:TimeResult DB:time", "block": "/**\n * F:executeTimeAdd - Add time to timestamp\n * @notation P:timestamp,amount,unit F:executeTimeAd..."}, {"name": "TimeResult", "file": "src\\core\\handlers\\time.ts", "notation": "P:timestamp1,timestamp2 F:executeTimeDiff CB:executeTimeDiff I:TimeResult DB:time", "block": "/**\n * F:executeTimeDiff - Calculate difference between two timestamps\n * @notation P:timestamp1,tim..."}, {"name": "OperationResult", "file": "src\\core\\handlers\\time.ts", "notation": "P:command F:executeTimeCommand CB:executeTimeCommand I:OperationResult DB:time", "block": "/**\n * F:executeTimeCommand - Execute time command with resilience\n * @notation P:command F:executeT..."}, {"name": "TimeResult", "file": "src\\core\\handlers\\time.ts", "notation": "P:templateSource,vars,engine F:executeTimeTemplate CB:executeTimeTemplate I:TimeResult DB:none", "block": "/**\n * F:executeTimeTemplate - Execute time template operation\n * @notation P:templateSource,vars,en..."}, {"name": "function", "file": "src\\core\\handlers\\time.ts", "notation": "P:db F:timeHandler CB:none I:function DB:time", "block": "/**\n * F:timeHandler - Create time handler function\n * @notation P:db F:timeHandler CB:none I:functi..."}, {"name": "all", "file": "src\\core\\index.ts", "notation": "P:core/index F:all CB:none I:all DB:none", "block": "/**\n * Core Module - AI-Optimized Public API\n * Machine-only exports for AI consumption and mutation..."}, {"name": "all", "file": "src\\core\\notation\\index.ts", "notation": "P:core/notation/index F:all CB:none I:all DB:none", "block": "/**\n * Core Notation - AI-Optimized Notation Exports\n * Machine-only exports for AI consumption and ..."}, {"name": "RefactorManifest", "file": "src\\core\\report\\generateManifest.ts", "notation": "P:core/report/generateManifest F:createManifest,recordTransformation,generateReport CB:none I:RefactorManifest,TransformationRecord DB:none", "block": "/**\n * Manifest Generator - AI-Optimized Transformation Tracking\n * Pure functions for tracking and ..."}, {"name": "RefactorManifest", "file": "src\\core\\report\\generateManifest.ts", "notation": "P:none F:createManifest CB:none I:RefactorManifest DB:none", "block": "/**\n * F:createManifest - Create a new refactor manifest\n * @notation P:none F:createManifest CB:non..."}, {"name": "RefactorManifest", "file": "src\\core\\report\\generateManifest.ts", "notation": "P:manifest,transformation F:recordTransformation CB:none I:RefactorManifest,TransformationRecord DB:none", "block": "/**\n * F:recordTransformation - Record a transformation in the manifest\n * @notation P:manifest,tran..."}, {"name": "RefactorManifest", "file": "src\\core\\report\\generateManifest.ts", "notation": "P:manifest,symbolMove F:recordSymbolMove CB:none I:RefactorManifest,SymbolMove DB:none", "block": "/**\n * F:recordSymbolMove - Record a symbol move in the manifest\n * @notation P:manifest,symbolMove ..."}, {"name": "void", "file": "src\\core\\report\\generateManifest.ts", "notation": "P:manifest,filePath F:saveManifest CB:none I:void DB:none", "block": "/**\n * F:saveManifest - Save manifest to file system\n * @notation P:manifest,filePath F:saveManifest..."}, {"name": "RefactorManifest", "file": "src\\core\\report\\generateManifest.ts", "notation": "P:filePath F:loadManifest CB:none I:RefactorManifest DB:none", "block": "/**\n * F:loadManifest - Load manifest from file system\n * @notation P:filePath F:loadManifest CB:non..."}, {"name": "string", "file": "src\\core\\report\\generateManifest.ts", "notation": "P:manifest F:generateReport CB:none I:string DB:none", "block": "/**\n * F:generateReport - Generate a human-readable transformation report\n * @notation P:manifest F:..."}, {"name": "SymbolIndex", "file": "src\\core\\report\\generateManifest.ts", "notation": "P:filePath,symbols F:createSymbolIndex CB:none I:SymbolIndex DB:none", "block": "/**\n * F:createSymbolIndex - Create symbol index for a module\n * @notation P:filePath,symbols F:crea..."}, {"name": "void", "file": "src\\core\\report\\generateManifest.ts", "notation": "P:symbolIndex,outputDir F:saveSymbolIndex CB:none I:void DB:none", "block": "/**\n * F:saveSymbolIndex - Save symbol index to file\n * @notation P:symbolIndex,outputDir F:saveSymb..."}, {"name": "string", "file": "src\\core\\report\\generateManifest.ts", "notation": "P:manifest F:generateUndoScript CB:none I:string DB:none", "block": "/**\n * F:generateUndoScript - Generate undo script for transformations\n * @notation P:manifest F:gen..."}, {"name": "void", "file": "src\\core\\report\\generateManifest.ts", "notation": "P:transformation,logPath F:logTransformation CB:none I:void DB:none", "block": "/**\n * F:logTransformation - Log transformation to file\n * @notation P:transformation,logPath F:logT..."}, {"name": "all", "file": "src\\core\\report\\index.ts", "notation": "P:core/report/index F:all CB:none I:all DB:none", "block": "/**\n * Core Report - AI-Optimized Report Exports\n * Machine-only exports for AI consumption and muta..."}, {"name": "all", "file": "src\\core\\schema\\index.ts", "notation": "P:core/schema/index F:all CB:none I:all DB:none", "block": "/**\n * Core Schema - AI-Optimized Schema Exports\n * Machine-only exports for AI consumption and muta..."}, {"name": "SecurityEvent", "file": "src\\core\\schema\\securitySchema.ts", "notation": "P:core/schema/securitySchema F:validateSecurityEvent,validateSecurityResult CB:none I:SecurityEvent,SecurityValidationResult DB:security", "block": "/**\n * Security Schema - AI-Optimized Security Validation Types\n * Migrated from utils/security.ts t..."}, {"name": "boolean", "file": "src\\core\\schema\\securitySchema.ts", "notation": "P:event F:validateSecurityEvent CB:none I:boolean DB:none", "block": "/**\n * F:validateSecurityEvent - Validate security event structure\n * @notation P:event F:validateSe..."}, {"name": "boolean", "file": "src\\core\\schema\\securitySchema.ts", "notation": "P:result F:validateSecurityResult CB:none I:boolean DB:none", "block": "/**\n * F:validateSecurityResult - Validate security validation result structure\n * @notation P:resul..."}, {"name": "boolean", "file": "src\\core\\schema\\securitySchema.ts", "notation": "P:options F:validateCommandValidationOptions CB:none I:boolean DB:none", "block": "/**\n * F:validateCommandValidationOptions - Validate command validation options\n * @notation P:optio..."}, {"name": "boolean", "file": "src\\core\\schema\\securitySchema.ts", "notation": "P:options F:validateURLValidationOptions CB:none I:boolean DB:none", "block": "/**\n * F:validateURLValidationOptions - Validate URL validation options\n * @notation P:options F:val..."}, {"name": "SecurityValidationResult", "file": "src\\core\\schema\\securitySchema.ts", "notation": "P:isValid,reason,sanitized F:createSecurityValidationResult CB:none I:SecurityValidationResult DB:none", "block": "/**\n * F:createSecurityValidationResult - Create security validation result\n * @notation P:isValid,r..."}, {"name": "SecurityEvent", "file": "src\\core\\schema\\securitySchema.ts", "notation": "P:type,severity,source,details,blocked F:createSecurityEvent CB:none I:SecurityEvent DB:security", "block": "/**\n * F:createSecurityEvent - Create security event\n * @notation P:type,severity,source,details,blo..."}, {"name": "boolean", "file": "src\\core\\schema\\securitySchema.ts", "notation": "P:value F:isSecurityEvent CB:none I:boolean DB:none", "block": "/**\n * F:isSecurityEvent - Type guard for security event\n * @notation P:value F:isSecurityEvent CB:n..."}, {"name": "boolean", "file": "src\\core\\schema\\securitySchema.ts", "notation": "P:value F:isSecurityValidationResult CB:none I:boolean DB:none", "block": "/**\n * F:isSecurityValidationResult - Type guard for security validation result\n * @notation P:value..."}, {"name": "boolean", "file": "src\\core\\schema\\securitySchema.ts", "notation": "P:value F:isCommandValidationOptions CB:none I:boolean DB:none", "block": "/**\n * F:isCommandValidationOptions - Type guard for command validation options\n * @notation P:value..."}, {"name": "boolean", "file": "src\\core\\schema\\securitySchema.ts", "notation": "P:value F:isURLValidationOptions CB:none I:boolean DB:none", "block": "/**\n * F:isURLValidationOptions - Type guard for URL validation options\n * @notation P:value F:isURL..."}, {"name": "ValidationResult", "file": "src\\core\\schema\\validateSchema.ts", "notation": "P:core/schema/validateSchema F:validateSchema,createValidator CB:validateSchema I:ValidationResult,SchemaConfig DB:validation", "block": "/**\n * Universal Schema Validator - AI-Optimized Pure Functions\n * Centralized schema validation for..."}, {"name": "ValidationResult", "file": "src\\core\\schema\\validateSchema.ts", "notation": "P:none F:none CB:none I:ValidationResult DB:validation", "block": "/**\n * @I:ValidationResult - Schema validation result\n * @notation P:none F:none CB:none I:Validatio..."}, {"name": "SchemaConfig", "file": "src\\core\\schema\\validateSchema.ts", "notation": "P:none F:none CB:none I:SchemaConfig DB:validation", "block": "/**\n * @I:SchemaConfig - Schema configuration\n * @notation P:none F:none CB:none I:SchemaConfig DB:v..."}, {"name": "ValidatorCache", "file": "src\\core\\schema\\validateSchema.ts", "notation": "P:none F:none CB:none I:ValidatorCache DB:validation", "block": "/**\n * @I:ValidatorCache - Validator cache entry\n * @notation P:none F:none CB:none I:ValidatorCache..."}, {"name": "object", "file": "src\\core\\schema\\validateSchema.ts", "notation": "P:schemaPath F:loadSchema CB:loadSchema I:object DB:schemas", "block": "/**\n * F:loadSchema - Load schema from file system\n * @notation P:schemaPath F:loadSchema CB:loadSch..."}, {"name": "ValidateFunction", "file": "src\\core\\schema\\validateSchema.ts", "notation": "P:schema,config F:createValidator CB:createValidator I:ValidateFunction DB:validation", "block": "/**\n * F:createValidator - Create validator function for schema\n * @notation P:schema,config F:creat..."}, {"name": "ValidateFunction", "file": "src\\core\\schema\\validateSchema.ts", "notation": "P:schema<PERSON><PERSON>,config F:getCachedValidator CB:getCachedValidator I:ValidateFunction DB:validation", "block": "/**\n * F:getCachedValidator - Get cached validator or create new one\n * @notation P:schemaPath,confi..."}, {"name": "ValidationResult", "file": "src\\core\\schema\\validateSchema.ts", "notation": "P:data,schemaPath,config F:validateSchema CB:validateSchema I:ValidationResult DB:validation", "block": "/**\n * F:validateSchema - Universal schema validation function\n * @notation P:data,schemaPath,config..."}, {"name": "ValidationResult", "file": "src\\core\\schema\\validateSchema.ts", "notation": "P:data,schema,config F:validateWithSchema CB:validateWithSchema I:ValidationResult DB:validation", "block": "/**\n * F:validateWithSchema - Validate data with inline schema\n * @notation P:data,schema,config F:v..."}, {"name": "ValidationResult", "file": "src\\core\\schema\\validateSchema.ts", "notation": "P:data F:validateMemoryCommand CB:validateMemoryCommand I:ValidationResult DB:memory", "block": "/**\n * F:validateMemoryCommand - Validate memory command structure\n * @notation P:data F:validateMem..."}, {"name": "ValidationResult", "file": "src\\core\\schema\\validateSchema.ts", "notation": "P:data F:validateFileCommand CB:validateFileCommand I:ValidationResult DB:files", "block": "/**\n * F:validateFileCommand - Validate file command structure\n * @notation P:data F:validateFileCom..."}, {"name": "ValidationResult", "file": "src\\core\\schema\\validateSchema.ts", "notation": "P:data F:validateGitHubCommand CB:validateGitHubCommand I:ValidationResult DB:github", "block": "/**\n * F:validateGitHubCommand - Validate GitHub command structure\n * @notation P:data F:validateGit..."}, {"name": "ValidationResult", "file": "src\\core\\schema\\validateSchema.ts", "notation": "P:data F:validateDatabaseCommand CB:validateDatabaseCommand I:ValidationResult DB:database", "block": "/**\n * F:validateDatabaseCommand - Validate database command structure\n * @notation P:data F:validat..."}, {"name": "ValidationResult", "file": "src\\core\\schema\\validateSchema.ts", "notation": "P:data F:validateEnhancedToolCommand CB:validateEnhancedToolCommand I:ValidationResult DB:enhanced_tools", "block": "/**\n * F:validateEnhancedToolCommand - Validate enhanced tool command structure\n * @notation P:data ..."}, {"name": "ValidationResult", "file": "src\\core\\schema\\validateSchema.ts", "notation": "P:data F:validateCoordinationCommand CB:validateCoordinationCommand I:ValidationResult DB:coordination", "block": "/**\n * F:validateCoordinationCommand - Validate coordination command structure\n * @notation P:data F..."}, {"name": "ValidationResult", "file": "src\\core\\schema\\validateSchema.ts", "notation": "P:data F:validateMonitoringCommand CB:validateMonitoringCommand I:ValidationResult DB:monitoring", "block": "/**\n * F:validateMonitoringCommand - Validate monitoring command structure\n * @notation P:data F:val..."}, {"name": "ValidationResult", "file": "src\\core\\schema\\validateSchema.ts", "notation": "P:data F:validateTerminalCommand CB:validateTerminalCommand I:ValidationResult DB:terminal", "block": "/**\n * F:validateTerminalCommand - Validate terminal command structure\n * @notation P:data F:validat..."}, {"name": "void", "file": "src\\core\\schema\\validateSchema.ts", "notation": "P:none F:clearValidatorCache CB:none I:void DB:validation", "block": "/**\n * F:clearValidatorCache - Clear validator cache\n * @notation P:none F:clearValidatorCache CB:no..."}, {"name": "object", "file": "src\\core\\schema\\validateSchema.ts", "notation": "P:none F:getValidatorCacheStats CB:none I:object DB:validation", "block": "/**\n * F:getValidatorCacheStats - Get validator cache statistics\n * @notation P:none F:getValidatorC..."}, {"name": "void", "file": "src\\core\\schema\\validateSchema.ts", "notation": "P:data,schemaPath,config F:validateAndThrow CB:validateAndThrow I:void DB:validation", "block": "/**\n * F:validateAndThrow - Validate data and throw on error\n * @notation P:data,schemaPath,config F..."}, {"name": "function", "file": "src\\core\\schema\\validateSchema.ts", "notation": "P:schema<PERSON><PERSON>,config F:createSchemaValidator CB:createSchemaValidator I:function DB:validation", "block": "/**\n * F:createSchemaValidator - Create schema validator factory\n * @notation P:schemaPath,config F:..."}, {"name": "all", "file": "src\\core\\state\\index.ts", "notation": "P:core/state/index F:all CB:none I:all DB:none", "block": "/**\n * Core State - AI-Optimized State Exports\n * Machine-only exports for AI consumption and mutati..."}, {"name": "SecurityState", "file": "src\\core\\state\\securityState.ts", "notation": "P:core/state/securityState F:createSecurityState,addSecurityEvent CB:addSecurityEvent I:SecurityState,SecurityEventLog DB:security", "block": "/**\n * Security State - AI-Optimized Security State Management\n * Pure functions for security event ..."}, {"name": "SecurityState", "file": "src\\core\\state\\securityState.ts", "notation": "P:config,maxEvents F:createSecurityState CB:none I:SecurityState DB:security", "block": "/**\n * F:createSecurityState - Create initial security state\n * @notation P:config,maxEvents F:creat..."}, {"name": "SecurityState", "file": "src\\core\\state\\securityState.ts", "notation": "P:state,event F:addSecurityEvent CB:addSecurityEvent I:SecurityState DB:security", "block": "/**\n * F:addSecurityEvent - Add security event to state\n * @notation P:state,event F:addSecurityEven..."}, {"name": "SecurityState", "file": "src\\core\\state\\securityState.ts", "notation": "P:state,result F:updateLastValidation CB:none I:SecurityState DB:none", "block": "/**\n * F:updateLastValidation - Update last validation result\n * @notation P:state,result F:updateLa..."}, {"name": "SecurityState", "file": "src\\core\\state\\securityState.ts", "notation": "P:state F:clearSecurityEvents CB:none I:SecurityState DB:security", "block": "/**\n * F:clearSecurityEvents - Clear all security events\n * @notation P:state F:clearSecurityEvents ..."}, {"name": "array", "file": "src\\core\\state\\securityState.ts", "notation": "P:state,timeWindowMs F:getRecentSecurityEvents CB:none I:array DB:none", "block": "/**\n * F:getRecentSecurityEvents - Get recent security events within time window\n * @notation P:stat..."}, {"name": "array", "file": "src\\core\\state\\securityState.ts", "notation": "P:state,type F:getSecurityEventsByType CB:none I:array DB:none", "block": "/**\n * F:getSecurityEventsByType - Get security events by type\n * @notation P:state,type F:getSecuri..."}, {"name": "array", "file": "src\\core\\state\\securityState.ts", "notation": "P:state,severity F:getSecurityEventsBySeverity CB:none I:array DB:none", "block": "/**\n * F:getSecurityEventsBySeverity - Get security events by severity\n * @notation P:state,severity..."}, {"name": "SecurityMetrics", "file": "src\\core\\state\\securityState.ts", "notation": "P:state F:getSecurityMetrics CB:none I:SecurityMetrics DB:none", "block": "/**\n * F:getSecurityMetrics - Get security metrics from state\n * @notation P:state F:getSecurityMetr..."}, {"name": "object", "file": "src\\core\\state\\securityState.ts", "notation": "P:state,command,args,allowedCommands F:validateCommandWithState CB:validateCommandWithState I:object DB:security", "block": "/**\n * F:validateCommandWithState - Validate command and update state\n * @notation P:state,command,a..."}, {"name": "object", "file": "src\\core\\state\\securityState.ts", "notation": "P:state,command,args F:validateGitCommandWithState CB:validateGitCommandWithState I:object DB:security", "block": "/**\n * F:validateGitCommandWithState - Validate git command with state\n * @notation P:state,command,..."}, {"name": "object", "file": "src\\core\\state\\securityState.ts", "notation": "P:state,command,args F:validateTerminalCommandWithState CB:validateTerminalCommandWithState I:object DB:security", "block": "/**\n * F:validateTerminalCommandWithState - Validate terminal command with state\n * @notation P:stat..."}, {"name": "SecurityState", "file": "src\\core\\state\\securityState.ts", "notation": "P:state,type,severity,source,details,blocked F:logSecurityEventToState CB:logSecurityEventToState I:SecurityState DB:security", "block": "/**\n * F:logSecurityEventToState - Log security event and update state\n * @notation P:state,type,sev..."}, {"name": "SecurityEventLog", "file": "src\\core\\state\\securityState.ts", "notation": "P:state,timeWindowMs F:createSecurityEventLog CB:none I:SecurityEventLog DB:none", "block": "/**\n * F:createSecurityEventLog - Create security event log from state\n * @notation P:state,timeWind..."}, {"name": "SecurityState", "file": "src\\core\\state\\securityState.ts", "notation": "P:none F:getGlobalSecurityState CB:none I:SecurityState DB:none", "block": "/**\n * F:getGlobalSecurityState - Get global security state\n * @notation P:none F:getGlobalSecurityS..."}, {"name": "void", "file": "src\\core\\state\\securityState.ts", "notation": "P:newState F:updateGlobalSecurityState CB:none I:void DB:security", "block": "/**\n * F:updateGlobalSecurityState - Update global security state\n * @notation P:newState F:updateGl..."}, {"name": "void", "file": "src\\core\\state\\securityState.ts", "notation": "P:config,maxEvents F:resetGlobalSecurityState CB:none I:void DB:security", "block": "/**\n * F:resetGlobalSecurityState - Reset global security state\n * @notation P:config,maxEvents F:re..."}, {"name": "AgentState", "file": "src\\core\\tools\\agent\\engine.ts", "notation": "P:core/tools/agent/engine F:executeAgentLoop,createAgentState CB:executeAgentLoop I:AgentState,AgentConfig DB:agent", "block": "/**\n * Agent Engine - AI-Optimized Pure Functions\n * Migrated from src/agent/engine.ts to pure funct..."}, {"name": "AgentConfig", "file": "src\\core\\tools\\agent\\engine.ts", "notation": "P:none F:createDefaultAgentConfig CB:none I:AgentConfig DB:none", "block": "/**\n * F:createDefaultAgentConfig - Create default agent configuration\n * @notation P:none F:createD..."}, {"name": "AgentState", "file": "src\\core\\tools\\agent\\engine.ts", "notation": "P:database,config F:createAgentState CB:none I:AgentState DB:agent", "block": "/**\n * F:createAgentState - Create initial agent state\n * @notation P:database,config F:createAgentS..."}, {"name": "string", "file": "src\\core\\tools\\agent\\engine.ts", "notation": "P:directivePath F:loadAgentDirectives CB:none I:string DB:none", "block": "/**\n * F:loadAgentDirectives - Load agent directives from file\n * @notation P:directivePath F:loadAg..."}, {"name": "ContextualState", "file": "src\\core\\tools\\agent\\engine.ts", "notation": "P:workspaceRoot F:consumeContextualState CB:consumeContextualState I:ContextualState DB:agent", "block": "/**\n * F:consumeContextualState - Consume all contextual state sources with caching optimization\n * ..."}, {"name": "DirectiveEnforcementResult", "file": "src\\core\\tools\\agent\\engine.ts", "notation": "P:context,response F:enforceDirectiveCompliance CB:enforceDirectiveCompliance I:DirectiveEnforcementResult DB:agent", "block": "/**\n * F:enforceDirectiveCompliance - Enforce directive compliance\n * @notation P:context,response F..."}, {"name": "AgentState", "file": "src\\core\\tools\\agent\\engine.ts", "notation": "P:state F:startAgentState CB:none I:AgentState DB:agent", "block": "/**\n * F:startAgentState - Start agent state\n * @notation P:state F:startAgentState CB:none I:AgentS..."}, {"name": "AgentState", "file": "src\\core\\tools\\agent\\engine.ts", "notation": "P:state F:stopAgentState CB:none I:AgentState DB:agent", "block": "/**\n * F:stopAgentState - Stop agent state\n * @notation P:state F:stopAgentState CB:none I:AgentStat..."}, {"name": "AgentState", "file": "src\\core\\tools\\agent\\engine.ts", "notation": "P:state F:updateHeartbeat CB:none I:AgentState DB:agent", "block": "/**\n * F:updateHeartbeat - Update agent heartbeat\n * @notation P:state F:updateHeartbeat CB:none I:A..."}, {"name": "void", "file": "src\\core\\tools\\agent\\engine.ts", "notation": "P:state,config F:logHeartbeat CB:none I:void DB:none", "block": "/**\n * F:logHeartbeat - Log agent heartbeat\n * @notation P:state,config F:logHeartbeat CB:none I:voi..."}, {"name": "void", "file": "src\\core\\tools\\agent\\engine.ts", "notation": "P:state,config,onShutdown F:setupShutdownHandlers CB:none I:void DB:agent", "block": "/**\n * F:setupShutdownHandlers - Setup graceful shutdown handlers\n * @notation P:state,config,onShut..."}, {"name": "Promise", "file": "src\\core\\tools\\agent\\engine.ts", "notation": "P:database,config F:executeAgentLoop CB:executeAgentLoop I:Promise DB:agent", "block": "/**\n * F:executeAgentLoop - Execute agent loop with pure functions\n * @notation P:database,config F:..."}, {"name": "object", "file": "src\\core\\tools\\agent\\engine.ts", "notation": "P:database F:initializeHandlerRegistry CB:initializeHandlerRegistry I:object DB:handlers", "block": "/**\n * F:initializeHandlerRegistry - Initialize handler registry for direct MCP execution\n * @notati..."}, {"name": "unknown", "file": "src\\core\\tools\\agent\\engine.ts", "notation": "P:handlerReg<PERSON>ry,command,payload F:executeHandlerCommand CB:executeHandlerCommand I:unknown DB:handlers", "block": "/**\n * F:executeHandlerCommand - Execute command through handler registry\n * @notation P:handlerRegi..."}, {"name": "number", "file": "src\\core\\tools\\agent\\engine.ts", "notation": "P:state F:getAgentUptime CB:none I:number DB:none", "block": "/**\n * F:getAgentUptime - Get agent uptime in seconds\n * @notation P:state F:getAgentUptime CB:none ..."}, {"name": "boolean", "file": "src\\core\\tools\\agent\\engine.ts", "notation": "P:state,maxHeartbeatAge F:isAgentHealthy CB:none I:boolean DB:none", "block": "/**\n * F:isAgentHealthy - Check if agent is healthy\n * @notation P:state,maxHeartbeatAge F:isAgentHe..."}, {"name": "object", "file": "src\\core\\tools\\agent\\engine.ts", "notation": "P:state F:getAgentStatus CB:none I:object DB:none", "block": "/**\n * F:getAgentStatus - Get comprehensive agent status\n * @notation P:state F:getAgentStatus CB:no..."}, {"name": "Promise", "file": "src\\core\\tools\\agent\\engine.ts", "notation": "P:db F:executeAgentLoopLegacy CB:executeAgentLoopLegacy I:Promise DB:agent", "block": "/**\n * F:executeAgentLoopLegacy - Legacy compatibility wrapper\n * @notation P:db F:executeAgentLoopL..."}, {"name": "ExecutionPlan", "file": "src\\core\\tools\\agent\\executionPlanner.ts", "notation": "P:core/tools/agent/executionPlanner F:createExecutionPlan,optimizeExecutionPlan CB:createExecutionPlan I:ExecutionPlan,ExecutionTask DB:execution", "block": "/**\n * Execution Planner - AI-Optimized Pure Functions\n * Migrated from src/agent/executionPlanner.t..."}, {"name": "ExecutionTask", "file": "src\\core\\tools\\agent\\executionPlanner.ts", "notation": "P:id,type,parameters,dependencies F:createExecutionTask CB:none I:ExecutionTask DB:execution", "block": "/**\n * F:createExecutionTask - Create execution task with defaults\n * @notation P:id,type,parameters..."}, {"name": "TaskDependencyGraph", "file": "src\\core\\tools\\agent\\executionPlanner.ts", "notation": "P:tasks F:buildDependencyGraph CB:none I:TaskDependencyGraph DB:none", "block": "/**\n * F:buildDependencyGraph - Build task dependency graph\n * @notation P:tasks F:buildDependencyGr..."}, {"name": "object", "file": "src\\core\\tools\\agent\\executionPlanner.ts", "notation": "P:tasks F:validateDependencies CB:none I:object DB:none", "block": "/**\n * F:validateDependencies - Validate task dependencies for cycles\n * @notation P:tasks F:validat..."}, {"name": "array", "file": "src\\core\\tools\\agent\\executionPlanner.ts", "notation": "P:tasks,options F:generateExecutionSequence CB:none I:array DB:none", "block": "/**\n * F:generateExecutionSequence - Generate optimal execution sequence\n * @notation P:tasks,option..."}, {"name": "ValidationResult", "file": "src\\core\\tools\\agent\\executionPlanner.ts", "notation": "P:targetModule,proposedChanges F:preemptiveValidationPipeline CB:preemptiveValidationPipeline I:ValidationResult DB:execution", "block": "/**\n * F:preemptiveValidationPipeline - Mandatory validation before any implementation\n * @notation ..."}, {"name": "array", "file": "src\\core\\tools\\agent\\executionPlanner.ts", "notation": "P:workspaceRoot F:checkExistingCompilationErrors CB:none I:array DB:validation", "block": "/**\n * F:checkExistingCompilationErrors - Check for existing TypeScript compilation errors using Typ..."}, {"name": "boolean", "file": "src\\core\\tools\\agent\\executionPlanner.ts", "notation": "P:workspaceRoot F:executeActualTypeScriptCompilation CB:none I:boolean DB:validation", "block": "/**\n * F:executeActualTypeScriptCompilation - Execute real TypeScript compilation check using TypeSc..."}, {"name": "boolean", "file": "src\\core\\tools\\agent\\executionPlanner.ts", "notation": "P:targetModule,workspaceRoot F:executeActualRuntimeValidation CB:none I:boolean DB:validation", "block": "/**\n * F:executeActualRuntimeValidation - Execute real runtime validation using MCP server\n * @notat..."}, {"name": "boolean", "file": "src\\core\\tools\\agent\\executionPlanner.ts", "notation": "P:targetM<PERSON>ule,proposed<PERSON><PERSON><PERSON>,workspaceRoot F:executeActualArchitecturalAnalysis CB:none I:boolean DB:validation", "block": "/**\n * F:executeActualArchitecturalAnalysis - Execute real codebase analysis using codebase-retrieva..."}, {"name": "PlanningResult", "file": "src\\core\\tools\\agent\\executionPlanner.ts", "notation": "P:sessionId,tasks,options F:createExecutionPlan CB:createExecutionPlan I:PlanningResult DB:execution", "block": "/**\n * F:createExecutionPlan - Create execution plan from tasks with preemptive validation integrati..."}, {"name": "ExecutionPlan", "file": "src\\core\\tools\\agent\\executionPlanner.ts", "notation": "P:plan,options F:optimizeExecutionPlan CB:optimizeExecutionPlan I:ExecutionPlan DB:execution", "block": "/**\n * F:optimizeExecutionPlan - Optimize existing execution plan\n * @notation P:plan,options F:opti..."}, {"name": "TaskConfig", "file": "src\\core\\tools\\agent\\executionPlanner.ts", "notation": "P:type F:getTaskConfig CB:none I:TaskConfig DB:none", "block": "/**\n * F:getTaskConfig - Get task configuration by type\n * @notation P:type F:getTaskConfig CB:none ..."}, {"name": "number", "file": "src\\core\\tools\\agent\\executionPlanner.ts", "notation": "P:plan F:estimatePlanDuration CB:none I:number DB:none", "block": "/**\n * F:estimatePlanDuration - Estimate plan duration considering parallelization\n * @notation P:pl..."}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "file": "src\\core\\tools\\agent\\feedbackProcessor.ts", "notation": "P:core/tools/agent/feedbackProcessor F:processFeedback,analyzeExecutionResults CB:processFeedback I:FeedbackResult,ContextTransformation DB:feedback", "block": "/**\n * Feedback Processor - AI-Optimized Pure Functions\n * Migrated from src/agent/feedbackProcessor..."}, {"name": "PerformanceBaseline", "file": "src\\core\\tools\\agent\\feedbackProcessor.ts", "notation": "P:results F:calculateDynamicBaseline CB:none I:PerformanceBaseline DB:feedback", "block": "/**\n * F:calculateDynamicBaseline - Calculate performance baseline from execution history\n * @notati..."}, {"name": "PerformanceBaseline", "file": "src\\core\\tools\\agent\\feedbackProcessor.ts", "notation": "P:results F:getPerformanceThresholds CB:none I:PerformanceBaseline DB:feedback", "block": "/**\n * F:getPerformanceThresholds - Get current performance thresholds (dynamic or default)\n * @nota..."}, {"name": "array", "file": "src\\core\\tools\\agent\\feedbackProcessor.ts", "notation": "P:results F:detectSuccessPatterns CB:none I:array DB:feedback", "block": "/**\n * F:detectSuccessPatterns - Detect success patterns from execution results\n * @notation P:resul..."}, {"name": "array", "file": "src\\core\\tools\\agent\\feedbackProcessor.ts", "notation": "P:results F:detectErrorPatterns CB:none I:array DB:feedback", "block": "/**\n * F:detectErrorPatterns - Detect error patterns from execution results\n * @notation P:results F..."}, {"name": "array", "file": "src\\core\\tools\\agent\\feedbackProcessor.ts", "notation": "P:results F:analyzePerformanceMetrics CB:none I:array DB:feedback", "block": "/**\n * F:analyzePerformanceMetrics - Analyze performance metrics from execution results\n * @notation..."}, {"name": "array", "file": "src\\core\\tools\\agent\\feedbackProcessor.ts", "notation": "P:successPatterns,errorPatterns,options F:generateContextTransformations CB:none I:array DB:feedback", "block": "/**\n * F:generateContextTransformations - Generate context transformations from patterns\n * @notatio..."}, {"name": "array", "file": "src\\core\\tools\\agent\\feedbackProcessor.ts", "notation": "P:insights,planId F:generateExecutionRefinements CB:none I:array DB:feedback", "block": "/**\n * F:generateExecutionRefinements - Generate execution refinements from insights\n * @notation P:..."}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "file": "src\\core\\tools\\agent\\feedbackProcessor.ts", "notation": "P:results,planId,options F:processFeedback CB:processFeedback I:FeedbackResult DB:feedback", "block": "/**\n * F:processFeedback - Process execution feedback and generate insights\n * @notation P:results,p..."}, {"name": "object", "file": "src\\core\\tools\\agent\\feedbackProcessor.ts", "notation": "P:results F:analyzeExecutionResults CB:analyzeExecutionResults I:object DB:feedback", "block": "/**\n * F:analyzeExecutionResults - Analyze execution results for patterns\n * @notation P:results F:a..."}, {"name": "string", "file": "src\\core\\tools\\agent\\feedbackProcessor.ts", "notation": "P:feedbackResult F:createFeedbackSummary CB:none I:string DB:none", "block": "/**\n * F:createFeedbackSummary - Create feedback summary for logging\n * @notation P:feedbackResult F..."}, {"name": "all", "file": "src\\core\\tools\\agent\\index.ts", "notation": "P:core/tools/agent/index F:all CB:none I:all DB:agent", "block": "/**\n * Agent <PERSON>ls - AI-Optimized Agent Module Index\n * Consolidated exports for all agent functiona..."}, {"name": "object", "file": "src\\core\\tools\\agent\\index.ts", "notation": "P:sessionId,templatePath,database F:createAgentWorkflow CB:createAgentWorkflow I:object DB:agent", "block": "/**\n * F:createAgentWorkflow - Create complete agent workflow\n * @notation P:sessionId,templatePath,..."}, {"name": "object", "file": "src\\core\\tools\\agent\\index.ts", "notation": "P:workflow,tasks F:executeAgentWorkflow CB:executeAgentWorkflow I:object DB:agent", "block": "/**\n * F:executeAgentWorkflow - Execute complete agent workflow\n * @notation P:workflow,tasks F:exec..."}, {"name": "object", "file": "src\\core\\tools\\agent\\index.ts", "notation": "P:none F:getAgentModuleInfo CB:none I:object DB:none", "block": "/**\n * F:getAgentModuleInfo - Get agent module information\n * @notation P:none F:getAgentModuleInfo ..."}, {"name": "object", "file": "src\\core\\tools\\agent\\index.ts", "notation": "P:none F:validateAgentModule CB:none I:object DB:none", "block": "/**\n * F:validateAgentModule - Validate agent module integrity\n * @notation P:none F:validateAgentMo..."}, {"name": "object", "file": "src\\core\\tools\\agent\\index.ts", "notation": "P:workspaceRoot,config F:initializeAgentRuntime CB:initializeAgentRuntime I:object DB:agent", "block": "/**\n * F:initializeAgentRuntime - Initialize agent runtime with all subsystems\n * @notation P:worksp..."}, {"name": "object", "file": "src\\core\\tools\\agent\\index.ts", "notation": "P:workspaceRoot F:registerRuntimeSymbols CB:registerRuntimeSymbols I:object DB:agent", "block": "/**\n * F:registerRuntimeSymbols - Register runtime symbols for agent system\n * @notation P:workspace..."}, {"name": "PromptAnalysis", "file": "src\\core\\tools\\agent\\templateParser.ts", "notation": "P:core/tools/agent/templateParser F:parsePromptTemplate,validatePromptAnalysis CB:parsePromptTemplate I:PromptAnalysis,TemplateParseResult DB:templates", "block": "/**\n * Template Parser - AI-Optimized Pure Functions\n * Migrated from src/agent/templateParser.ts to..."}, {"name": "boolean", "file": "src\\core\\tools\\agent\\templateParser.ts", "notation": "P:analysis F:validatePromptAnalysis CB:none I:boolean DB:none", "block": "/**\n * F:validatePromptAnalysis - Validate prompt analysis structure\n * @notation P:analysis F:valid..."}, {"name": "TemplateValidationResult", "file": "src\\core\\tools\\agent\\templateParser.ts", "notation": "P:analysis,options F:validateTemplateContent CB:none I:TemplateValidationResult DB:none", "block": "/**\n * F:validateTemplateContent - Validate template content quality\n * @notation P:analysis,options..."}, {"name": "string", "file": "src\\core\\tools\\agent\\templateParser.ts", "notation": "P:content F:extractGoal CB:none I:string DB:none", "block": "/**\n * F:extractGoal - Extract goal from template content\n * @notation P:content F:extractGoal CB:no..."}, {"name": "array", "file": "src\\core\\tools\\agent\\templateParser.ts", "notation": "P:content F:extractTraits CB:none I:array DB:none", "block": "/**\n * F:extractTraits - Extract traits from template content\n * @notation P:content F:extractTraits..."}, {"name": "array", "file": "src\\core\\tools\\agent\\templateParser.ts", "notation": "P:content F:extractTools CB:none I:array DB:none", "block": "/**\n * F:extractTools - Extract tools from template content\n * @notation P:content F:extractTools CB..."}, {"name": "PromptAnalysis", "file": "src\\core\\tools\\agent\\templateParser.ts", "notation": "P:content,options F:parseTemplateContent CB:none I:PromptAnalysis DB:none", "block": "/**\n * F:parseTemplateContent - Parse template content into analysis\n * @notation P:content,options ..."}, {"name": "TemplateParseResult", "file": "src\\core\\tools\\agent\\templateParser.ts", "notation": "P:filepath,options F:parsePromptTemplate CB:parsePromptTemplate I:TemplateParseResult DB:templates", "block": "/**\n * F:parsePromptTemplate - Parse prompt template file with comprehensive error handling\n * @nota..."}, {"name": "PromptAnalysis", "file": "src\\core\\tools\\agent\\templateParser.ts", "notation": "P:none F:createEmptyPromptAnalysis CB:none I:PromptAnalysis DB:none", "block": "/**\n * F:createEmptyPromptAnalysis - Create empty prompt analysis\n * @notation P:none F:createEmptyP..."}, {"name": "PromptAnalysis", "file": "src\\core\\tools\\agent\\templateParser.ts", "notation": "P:analyses F:mergePromptAnalyses CB:none I:PromptAnalysis DB:none", "block": "/**\n * F:mergePromptAnalyses - Merge multiple prompt analyses\n * @notation P:analyses F:mergePromptA..."}, {"name": "PromptAnalysis", "file": "src\\core\\tools\\agent\\templateParser.ts", "notation": "P:filepath F:parsePromptTemplateLegacy CB:parsePromptTemplateLegacy I:PromptAnalysis DB:templates", "block": "/**\n * F:parsePromptTemplateLegacy - Legacy compatibility wrapper\n * @notation P:filepath F:parsePro..."}, {"name": "BatchAnalysisOptions", "file": "src\\core\\tools\\batchAnalyzer.ts", "notation": "P:core/tools/batchAnalyzer F:analyzeBatch,analyzeFile,extractMethods,extractCircuitBreakers,extractInterfaces,extractImports,extractExports CB:none I:BatchAnalysisOptions,FileAnalysisResult,BatchAnalysisResult DB:none", "block": "/**\n * Batch Analyzer Tool - AI-Optimized Analysis Engine\n * Pure functions for batch file analysis ..."}, {"name": "BatchAnalysisOptions", "file": "src\\core\\tools\\batchAnalyzer.ts", "notation": "P:options,workspaceRoot F:analyzeBatch CB:none I:BatchAnalysisOptions,BatchAnalysisResult DB:none", "block": "/**\n * F:analyzeBatch - Analyze multiple files matching a pattern\n * @notation P:options,workspaceRo..."}, {"name": "FileAnalysisResult", "file": "src\\core\\tools\\batchAnalyzer.ts", "notation": "P:filePath,options,workspaceRoot F:analyzeFile CB:none I:FileAnalysisResult DB:none", "block": "/**`)\n      },\n      (err, matches) => {\n        if (err) reject(err)\n        else resolve(matches)\n..."}, {"name": "MethodInfo", "file": "src\\core\\tools\\batchAnalyzer.ts", "notation": "P:lines F:extractMethods CB:none I:MethodInfo DB:none", "block": "/**\n * F:extractMethods - Extract method information from file lines\n * @notation P:lines F:extractM..."}, {"name": "CircuitBreakerInfo", "file": "src\\core\\tools\\batchAnalyzer.ts", "notation": "P:content,lines F:extractCircuitBreakers CB:none I:CircuitBreakerInfo DB:none", "block": "/**\n * F:extractCircuitBreakers - Extract circuit breaker patterns from content\n * @notation P:conte..."}, {"name": "InterfaceInfo", "file": "src\\core\\tools\\batchAnalyzer.ts", "notation": "P:lines F:extractInterfaces CB:none I:InterfaceInfo DB:none", "block": "/**\n * F:extractInterfaces - Extract interface definitions from lines\n * @notation P:lines F:extract..."}, {"name": "ImportInfo", "file": "src\\core\\tools\\batchAnalyzer.ts", "notation": "P:lines F:extractImports CB:none I:ImportInfo DB:none", "block": "/**\n * F:extractImports - Extract import statements from lines\n * @notation P:lines F:extractImports..."}, {"name": "ExportInfo", "file": "src\\core\\tools\\batchAnalyzer.ts", "notation": "P:lines F:extractExports CB:none I:ExportInfo DB:none", "block": "/**\n * F:extractExports - Extract export statements from lines\n * @notation P:lines F:extractExports..."}, {"name": "all", "file": "src\\core\\tools\\index.ts", "notation": "P:core/tools/index F:all CB:none I:all DB:none", "block": "/**\n * Core Tools - AI-Optimized Tool Exports\n * Machine-only exports for AI consumption and mutatio..."}, {"name": "QueryResult", "file": "src\\core\\tools\\queryOptimizer.ts", "notation": "P:core/tools/queryOptimizer F:executeOptimizedQuery,QueryBuilder CB:executeOptimizedQuery I:QueryResult,QueryBuilderState DB:queries", "block": "/**\n * Query Optimizer - AI-Optimized Pure Functions\n * Simple database query optimization and build..."}, {"name": "QueryResult", "file": "src\\core\\tools\\queryOptimizer.ts", "notation": "P:none F:none CB:none I:QueryResult DB:queries", "block": "/**\n * @I:QueryResult - Query execution result\n * @notation P:none F:none CB:none I:QueryResult DB:q..."}, {"name": "QueryBuilderState", "file": "src\\core\\tools\\queryOptimizer.ts", "notation": "P:none F:none CB:none I:QueryBuilderState DB:queries", "block": "/**\n * @I:QueryBuilderState - Query builder state\n * @notation P:none F:none CB:none I:QueryBuilderS..."}, {"name": "QueryResult", "file": "src\\core\\tools\\queryOptimizer.ts", "notation": "P:db,sql,params F:executeOptimizedQuery CB:executeOptimizedQuery I:QueryResult DB:queries", "block": "/**\n * F:executeOptimizedQuery - Execute optimized database query\n * @notation P:db,sql,params F:exe..."}, {"name": "QueryBuilderState", "file": "src\\core\\tools\\queryOptimizer.ts", "notation": "P:table F:createQueryBuilderState CB:none I:QueryBuilderState DB:queries", "block": "/**\n * F:createQueryBuilderState - Create initial query builder state\n * @notation P:table F:createQ..."}, {"name": "QueryBuilderState", "file": "src\\core\\tools\\queryOptimizer.ts", "notation": "P:state,condition,param F:addWhereCondition CB:none I:QueryBuilderState DB:queries", "block": "/**\n * F:addWhereCondition - Add WHERE condition to query builder state\n * @notation P:state,conditi..."}, {"name": "QueryBuilderState", "file": "src\\core\\tools\\queryOptimizer.ts", "notation": "P:state,limit F:setLimit CB:none I:QueryBuilderState DB:queries", "block": "/**\n * F:setLimit - Set LIMIT for query builder state\n * @notation P:state,limit F:setLimit CB:none ..."}, {"name": "object", "file": "src\\core\\tools\\queryOptimizer.ts", "notation": "P:state F:buildSelectQuery CB:none I:object DB:queries", "block": "/**\n * F:buildSelectQuery - Build SELECT query from state\n * @notation P:state F:buildSelectQuery CB..."}, {"name": "QueryBuilder", "file": "src\\core\\tools\\queryOptimizer.ts", "notation": "P:table F:QueryBuilder CB:none I:QueryBuilder DB:queries", "block": "/**\n * QueryBuilder - Functional query builder class for compatibility\n * @notation P:table F:QueryB..."}, {"name": "QueryBuilder", "file": "src\\core\\tools\\queryOptimizer.ts", "notation": "P:condition,param F:where CB:none I:QueryBuilder DB:queries", "block": "/**\n   * F:where - Add WHERE condition\n   * @notation P:condition,param F:where CB:none I:QueryBuild..."}, {"name": "QueryBuilder", "file": "src\\core\\tools\\queryOptimizer.ts", "notation": "P:limit F:limit CB:none I:QueryBuilder DB:queries", "block": "/**\n   * F:limit - Set LIMIT\n   * @notation P:limit F:limit CB:none I:QueryBuilder DB:queries\n   */..."}, {"name": "QueryResult", "file": "src\\core\\tools\\queryOptimizer.ts", "notation": "P:db F:execute CB:execute I:QueryResult DB:queries", "block": "/**\n   * F:execute - Execute the built query\n   * @notation P:db F:execute CB:execute I:QueryResult ..."}, {"name": "object", "file": "src\\core\\tools\\queryOptimizer.ts", "notation": "P:none F:toSQL CB:none I:object DB:queries", "block": "/**\n   * F:toSQL - Get SQL and parameters\n   * @notation P:none F:toSQL CB:none I:object DB:queries\n..."}, {"name": "string", "file": "src\\core\\tools\\queryOptimizer.ts", "notation": "P:tableName F:sanitizeTableName CB:none I:string DB:queries", "block": "/**\n * F:sanitizeTableName - Sanitize table name for SQL injection prevention\n * @notation P:tableNa..."}, {"name": "string", "file": "src\\core\\tools\\queryOptimizer.ts", "notation": "P:columnName F:sanitizeColumnName CB:none I:string DB:queries", "block": "/**\n * F:sanitizeColumnName - Sanitize column name for SQL injection prevention\n * @notation P:colum..."}, {"name": "object", "file": "src\\core\\tools\\queryOptimizer.ts", "notation": "P:table,data F:createInsertQuery CB:none I:object DB:queries", "block": "/**\n * F:createInsertQuery - Create INSERT query\n * @notation P:table,data F:createInsertQuery CB:no..."}, {"name": "object", "file": "src\\core\\tools\\queryOptimizer.ts", "notation": "P:table,data,filters F:createUpdateQuery CB:none I:object DB:queries", "block": "/**\n * F:createUpdateQuery - Create UPDATE query\n * @notation P:table,data,filters F:createUpdateQue..."}, {"name": "TemplateResult", "file": "src\\core\\tools\\templateProcessor.ts", "notation": "P:core/tools/templateProcessor F:processMultiHandlerTemplate,processTemplate CB:processTemplate I:TemplateResult,TemplateOptions DB:templates", "block": "/**\n * Template Processor - AI-Optimized Pure Functions\n * Provides multi-handler template processin..."}, {"name": "TemplateOptions", "file": "src\\core\\tools\\templateProcessor.ts", "notation": "P:none F:none CB:none I:TemplateOptions DB:templates", "block": "/**\n * @I:TemplateOptions - Template processing options\n * @notation P:none F:none CB:none I:Templat..."}, {"name": "TemplateResult", "file": "src\\core\\tools\\templateProcessor.ts", "notation": "P:none F:none CB:none I:TemplateResult DB:templates", "block": "/**\n * @I:TemplateResult - Template processing result\n * @notation P:none F:none CB:none I:TemplateR..."}, {"name": "string", "file": "src\\core\\tools\\templateProcessor.ts", "notation": "P:template,vars F:processSimpleTemplate CB:none I:string DB:none", "block": "/**\n * F:processSimpleTemplate - Process template with simple variable substitution\n * @notation P:t..."}, {"name": "string", "file": "src\\core\\tools\\templateProcessor.ts", "notation": "P:template,vars F:processMustacheTemplate CB:none I:string DB:none", "block": "/**\n * F:processMustacheTemplate - Process template with Mustache-like syntax\n * @notation P:templat..."}, {"name": "object", "file": "src\\core\\tools\\templateProcessor.ts", "notation": "P:template F:detectAINotation CB:none I:object DB:none", "block": "/**\n * F:detectAINotation - Detect AI notation patterns in template\n * @notation P:template F:detect..."}, {"name": "TemplateResult", "file": "src\\core\\tools\\templateProcessor.ts", "notation": "P:template,vars,engine F:processTemplate CB:processTemplate I:TemplateResult DB:templates", "block": "/**\n * F:processTemplate - Process template with specified engine\n * @notation P:template,vars,engin..."}, {"name": "TemplateResult", "file": "src\\core\\tools\\templateProcessor.ts", "notation": "P:template,vars,engine F:processMultiHandlerTemplate CB:processMultiHandlerTemplate I:TemplateResult DB:templates", "block": "/**\n * F:processMultiHandlerTemplate - Main template processing function for handlers\n * @notation P..."}, {"name": "function", "file": "src\\core\\tools\\templateProcessor.ts", "notation": "P:options F:createTemplateProcessor CB:none I:function DB:templates", "block": "/**\n * F:createTemplateProcessor - Create template processor with default options\n * @notation P:opt..."}, {"name": "object", "file": "src\\core\\tools\\templateProcessor.ts", "notation": "P:template,engine F:validateTemplate CB:none I:object DB:templates", "block": "/**\n * F:validateTemplate - Validate template syntax\n * @notation P:template,engine F:validateTempla..."}, {"name": "string", "file": "src\\core\\tools\\templateProcessor.ts", "notation": "P:str F:escapeTemplateString CB:none I:string DB:none", "block": "/**\n * F:escapeTemplateString - Escape special characters in template string\n * @notation P:str F:es..."}, {"name": "string", "file": "src\\core\\tools\\templateProcessor.ts", "notation": "P:str F:unescapeTemplateString CB:none I:string DB:none", "block": "/**\n * F:unescapeTemplateString - Unescape special characters in template string\n * @notation P:str ..."}, {"name": "ToolchainConfig", "file": "src\\core\\tools\\toolchainOptimizer.ts", "notation": "P:core/tools/toolchainOptimizer F:executeToolchain,optimizeChaining CB:executeToolchain I:ToolchainConfig,ToolchainResult DB:toolchain", "block": "/**\n * Toolchain Optimizer - AI-Optimized Pure Functions\n * Optimizes BA → ANTI → TS → DB → REPORT e..."}, {"name": "ToolchainConfig", "file": "src\\core\\tools\\toolchainOptimizer.ts", "notation": "P:none F:none CB:none I:ToolchainConfig DB:toolchain", "block": "/**\n * @I:ToolchainConfig - Toolchain execution configuration\n * @notation P:none F:none CB:none I:T..."}, {"name": "ToolchainStage", "file": "src\\core\\tools\\toolchainOptimizer.ts", "notation": "P:none F:none CB:none I:ToolchainStage DB:toolchain", "block": "/**\n * @I:ToolchainStage - Individual toolchain stage\n * @notation P:none F:none CB:none I:Toolchain..."}, {"name": "ToolchainResult", "file": "src\\core\\tools\\toolchainOptimizer.ts", "notation": "P:none F:none CB:none I:ToolchainResult DB:toolchain", "block": "/**\n * @I:ToolchainResult - Complete toolchain execution result\n * @notation P:none F:none CB:none I..."}, {"name": "ToolchainCache", "file": "src\\core\\tools\\toolchainOptimizer.ts", "notation": "P:none F:none CB:none I:ToolchainCache DB:toolchain", "block": "/**\n * @I:ToolchainCache - Toolchain result cache\n * @notation P:none F:none CB:none I:ToolchainCach..."}, {"name": "ToolchainStage", "file": "src\\core\\tools\\toolchainOptimizer.ts", "notation": "P:pattern,config F:executeBA CB:executeBA I:ToolchainStage DB:toolchain", "block": "/**\n * F:executeBA - Execute Batch Analysis stage\n * @notation P:pattern,config F:executeBA CB:execu..."}, {"name": "ToolchainStage", "file": "src\\core\\tools\\toolchainOptimizer.ts", "notation": "P:b<PERSON><PERSON><PERSON><PERSON>,config F:executeANTI CB:executeANTI I:ToolchainStage DB:toolchain", "block": "/**\n * F:executeANTI - Execute Anti-Pattern Detection stage\n * @notation P:baResult,config F:execute..."}, {"name": "ToolchainStage", "file": "src\\core\\tools\\toolchainOptimizer.ts", "notation": "P:antiResult,config F:executeTS CB:executeTS I:ToolchainStage DB:toolchain", "block": "/**\n * F:executeTS - Execute TypeScript Validation stage\n * @notation P:antiResult,config F:executeT..."}, {"name": "ToolchainStage", "file": "src\\core\\tools\\toolchainOptimizer.ts", "notation": "P:ts<PERSON><PERSON><PERSON>,config F:executeDB CB:executeDB I:ToolchainStage DB:toolchain", "block": "/**\n * F:executeDB - Execute Database Validation stage\n * @notation P:tsResult,config F:executeDB CB..."}, {"name": "ToolchainStage", "file": "src\\core\\tools\\toolchainOptimizer.ts", "notation": "P:db<PERSON><PERSON><PERSON>,config F:executeREPORT CB:executeREPORT I:ToolchainStage DB:toolchain", "block": "/**\n * F:executeREPORT - Execute Report Generation stage\n * @notation P:dbResult,config F:executeREP..."}, {"name": "string", "file": "src\\core\\tools\\toolchainOptimizer.ts", "notation": "P:pattern,config F:getCache<PERSON>ey <PERSON>:none I:string DB:none", "block": "/**\n * F:getCacheKey - Generate cache key for toolchain execution\n * @notation P:pattern,config F:ge..."}, {"name": "unknown", "file": "src\\core\\tools\\toolchainOptimizer.ts", "notation": "P:key F:getCachedResult CB:none I:unknown DB:toolchain", "block": "/**\n * F:getCachedResult - Get cached toolchain result\n * @notation P:key F:getCachedResult CB:none ..."}, {"name": "void", "file": "src\\core\\tools\\toolchainOptimizer.ts", "notation": "P:key,result F:setCachedResult CB:none I:void DB:toolchain", "block": "/**\n * F:setCachedResult - Set cached toolchain result\n * @notation P:key,result F:setCachedResult C..."}, {"name": "ToolchainResult", "file": "src\\core\\tools\\toolchainOptimizer.ts", "notation": "P:config F:executeToolchain CB:executeToolchain I:ToolchainResult DB:toolchain", "block": "/**\n * F:executeToolchain - Execute complete toolchain pipeline\n * @notation P:config F:executeToolc..."}, {"name": "ToolchainConfig", "file": "src\\core\\tools\\toolchainOptimizer.ts", "notation": "P:config F:optimizeChaining CB:optimizeChaining I:ToolchainConfig DB:toolchain", "block": "/**\n * F:optimizeChaining - Optimize toolchain chaining based on runtime analysis\n * @notation P:con..."}, {"name": "all", "file": "src\\core\\types.ts", "notation": "P:core/types F:all CB:none I:all DB:none", "block": "/**\n * Core Types - AI-Optimized TypeScript\n * Machine-only types for AI consumption and mutation\n *..."}, {"name": "TaskType", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:TaskType DB:none", "block": "/**\n * @I:TaskType - AI notation task types for execution planning\n * @notation P:none F:none CB:non..."}, {"name": "ParameterType", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:ParameterType DB:none", "block": "/**\n * @I:ParameterType - Parameter type mapping for AI notation\n * @notation P:none F:none CB:none ..."}, {"name": "PARAMETER_KEY_MAP", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:PARAMETER_KEY_MAP DB:none", "block": "/**\n * @I:PARAMETER_KEY_MAP - Parameter key mapping for extraction\n * @notation P:none F:none CB:non..."}, {"name": "AgentStatus", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:AgentStatus DB:coordination", "block": "/**\n * @I:AgentStatus - Agent status types for coordination\n * @notation P:none F:none CB:none I:Age..."}, {"name": "MessageType", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:MessageType DB:coordination", "block": "/**\n * @I:MessageType - Message types for coordination\n * @notation P:none F:none CB:none I:MessageT..."}, {"name": "MessagePriority", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:MessagePriority DB:coordination", "block": "/**\n * @I:MessagePriority - Message priority levels\n * @notation P:none F:none CB:none I:MessagePrio..."}, {"name": "ExecutionPlanStatus", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:ExecutionPlanStatus DB:execution", "block": "/**\n * @I:ExecutionPlanStatus - Execution plan status types\n * @notation P:none F:none CB:none I:Exe..."}, {"name": "TransformationType", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:TransformationType DB:feedback", "block": "/**\n * @I:TransformationType - Context transformation types\n * @notation P:none F:none CB:none I:Tra..."}, {"name": "RefinementType", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:RefinementType DB:feedback", "block": "/**\n * @I:RefinementType - Execution refinement types\n * @notation P:none F:none CB:none I:Refinemen..."}, {"name": "BatchAnalysisOptions", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:chunkSize_typedef_added I:BatchAnalysisOptions DB:analysis", "block": "/**\n * @I:BatchAnalysisOptions - Batch analysis configuration options\n * @notation P:none F:none CB:..."}, {"name": "CoordinationCommand", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:CoordinationCommand DB:coordination", "block": "/**\n * @I:CoordinationCommand - Coordination command structure\n * @notation P:none F:none CB:none I:..."}, {"name": "Agent", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:Agent DB:coordination", "block": "/**\n * @I:Agent - Agent structure for coordination\n * @notation P:none F:none CB:none I:Agent DB:coo..."}, {"name": "Message", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:Message DB:coordination", "block": "/**\n * @I:Message - Message structure for coordination\n * @notation P:none F:none CB:none I:Message ..."}, {"name": "WorkspaceResource", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:WorkspaceResource DB:coordination", "block": "/**\n * @I:WorkspaceResource - Workspace resource structure\n * @notation P:none F:none CB:none I:Work..."}, {"name": "AgentProposal", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:AgentProposal DB:coordination", "block": "/**\n * @I:AgentProposal - Agent proposal structure\n * @notation P:none F:none CB:none I:AgentProposa..."}, {"name": "FileCommand", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:FileCommand DB:file", "block": "/**\n * @I:FileCommand - File command structure\n * @notation P:none F:none CB:none I:FileCommand DB:f..."}, {"name": "FileResponse", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:FileResponse DB:file", "block": "/**\n * @I:FileResponse - File response structure\n * @notation P:none F:none CB:none I:FileResponse D..."}, {"name": "FileCommandOptions", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:FileCommandOptions DB:file", "block": "/**\n * @I:FileCommandOptions - File command options\n * @notation P:none F:none CB:none I:FileCommand..."}, {"name": "GitHubCommand", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:GitHubCommand DB:github", "block": "/**\n * @I:GitHubCommand - GitHub command structure\n * @notation P:none F:none CB:none I:GitHubComman..."}, {"name": "GitHubResponse", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:GitHubResponse DB:github", "block": "/**\n * @I:GitHubResponse - GitHub response structure\n * @notation P:none F:none CB:none I:GitHubResp..."}, {"name": "GitHubRateLimit", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:GitHubRateLimit DB:github", "block": "/**\n * @I:GitHubRateLimit - GitHub rate limit information\n * @notation P:none F:none CB:none I:GitHu..."}, {"name": "DatabaseCommand", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:DatabaseCommand DB:database", "block": "/**\n * @I:DatabaseCommand - Database command structure\n * @notation P:none F:none CB:none I:Database..."}, {"name": "DatabaseResponse", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:DatabaseResponse DB:database", "block": "/**\n * @I:DatabaseResponse - Database response structure\n * @notation P:none F:none CB:none I:Databa..."}, {"name": "DatabaseCommandOptions", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:DatabaseCommandOptions DB:database", "block": "/**\n * @I:DatabaseCommandOptions - Database command options\n * @notation P:none F:none CB:none I:Dat..."}, {"name": "AINotationResponse", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:AINotationResponse DB:ai", "block": "/**\n * @I:AINotationResponse - AI notation response structure\n * @notation P:none F:none CB:none I:A..."}, {"name": "TaskConfig", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:TaskConfig DB:execution", "block": "/**\n * @I:TaskConfig - Immutable task configuration\n * @notation P:none F:none CB:none I:TaskConfig ..."}, {"name": "ExecutionTask", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:ExecutionTask DB:execution", "block": "/**\n * @I:ExecutionTask - Immutable execution task\n * @notation P:none F:none CB:none I:ExecutionTas..."}, {"name": "ExecutionPlan", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:ExecutionPlan DB:execution", "block": "/**\n * @I:ExecutionPlan - Immutable execution plan\n * @notation P:none F:none CB:none I:ExecutionPla..."}, {"name": "ExecutionContext", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:ExecutionContext DB:execution", "block": "/**\n * @I:ExecutionContext - Execution context (mutable for compatibility)\n * @notation P:none F:non..."}, {"name": "ToolExecutionResult", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:ToolExecutionResult DB:execution", "block": "/**\n * @I:ToolExecutionResult - Tool execution result\n * @notation P:none F:none CB:none I:ToolExecu..."}, {"name": "ContextTransformation", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:ContextTransformation DB:feedback", "block": "/**\n * @I:ContextTransformation - Context transformation\n * @notation P:none F:none CB:none I:Contex..."}, {"name": "ExecutionRefinement", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:ExecutionRefinement DB:feedback", "block": "/**\n * @I:ExecutionRefinement - Execution refinement\n * @notation P:none F:none CB:none I:ExecutionR..."}, {"name": "FeedbackContext", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:FeedbackContext DB:feedback", "block": "/**\n * @I:FeedbackContext - Feedback context\n * @notation P:none F:none CB:none I:FeedbackContext DB..."}, {"name": "ActionConfig", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:ActionConfig DB:actions", "block": "/**\n * @I:ActionConfig - Action configuration\n * @notation P:none F:none CB:none I:ActionConfig DB:a..."}, {"name": "RegistryMetrics", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:RegistryMetrics DB:metrics", "block": "/**\n * @I:RegistryMetrics - Registry metrics\n * @notation P:none F:none CB:none I:RegistryMetrics DB..."}, {"name": "ValidationConfig", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:ValidationConfig DB:validation", "block": "/**\n * @I:ValidationConfig - Validation configuration\n * @notation P:none F:none CB:none I:Validatio..."}, {"name": "PatternAnalysisConfig", "file": "src\\core\\types.ts", "notation": "P:none F:none CB:none I:PatternAnalysisConfig DB:analysis", "block": "/**\n * @I:PatternAnalysisConfig - Pattern analysis configuration\n * @notation P:none F:none CB:none ..."}, {"name": "Database", "file": "src\\index.ts", "notation": "P:none F:initDB CB:none I:Database DB:agent", "block": "/**\n * F:initDB - Initialize SQLite database for agent system\n * @notation P:none F:initDB CB:none I..."}, {"name": "void", "file": "src\\runtime\\launch.ts", "notation": "P:runtime/launch F:launchServer,initializeAgentSystem CB:launchServer I:void DB:agent", "block": "/**\n * Runtime Launch - Agent-Enhanced MCP Server Launcher\n * Initializes agent system and launches ..."}, {"name": "string", "file": "src\\runtime\\launch.ts", "notation": "P:startDir F:findWorkspaceRoot CB:none I:string DB:none", "block": "/**\n * F:findWorkspaceRoot - Find workspace root directory\n * @notation P:startDir F:findWorkspaceRo..."}, {"name": "object", "file": "src\\runtime\\launch.ts", "notation": "P:workspaceRoot F:getSimpleConfig CB:none I:object DB:none", "block": "/**\n * F:getSimpleConfig - Get simplified configuration\n * @notation P:workspaceRoot F:getSimpleConf..."}, {"name": "void", "file": "src\\runtime\\launch.ts", "notation": "P:workspaceRoot,config F:initializeAgentSystemAsync CB:initializeAgentSystemAsync I:void DB:agent", "block": "/**\n * F:initializeAgentSystemAsync - Initialize agent system asynchronously\n * @notation P:workspac..."}, {"name": "MCPCommand", "file": "src\\runtime\\router.ts", "notation": "P:runtime/router F:buildRouter,executeAgentCommand CB:buildRouter I:MCPCommand,AgentResult DB:agent", "block": "/**\n * Runtime Router - Agent-Centric Architecture\n * Migrated from legacy handler routing to unifie..."}, {"name": "function", "file": "src\\runtime\\router.ts", "notation": "P:db F:buildRouter CB:buildRouter I:function DB:agent", "block": "/**\n * F:buildRouter - Build agent-centric router with legacy compatibility\n * @notation P:db F:buil..."}, {"name": "AgentExecutionResult", "file": "src\\runtime\\router.ts", "notation": "P:command,payload F:executeCompleteAgentWorkflow CB:executeCompleteAgentWorkflow I:AgentExecutionResult DB:agent", "block": "/**\n   * F:executeCompleteAgentWorkflow - COMPLETE agent workflow execution (ONLY execution path)\n  ..."}, {"name": "TaskType", "file": "src\\runtime\\router.ts", "notation": "P:command F:mapCommandToTaskType CB:none I:TaskType DB:none", "block": "/**\n   * F:mapCommandToTaskType - Map MCP command to agent task type with enhanced MCP patterns\n   *..."}, {"name": "AgentExecutionResult", "file": "src\\runtime\\router.ts", "notation": "P:command,payload F:executeAgentValidatedCommand CB:executeAgentValidatedCommand I:AgentExecutionResult DB:agent", "block": "/**\n   * F:executeAgentValidatedCommand - Execute command through agent validation pipeline with gra..."}, {"name": "MCPCommand", "file": "src\\runtime\\server.ts", "notation": "P:runtime/server F:processInput,handleError CB:processInput I:MCPCommand,AgentExecutionResult DB:agent", "block": "/**\n * Runtime Server - Agent-Centric MCP Server\n * Migrated to use agent-enhanced routing with inte..."}, {"name": "Database", "file": "src\\runtime\\server.ts", "notation": "P:none F:initDB CB:none I:Database DB:agent", "block": "/**\n * F:initDB - Initialize SQLite database for agent system\n * @notation P:none F:initDB CB:none I..."}, {"name": "object", "file": "src\\runtime\\server.ts", "notation": "P:none F:initializeAgentEnhancedServer CB:initializeAgentEnhancedServer I:object DB:agent", "block": "/**\n * F:initializeAgentEnhancedServer - Initialize server with full agent orchestration\n * @notatio..."}]