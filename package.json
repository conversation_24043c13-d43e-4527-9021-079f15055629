{"name": "augster_mvp_scaffold", "version": "1.0.0", "description": "", "main": "dist/index.js", "type": "commonjs", "engines": {"node": ">=18.0.0"}, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "validate:env": "node -e \"['.env.dev','.env.memory'].forEach(f=>{if(!require('fs').existsSync(f))console.warn('⚠️ Missing',f)})\"", "setup": "npm install && npm run validate:env && npm run build", "mcp:dev": "cross-env NODE_ENV=development ts-node src/runtime/launch.ts", "mcp:prod": "cross-env NODE_ENV=production node dist/runtime/launch.js", "mcp:compiled": "npm run build && npm run mcp:prod -- --compiled", "profile:dev": "npx appmap-node npm run mcp:dev", "profile:prod": "npx appmap-node npm run mcp:prod", "start:app": "cross-env NODE_ENV=development ts-node src/index.ts", "start:mcp": "npm run mcp:dev", "start:dev": "concurrently \"npm run start:app\" \"npm run start:mcp\"", "start:prod": "npm run mcp:prod && node dist/runtime/server.js", "ollama:serve": "cross-env OLLAMA_HOST=0.0.0.0:11434 ollama serve", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "format": "prettier --write .", "check-format": "prettier --check ."}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@types/node": "^24.0.0", "@types/uuid": "^10.0.0", "ajv-formats": "^3.0.1", "concurrently": "^8.2.2", "dotenv": "^16.5.0", "fs-extra": "^11.3.0", "sqlite3": "^5.1.7", "ts-node": "^10.9.2", "typescript": "^5.8.3", "uuid": "^10.0.0"}, "devDependencies": {"@types/dotenv": "^8.2.3", "@types/fs-extra": "^11.0.4", "@types/glob": "^8.1.0", "@types/jest": "^29.5.12", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "ajv": "^8.17.1", "chalk": "^5.4.1", "cross-env": "^7.0.3", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "fast-glob": "^3.3.3", "jest": "^29.7.0", "jest-junit": "^16.0.0", "jsonc-parser": "^3.3.1", "prettier": "^3.5.3", "rimraf": "^6.0.1", "ts-jest": "^29.1.2", "ts-morph": "^26.0.0", "zod": "^3.25.67"}}