/**
 * Execution Planner Core - Types, Task Creation, and Dependency Graph
 * @notation P:core/tools/agent/planner-core F:createExecutionTask,buildDependencyGraph CB:plannerCore I:ExecutionTask,TaskDependencyGraph DB:execution
 */

import {
  TaskType,
  PARAMETER_KEY_MAP,
  ExecutionPlanStatus,
  ExecutionTask,
  ExecutionPlan,
  ExecutionContext,
  TaskConfig,
  AINotationMetadata
} from '../../types'

export type PlanningOptions = {
  readonly maxParallelTasks: number
  readonly prioritizeByDuration: boolean
  readonly enableOptimization: boolean
  readonly validateDependencies: boolean
}

export type PlanningResult = {
  readonly success: boolean
  readonly plan: ExecutionPlan | null
  readonly error?: string
  readonly optimizations: readonly string[]
  readonly warnings: readonly string[]
  readonly timestamp: number
  readonly processingTime: number
}

export type TaskDependencyGraph = {
  readonly nodes: ReadonlyMap<string, ExecutionTask>
  readonly edges: ReadonlyMap<string, readonly string[]>
  readonly roots: readonly string[]
  readonly leaves: readonly string[]
}

export const DEFAULT_PLANNING_OPTIONS: PlanningOptions = Object.freeze({
  maxParallelTasks: 4,
  prioritizeByDuration: true,
  enableOptimization: true,
  validateDependencies: true
})

const TASK_CONFIGS = new Map<TaskType, TaskConfig>([
  [
    'file-operation',
    Object.freeze({
      estimatedDuration: 2000,
      maxRetries: 3,
      timeout: 30000,
      requiredParameters: ['path', 'operation'],
      optionalParameters: ['content', 'encoding']
    })
  ],
  [
    'database-query',
    Object.freeze({
      estimatedDuration: 1000,
      maxRetries: 2,
      timeout: 15000,
      requiredParameters: ['query'],
      optionalParameters: ['parameters', 'transaction']
    })
  ],
  [
    'api-request',
    Object.freeze({
      estimatedDuration: 3000,
      maxRetries: 3,
      timeout: 45000,
      requiredParameters: ['url', 'method'],
      optionalParameters: ['headers', 'body', 'timeout']
    })
  ],
  [
    'validation',
    Object.freeze({
      estimatedDuration: 500,
      maxRetries: 1,
      timeout: 10000,
      requiredParameters: ['target'],
      optionalParameters: ['rules', 'strict']
    })
  ]
])

/**
 * F:createExecutionTask - Create execution task with validation
 * @notation P:id,type,parameters F:createExecutionTask CB:taskCreation I:ExecutionTask DB:execution
 */
export const createExecutionTask = (
  id: string,
  type: TaskType,
  parameters: Record<string, unknown> = {},
  dependencies: readonly string[] = [],
  metadata: AINotationMetadata = {}
): ExecutionTask => {
  const config = TASK_CONFIGS.get(type)
  if (!config) {
    throw new Error(`Unknown task type: ${type}`)
  }

  // Validate required parameters
  const missingParams = config.requiredParameters.filter(param => !(param in parameters))
  if (missingParams.length > 0) {
    throw new Error(`Missing required parameters for ${type}: ${missingParams.join(', ')}`)
  }

  return Object.freeze({
    id,
    type,
    parameters: Object.freeze({ ...parameters }),
    dependencies: Object.freeze([...dependencies]),
    status: 'pending',
    estimatedDuration: config.estimatedDuration,
    maxRetries: config.maxRetries,
    timeout: config.timeout,
    metadata: Object.freeze({ ...metadata }),
    createdAt: Date.now(),
    updatedAt: Date.now()
  })
}

/**
 * F:buildDependencyGraph - Build task dependency graph
 * @notation P:tasks F:buildDependencyGraph CB:dependencyGraph I:TaskDependencyGraph DB:execution
 */
export const buildDependencyGraph = (tasks: readonly ExecutionTask[]): TaskDependencyGraph => {
  const nodes = new Map<string, ExecutionTask>()
  const edges = new Map<string, readonly string[]>()
  const incomingEdges = new Map<string, number>()

  // Build nodes and edges
  for (const task of tasks) {
    nodes.set(task.id, task)
    edges.set(task.id, task.dependencies)
    
    // Count incoming edges for each node
    if (!incomingEdges.has(task.id)) {
      incomingEdges.set(task.id, 0)
    }
    
    for (const dep of task.dependencies) {
      incomingEdges.set(dep, (incomingEdges.get(dep) || 0) + 1)
    }
  }

  // Find root nodes (no incoming edges)
  const roots = Array.from(nodes.keys()).filter(id => (incomingEdges.get(id) || 0) === 0)
  
  // Find leaf nodes (no outgoing edges)
  const leaves = Array.from(nodes.keys()).filter(id => {
    const deps = edges.get(id) || []
    return deps.length === 0
  })

  return Object.freeze({
    nodes: new Map(nodes),
    edges: new Map(edges),
    roots: Object.freeze(roots),
    leaves: Object.freeze(leaves)
  })
}

/**
 * F:getTaskConfig - Get task configuration by type
 * @notation P:type F:getTaskConfig CB:taskConfig I:TaskConfig DB:execution
 */
export const getTaskConfig = (type: TaskType): TaskConfig | undefined => {
  return TASK_CONFIGS.get(type)
}

/**
 * F:validateTaskParameters - Validate task parameters against config
 * @notation P:task F:validateTaskParameters CB:parameterValidation I:boolean DB:execution
 */
export const validateTaskParameters = (task: ExecutionTask): { valid: boolean; errors: string[] } => {
  const config = TASK_CONFIGS.get(task.type)
  if (!config) {
    return { valid: false, errors: [`Unknown task type: ${task.type}`] }
  }

  const errors: string[] = []
  
  // Check required parameters
  for (const param of config.requiredParameters) {
    if (!(param in task.parameters)) {
      errors.push(`Missing required parameter: ${param}`)
    }
  }

  // Check for unknown parameters
  const allValidParams = [...config.requiredParameters, ...config.optionalParameters]
  for (const param of Object.keys(task.parameters)) {
    if (!allValidParams.includes(param)) {
      errors.push(`Unknown parameter: ${param}`)
    }
  }

  return { valid: errors.length === 0, errors }
}
