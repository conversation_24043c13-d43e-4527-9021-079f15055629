/**
 * Core Constants - Environment and Basic Configuration
 * @notation P:core/constants/core-constants F:all CB:none I:all DB:none
 */

export const ENV_DEVELOPMENT = {
  NODE_ENV: 'development',
  MCP_PORT: '8082',
  MCP_DB_PATH: '.augment/db/augster-dev.db',
  LOG_LEVEL: 'debug',
  LOG_FILE: '.augment/logs/mcp-dev.log'
} as const

export const ENV_PRODUCTION = {
  NODE_ENV: 'production',
  MCP_PORT: '8081',
  MCP_DB_PATH: '.augment/db/augster.db',
  LOG_LEVEL: 'info',
  LOG_FILE: '.augment/logs/mcp-prod.log'
} as const

export const AI_SYMBOLS = {
  '🔎': 'EXTRACT',
  '⚡': 'IMPLEMENT',
  '🧪': 'TEST',
  '✅': 'VALIDATE',
  '📝': 'DOCUMENT',
  '🔧': 'CONFIGURE',
  '🚀': 'DEPLOY',
  '🛑': 'STOP',
  '📊': 'MEASURE',
  '🔄': 'COORDINATE',
  '💾': 'COMPILE'
} as const

export const NOTATION_PREFIXES = {
  P: 'PARAMETER',
  F: 'FUNCTION',
  CB: 'CIRCUIT_BREAKER',
  I: 'INTERFACE',
  S: 'SCHEMA',
  DB: 'DATABASE'
} as const

export const DEFAULT_CHUNK_SIZE = 8192
export const MAX_METRICS = 10000
export const DEFAULT_TIMEOUT = 30000

export const MIN_PORT = 1024
export const MAX_PORT = 65535

export const REQUIRED_DIRECTORIES = Object.freeze([
  '.augment/db',
  '.augment/logs',
  '.augment/config',
  '.augment/cache'
] as const)

export const LOG_LEVELS = Object.freeze(['debug', 'info', 'warn', 'error'] as const)

export const SUPPORTED_FILE_EXTENSIONS = Object.freeze([
  '.ts',
  '.js',
  '.json',
  '.md',
  '.txt',
  '.log'
] as const)

export const TEMPLATE_ENGINES = Object.freeze(['simple', 'mustache'] as const)

export const TRANSFORMATION_VERSION = '1.0.0'
export const MANIFEST_SCHEMA_VERSION = '1.0.0'

export const SYMBOL_TYPES = Object.freeze([
  'function',
  'class',
  'interface',
  'type',
  'variable',
  'constant'
] as const)

export const NOTATION_SYMBOLS = Object.freeze(['P', 'F', 'CB', 'I', 'S', 'DB'])

export const SCHEMA_PATHS = {
  memory: 'src/core/schema/memory.schema.json',
  file: 'src/core/schema/file.schema.json',
  database: 'src/core/schema/database.schema.json',
  github: 'src/core/schema/github.schema.json',
  monitoring: 'src/core/schema/monitoring.schema.json',
  coordination: 'src/core/schema/coordination.schema.json',
  fetch: 'src/core/schema/fetch.schema.json',
  time: 'src/core/schema/time.schema.json',
  git: 'src/core/schema/git.schema.json',
  terminal: 'src/core/schema/terminal.schema.json',
  enhancedTool: 'src/core/schema/enhancedTool.schema.json'
} as const

export const SYMBOL_REGEX =
  /(?:🔎|EXTRACT|⚡|IMPLEMENT|🧪|TEST|✅|VALIDATE|📝|DOCUMENT|🔧|CONFIGURE|🚀|DEPLOY|🛑|STOP|📊|MEASURE|🔄|COORDINATE|💾|COMPILE)/

export const PARAM_REGEX = /([PMCBISDHAT]):([\w\-\.\/\*]+)/g
export const IMPORT_PATTERN = /^\s*import\s+.*from\s+['"`]([^'"`]+)['"`]/
export const RELATIVE_IMPORT_PATTERN = /^\./
export const TYPESCRIPT_DECLARATION_PATTERN = /\.d\.ts$/

export const EXCLUDED_PATTERNS = Object.freeze(['node_modules', '/dist/', '.d.ts'])
export const EXCLUDED_DIRECTORIES = Object.freeze(['node_modules', 'dist', '.git', 'coverage'])
