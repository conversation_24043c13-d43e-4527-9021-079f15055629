/**
 * Execution Planner Workflow - Sequence Generation, Plan Creation, and Optimization
 * Split from executionPlanner.ts for constraint compliance (≤150 lines)
 *
 * @notation P:core/tools/agent/planner-workflow F:generateExecutionSequence,createExecutionPlan,optimizeExecutionPlan CB:plannerWorkflow I:ExecutionPlan DB:execution
 */

import { ExecutionTask, ExecutionPlan, ExecutionPlanStatus } from '../../types'
import { PlanningOptions, PlanningResult, TaskDependencyGraph, DEFAULT_PLANNING_OPTIONS, buildDependencyGraph } from './planner-core'
import { validateDependencies } from './planner-validation'

/**
 * F:generateExecutionSequence - Generate optimal execution sequence from tasks
 * @notation P:tasks,options F:generateExecutionSequence CB:sequenceGeneration I:ExecutionTask[][] DB:execution
 */
export const generateExecutionSequence = (
  tasks: readonly ExecutionTask[],
  options: PlanningOptions = DEFAULT_PLANNING_OPTIONS
): readonly (readonly ExecutionTask[])[] => {
  if (tasks.length === 0) {
    return []
  }

  const graph = buildDependencyGraph(tasks)
  const sequence: ExecutionTask[][] = []
  const completed = new Set<string>()
  const remaining = new Map(graph.nodes)

  while (remaining.size > 0) {
    // Find tasks that can be executed (all dependencies completed)
    const ready: ExecutionTask[] = []
    
    for (const [taskId, task] of remaining) {
      const canExecute = task.dependencies.every(dep => completed.has(dep))
      if (canExecute) {
        ready.push(task)
      }
    }

    if (ready.length === 0) {
      // No tasks can be executed - likely circular dependency
      const remainingIds = Array.from(remaining.keys())
      throw new Error(`Cannot generate execution sequence. Remaining tasks: ${remainingIds.join(', ')}`)
    }

    // Sort ready tasks by priority if enabled
    if (options.prioritizeByDuration) {
      ready.sort((a, b) => (b.estimatedDuration || 0) - (a.estimatedDuration || 0))
    }

    // Limit parallel execution
    const batch = ready.slice(0, options.maxParallelTasks)
    sequence.push(Object.freeze([...batch]))

    // Mark batch tasks as completed and remove from remaining
    for (const task of batch) {
      completed.add(task.id)
      remaining.delete(task.id)
    }
  }

  return Object.freeze(sequence.map(batch => Object.freeze(batch)))
}

/**
 * F:createExecutionPlan - Create execution plan from tasks
 * @notation P:sessionId,tasks,options F:createExecutionPlan CB:planCreation I:PlanningResult DB:execution
 */
export const createExecutionPlan = (
  sessionId: string,
  tasks: readonly ExecutionTask[],
  options: PlanningOptions = DEFAULT_PLANNING_OPTIONS
): PlanningResult => {
  const startTime = Date.now()
  const optimizations: string[] = []
  const warnings: string[] = []

  try {
    // Validate dependencies if enabled
    if (options.validateDependencies) {
      const validation = validateDependencies(tasks)
      if (!validation.valid) {
        return Object.freeze({
          success: false,
          plan: null,
          error: `Dependency validation failed: ${validation.errors.join(', ')}`,
          optimizations: Object.freeze(optimizations),
          warnings: Object.freeze([...warnings, ...validation.warnings]),
          timestamp: Date.now(),
          processingTime: Date.now() - startTime
        })
      }
      warnings.push(...validation.warnings)
    }

    // Generate execution sequence
    const executionSequence = generateExecutionSequence(tasks, options)
    
    // Calculate estimated duration
    const estimatedDuration = executionSequence.reduce((total, batch) => {
      const batchDuration = Math.max(...batch.map(task => task.estimatedDuration || 0))
      return total + batchDuration
    }, 0)

    // Apply optimizations if enabled
    let finalSequence = executionSequence
    if (options.enableOptimization) {
      // Optimization: Merge small batches
      if (executionSequence.length > 1) {
        const optimizedSequence: ExecutionTask[][] = []
        let currentBatch: ExecutionTask[] = []

        for (const batch of executionSequence) {
          if (currentBatch.length + batch.length <= options.maxParallelTasks) {
            currentBatch.push(...batch)
          } else {
            if (currentBatch.length > 0) {
              optimizedSequence.push([...currentBatch])
            }
            currentBatch = [...batch]
          }
        }

        if (currentBatch.length > 0) {
          optimizedSequence.push(currentBatch)
        }

        if (optimizedSequence.length < executionSequence.length) {
          finalSequence = Object.freeze(optimizedSequence.map(batch => Object.freeze(batch)))
          optimizations.push(`Merged ${executionSequence.length - optimizedSequence.length} batches`)
        }
      }
    }

    const plan: ExecutionPlan = Object.freeze({
      id: `plan_${sessionId}_${Date.now()}`,
      sessionId,
      tasks: Object.freeze([...tasks]),
      executionSequence: finalSequence,
      status: 'pending' as ExecutionPlanStatus,
      estimatedDuration,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      metadata: Object.freeze({
        planningOptions: options,
        taskCount: tasks.length,
        batchCount: finalSequence.length,
        optimizationsApplied: optimizations.length
      })
    })

    return Object.freeze({
      success: true,
      plan,
      optimizations: Object.freeze(optimizations),
      warnings: Object.freeze(warnings),
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })

  } catch (error) {
    return Object.freeze({
      success: false,
      plan: null,
      error: (error as Error).message,
      optimizations: Object.freeze(optimizations),
      warnings: Object.freeze(warnings),
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  }
}

/**
 * F:optimizeExecutionPlan - Optimize existing execution plan
 * @notation P:plan,options F:optimizeExecutionPlan CB:planOptimization I:ExecutionPlan DB:execution
 */
export const optimizeExecutionPlan = (
  plan: ExecutionPlan,
  options: PlanningOptions = DEFAULT_PLANNING_OPTIONS
): ExecutionPlan => {
  // Re-generate sequence with new options
  const newSequence = generateExecutionSequence(plan.tasks, options)
  
  // Calculate new estimated duration
  const newEstimatedDuration = newSequence.reduce((total, batch) => {
    const batchDuration = Math.max(...batch.map(task => task.estimatedDuration || 0))
    return total + batchDuration
  }, 0)

  return Object.freeze({
    ...plan,
    executionSequence: newSequence,
    estimatedDuration: newEstimatedDuration,
    updatedAt: Date.now(),
    metadata: Object.freeze({
      ...plan.metadata,
      planningOptions: options,
      batchCount: newSequence.length,
      lastOptimized: Date.now()
    })
  })
}

/**
 * F:estimatePlanDuration - Estimate total plan execution duration
 * @notation P:plan F:estimatePlanDuration CB:durationEstimation I:number DB:execution
 */
export const estimatePlanDuration = (plan: ExecutionPlan): number => {
  return plan.executionSequence.reduce((total, batch) => {
    const batchDuration = Math.max(...batch.map(task => task.estimatedDuration || 0))
    return total + batchDuration
  }, 0)
}

/**
 * F:getPlanStatistics - Get comprehensive plan statistics
 * @notation P:plan F:getPlanStatistics CB:planStatistics I:object DB:execution
 */
export const getPlanStatistics = (plan: ExecutionPlan) => {
  const totalTasks = plan.tasks.length
  const totalBatches = plan.executionSequence.length
  const avgBatchSize = totalBatches > 0 ? totalTasks / totalBatches : 0
  const maxBatchSize = Math.max(...plan.executionSequence.map(batch => batch.length))
  const minBatchSize = Math.min(...plan.executionSequence.map(batch => batch.length))

  const tasksByType = plan.tasks.reduce((acc, task) => {
    acc[task.type] = (acc[task.type] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return Object.freeze({
    totalTasks,
    totalBatches,
    avgBatchSize: Math.round(avgBatchSize * 100) / 100,
    maxBatchSize,
    minBatchSize,
    estimatedDuration: plan.estimatedDuration,
    tasksByType: Object.freeze(tasksByType),
    parallelizationRatio: totalBatches > 0 ? totalTasks / totalBatches : 0
  })
}
