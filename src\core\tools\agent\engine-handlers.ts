/**
 * Agent Engine Handlers - Handler Registry and Command Execution
 * @notation P:core/tools/agent/engine-handlers F:initializeHandlerRegistry,executeHandlerCommand CB:handlerRegistry I:HandlerRegistry DB:handlers
 */

import { Database } from 'sqlite3'

/**
 * F:initializeHandlerRegistry - Initialize handler registry for direct MCP execution
 * @notation P:database F:initializeHandlerRegistry CB:initializeHandlerRegistry I:object DB:handlers
 */
export const initializeHandlerRegistry = async (database: Database | null) => {
  console.log('🔧 INITIALIZING HANDLER REGISTRY: Loading all MCP handlers')

  try {
    const { memoryHandler } = await import('../../handlers/memory')
    const { fileHandler } = await import('../../handlers/file')
    const { databaseHandler } = await import('../../handlers/database')
    const { githubHandler } = await import('../../handlers/github')
    const { monitoringHandler } = await import('../../handlers/monitoring')
    const { coordinationHandler } = await import('../../handlers/coordination')
    const { fetchHandler } = await import('../../handlers/fetch')
    const { timeHandler } = await import('../../handlers/time')
    const { gitHandler } = await import('../../handlers/git')
    const { terminalHandler } = await import('../../handlers/terminal')
    const { enhancedToolHandler } = await import('../../handlers/enhancedTool')

    const registry = Object.freeze({
      memory: database ? memoryHandler(database) : null,
      file: database ? fileHandler(database) : null,
      database: databaseHandler(),
      github: githubHandler(),
      monitoring: monitoringHandler(),
      coordination: database ? coordinationHandler(database) : null,
      fetch: fetchHandler(),
      time: database ? timeHandler(database) : null,
      git: gitHandler(),
      terminal: terminalHandler(),
      enhancedTool: database ? enhancedToolHandler(database) : null
    })

    console.log('✅ HANDLER REGISTRY: All handlers initialized')
    console.log(
      '📦 HANDLER REGISTRY: Available handlers:',
      Object.keys(registry).filter(k => registry[k as keyof typeof registry] !== null)
    )

    return registry
  } catch (error) {
    console.error('❌ HANDLER REGISTRY: Failed to initialize handlers:', (error as Error).message)
    return Object.freeze({})
  }
}

/**
 * F:executeHandlerCommand - Execute command through handler registry
 * @notation P:handlerRegistry,command,payload F:executeHandlerCommand CB:executeHandlerCommand I:unknown DB:handlers
 */
export const executeHandlerCommand = async (
  handlerRegistry: any,
  command: string,
  payload: any = {}
): Promise<any> => {
  try {
    console.log(`🔄 HANDLER COMMAND: Executing ${command}`)

    // Parse command to extract handler and action
    const [handlerName, action] = command.split('.')
    
    if (!handlerName || !action) {
      throw new Error(`Invalid command format: ${command}. Expected format: handler.action`)
    }

    const handler = handlerRegistry[handlerName]
    if (!handler) {
      throw new Error(`Handler not found: ${handlerName}`)
    }

    if (typeof handler.execute !== 'function') {
      throw new Error(`Handler ${handlerName} does not have execute method`)
    }

    // Execute the command with the payload
    const commandPayload = {
      action,
      ...payload
    }

    const result = await handler.execute(commandPayload)
    console.log(`✅ HANDLER COMMAND: ${command} executed successfully`)
    
    return result
  } catch (error) {
    console.error(`❌ HANDLER COMMAND: Failed to execute ${command}:`, (error as Error).message)
    throw error
  }
}

/**
 * F:validateHandlerRegistry - Validate handler registry completeness
 * @notation P:registry F:validateHandlerRegistry CB:validation I:boolean DB:handlers
 */
export const validateHandlerRegistry = (registry: any): boolean => {
  const requiredHandlers = [
    'memory', 'file', 'database', 'github', 'monitoring', 
    'coordination', 'fetch', 'time', 'git', 'terminal', 'enhancedTool'
  ]

  const availableHandlers = Object.keys(registry).filter(
    key => registry[key] !== null && registry[key] !== undefined
  )

  const missingHandlers = requiredHandlers.filter(
    handler => !availableHandlers.includes(handler)
  )

  if (missingHandlers.length > 0) {
    console.warn('⚠️ HANDLER REGISTRY: Missing handlers:', missingHandlers)
    return false
  }

  console.log('✅ HANDLER REGISTRY: All required handlers available')
  return true
}

/**
 * F:getHandlerStatus - Get status of all handlers in registry
 * @notation P:registry F:getHandlerStatus CB:status I:object DB:handlers
 */
export const getHandlerStatus = (registry: any) => {
  const status = Object.keys(registry).reduce((acc, handlerName) => {
    const handler = registry[handlerName]
    acc[handlerName] = {
      available: handler !== null && handler !== undefined,
      hasExecute: handler && typeof handler.execute === 'function',
      type: typeof handler
    }
    return acc
  }, {} as Record<string, any>)

  return Object.freeze(status)
}

/**
 * F:createHandlerProxy - Create proxy for handler command execution
 * @notation P:registry F:createHandlerProxy CB:proxy I:object DB:handlers
 */
export const createHandlerProxy = (registry: any) => {
  return new Proxy(registry, {
    get(target, prop) {
      const handlerName = String(prop)
      const handler = target[handlerName]
      
      if (!handler) {
        throw new Error(`Handler not found: ${handlerName}`)
      }

      return new Proxy(handler, {
        get(handlerTarget, actionProp) {
          const action = String(actionProp)
          
          if (action === 'execute') {
            return handlerTarget.execute
          }

          // Return a function that executes the action
          return async (payload: any = {}) => {
            return executeHandlerCommand(registry, `${handlerName}.${action}`, payload)
          }
        }
      })
    }
  })
}
