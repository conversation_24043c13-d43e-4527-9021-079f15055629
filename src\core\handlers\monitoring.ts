/**
 * Monitoring Handler - AI-Optimized Pure Functions
 * Migrated from BaseHandler class to pure function architecture
 *
 * @notation P:core/handlers/monitoring F:monitoring<PERSON>andler,executeMonitoringCommand CB:executeMonitoringCommand I:MonitoringCommand,MonitoringResult DB:monitoring
 */

import {
  createResilienceMonitor,
  getSystemHealthMetrics,
  type ResilienceMonitorInstance,
  type SystemHealthMetrics
} from '../antiPatterns/resilienceMonitor'
import {
  createCircuitBreakerRegistry,
  getAllCircuitBreakerMetrics,
  type CircuitBreakerMetrics
} from '../antiPatterns/circuitBreaker'
import {
  createRetryManagerRegistry,
  getAllRetryMetrics,
  type RetryMetrics
} from '../antiPatterns/retryManager'
import { processMultiHandlerTemplate } from '../tools'
import {
  HandlerConfig,
  OperationResult,
  createHandlerConfig,
  executeWithResilience
} from './executeCommand'

export type MonitoringCommand = {
  readonly action:
    | 'metrics'
    | 'health'
    | 'dashboard'
    | 'analytics'
    | 'handlers'
    | 'performance'
    | 'template'
  readonly path?: string
  readonly content?: string
  readonly options?: {
    readonly format?: 'json' | 'html' | 'csv'
    readonly templateEngine?: 'simple' | 'mustache'
    readonly templateVars?: Record<string, unknown>
    readonly templateOutputPath?: string
    readonly refresh?: boolean
  }
}

export type MonitoringResult = {
  readonly success: boolean
  readonly data?: unknown
  readonly error?: string
  readonly processingTime?: number
  readonly timestamp?: number
  readonly cacheHit?: boolean
}

/**
 * F:createMonitoringConfig - Create monitoring handler configuration
 * @notation P:none F:createMonitoringConfig CB:none I:HandlerConfig DB:none
 */
export const createMonitoringConfig = (): HandlerConfig => {
  return createHandlerConfig(
    'monitoring',
    ['metrics', 'health', 'dashboard', 'analytics', 'handlers', 'performance', 'template'],
    {
      failureThreshold: 5,
      recoveryTimeout: 30000,
      maxRetries: 3,
      baseDelay: 500
    }
  )
}

/**
 * F:validateMonitoringCommand - Validate monitoring command structure
 * @notation P:command F:validateMonitoringCommand CB:none I:boolean DB:none
 */
export const validateMonitoringCommand = (command: unknown): command is MonitoringCommand => {
  if (!command || typeof command !== 'object') return false

  const cmd = command as Record<string, unknown>

  return (
    typeof cmd.action === 'string' &&
    ['metrics', 'health', 'dashboard', 'analytics', 'handlers', 'performance', 'template'].includes(
      cmd.action
    )
  )
}

const CACHE_TTL = 30000

export type DashboardMetrics = {
  readonly systemHealth: {
    readonly status: 'HEALTHY' | 'DEGRADED' | 'CRITICAL'
    readonly uptime: number
    readonly availabilityPercent: number
    readonly timestamp: number
  }
  readonly circuitBreakers: Record<
    string,
    {
      readonly state: string
      readonly failureCount: number
      readonly successCount: number
      readonly lastFailureTime: number
      readonly failureRate: number
    }
  >
  readonly retryManagers: Record<
    string,
    {
      readonly totalAttempts: number
      readonly successfulRetries: number
      readonly failedRetries: number
      readonly averageDelay: number
      readonly successRate: number
    }
  >
  readonly operationMetrics: {
    readonly memory: { operations: number; avgResponseTime: number; errorRate: number }
    readonly file: { operations: number; avgResponseTime: number; errorRate: number }
    readonly github: { operations: number; avgResponseTime: number; errorRate: number }
  }
  readonly recentEvents: Array<{
    timestamp: number
    component: string
    type: string
    success: boolean
    details: string
  }>
}

let metricsCache: { data: DashboardMetrics | null; timestamp: number } = {
  data: null,
  timestamp: 0
}

/**
 * F:executeMonitoringMetrics - Execute monitoring metrics operation
 * @notation P:options F:executeMonitoringMetrics CB:executeMonitoringMetrics I:MonitoringResult DB:monitoring
 */
export const executeMonitoringMetrics = async (
  options: Record<string, unknown> = {}
): Promise<MonitoringResult> => {
  const startTime = Date.now()

  try {
    const now = Date.now()
    if (!options.refresh && metricsCache.data && now - metricsCache.timestamp < CACHE_TTL) {
      return Object.freeze({
        success: true,
        data: metricsCache.data,
        processingTime: Date.now() - startTime,
        timestamp: now,
        cacheHit: true
      })
    }

    const resilienceMonitor = createResilienceMonitor()
    const circuitBreakerRegistry = createCircuitBreakerRegistry()
    const retryManagerRegistry = createRetryManagerRegistry()

    const circuitBreakerMetrics = getAllCircuitBreakerMetrics(circuitBreakerRegistry)
    const retryManagerMetrics = getAllRetryMetrics(retryManagerRegistry)

    const systemHealthMetrics = getSystemHealthMetrics(
      resilienceMonitor,
      circuitBreakerMetrics,
      retryManagerMetrics
    )

    const operationMetrics = {
      memory: { operations: 0, avgResponseTime: 0, errorRate: 0 },
      file: { operations: 0, avgResponseTime: 0, errorRate: 0 },
      github: { operations: 0, avgResponseTime: 0, errorRate: 0 }
    }

    const dashboardMetrics: DashboardMetrics = Object.freeze({
      systemHealth: Object.freeze({
        status: systemHealthMetrics.systemStatus,
        uptime: systemHealthMetrics.uptime,
        availabilityPercent: systemHealthMetrics.availabilityPercent,
        timestamp: systemHealthMetrics.timestamp
      }),
      circuitBreakers: {},
      retryManagers: {},
      operationMetrics: Object.freeze(operationMetrics),
      recentEvents: systemHealthMetrics.errorRecoveryEvents.slice(-20).map(event =>
        Object.freeze({
          timestamp: event.timestamp,
          component: event.component,
          type: event.errorType,
          success: event.success,
          details: event.details || 'No details available'
        })
      )
    })

    const circuitBreakers: Record<string, unknown> = {}
    Object.entries(systemHealthMetrics.circuitBreakers).forEach(([name, metrics]) => {
      const cbMetrics = metrics as CircuitBreakerMetrics
      const totalCalls = (cbMetrics as any).totalCalls || 1
      const failureRate = totalCalls > 0 ? (cbMetrics.failureCount / totalCalls) * 100 : 0

      circuitBreakers[name] = Object.freeze({
        state: cbMetrics.state,
        failureCount: cbMetrics.failureCount,
        successCount: cbMetrics.successCount,
        lastFailureTime: cbMetrics.lastFailureTime,
        failureRate: Math.round(failureRate * 100) / 100
      })
    })

    const retryManagers: Record<string, unknown> = {}
    Object.entries(systemHealthMetrics.retryManagers).forEach(([name, metrics]) => {
      const retryMetrics = metrics as RetryMetrics
      const successRate =
        retryMetrics.totalAttempts > 0
          ? ((retryMetrics.totalAttempts - retryMetrics.failedRetries) /
              retryMetrics.totalAttempts) *
            100
          : 100

      retryManagers[name] = Object.freeze({
        totalAttempts: retryMetrics.totalAttempts,
        successfulRetries: retryMetrics.successfulRetries,
        failedRetries: retryMetrics.failedRetries,
        averageDelay: retryMetrics.averageDelay,
        successRate: successRate
      })
    })

    const recentEvents = systemHealthMetrics.errorRecoveryEvents.slice(-100)
    const operationStats = {
      memory: { total: 0, successful: 0, totalTime: 0 },
      file: { total: 0, successful: 0, totalTime: 0 },
      github: { total: 0, successful: 0, totalTime: 0 }
    }

    recentEvents.forEach(event => {
      const component = event.component.split('-')[0] as keyof typeof operationStats
      if (operationStats[component]) {
        operationStats[component].total++
        operationStats[component].totalTime += event.recoveryTime
        if (event.success) {
          operationStats[component].successful++
        }
      }
    })

    Object.entries(operationStats).forEach(([key, stats]) => {
      const component = key as keyof typeof operationMetrics
      operationMetrics[component] = {
        operations: stats.total,
        avgResponseTime: stats.total > 0 ? Math.round(stats.totalTime / stats.total) : 0,
        errorRate:
          stats.total > 0 ? Math.round(((stats.total - stats.successful) / stats.total) * 100) : 0
      }
    })

    const finalMetrics = Object.freeze({
      ...dashboardMetrics,
      circuitBreakers: Object.freeze(circuitBreakers),
      retryManagers: Object.freeze(retryManagers),
      operationMetrics: Object.freeze(operationMetrics)
    })

    metricsCache = {
      data: finalMetrics as DashboardMetrics,
      timestamp: now
    }

    return Object.freeze({
      success: true,
      data: finalMetrics,
      processingTime: Date.now() - startTime,
      timestamp: now,
      cacheHit: false
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: `Failed to get system metrics: ${(error as Error).message}`,
      processingTime: Date.now() - startTime,
      timestamp: Date.now()
    })
  }
}

/**
 * F:executeMonitoringHealth - Execute monitoring health operation
 * @notation P:options F:executeMonitoringHealth CB:executeMonitoringHealth I:MonitoringResult DB:monitoring
 */
export const executeMonitoringHealth = async (): Promise<MonitoringResult> => {
  const startTime = Date.now()

  try {
    const resilienceMonitor = createResilienceMonitor()
    const circuitBreakerRegistry = createCircuitBreakerRegistry()
    const retryManagerRegistry = createRetryManagerRegistry()

    const circuitBreakerMetrics = getAllCircuitBreakerMetrics(circuitBreakerRegistry)
    const retryManagerMetrics = getAllRetryMetrics(retryManagerRegistry)

    const healthMetrics = getSystemHealthMetrics(
      resilienceMonitor,
      circuitBreakerMetrics,
      retryManagerMetrics
    )

    const healthData = Object.freeze({
      status: healthMetrics.systemStatus,
      uptime: healthMetrics.uptime,
      availability: healthMetrics.availabilityPercent,
      timestamp: healthMetrics.timestamp,
      components: Object.freeze({
        circuitBreakers: Object.keys(healthMetrics.circuitBreakers).length,
        activeCircuitBreakers: Object.values(healthMetrics.circuitBreakers).filter(
          cb => cb.state !== 'CLOSED'
        ).length,
        retryManagers: Object.keys(healthMetrics.retryManagers).length,
        recentEvents: healthMetrics.errorRecoveryEvents.length
      })
    })

    return Object.freeze({
      success: true,
      data: healthData,
      processingTime: Date.now() - startTime,
      timestamp: Date.now()
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: `Failed to get health status: ${(error as Error).message}`,
      processingTime: Date.now() - startTime,
      timestamp: Date.now()
    })
  }
}

/**
 * F:executeMonitoringDashboard - Execute monitoring dashboard operation
 * @notation P:options F:executeMonitoringDashboard CB:executeMonitoringDashboard I:MonitoringResult DB:monitoring
 */
export const executeMonitoringDashboard = async (
  options: Record<string, unknown> = {}
): Promise<MonitoringResult> => {
  const startTime = Date.now()

  try {
    const metricsResponse = await executeMonitoringMetrics(options)
    if (!metricsResponse.success) {
      return metricsResponse
    }

    const metrics = metricsResponse.data as DashboardMetrics

    const html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Augster MCP Monitoring Dashboard</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .dashboard { max-width: 1200px; margin: 0 auto; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status-${metrics.systemHealth.status.toLowerCase()} { color: ${metrics.systemHealth.status === 'HEALTHY' ? '#28a745' : metrics.systemHealth.status === 'DEGRADED' ? '#ffc107' : '#dc3545'}; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .metric-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .metric-value { font-size: 2em; font-weight: bold; margin: 10px 0; }
        .refresh-btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>Augster MCP Monitoring Dashboard</h1>
            <p class="status-${metrics.systemHealth.status.toLowerCase()}">System Status: ${metrics.systemHealth.status}</p>
            <p>Uptime: ${Math.round(metrics.systemHealth.uptime / 1000 / 60)} minutes | Availability: ${metrics.systemHealth.availabilityPercent.toFixed(2)}%</p>
            <button class="refresh-btn" onclick="location.reload()">Refresh</button>
        </div>
        <div class="metrics-grid">
            <div class="metric-card">
                <h3>Circuit Breakers</h3>
                <div class="metric-value">${Object.keys(metrics.circuitBreakers).length}</div>
                <p>Active: ${Object.values(metrics.circuitBreakers).filter((cb: unknown) => (cb as Record<string, unknown>).state !== 'CLOSED').length}</p>
            </div>
            <div class="metric-card">
                <h3>Memory Operations</h3>
                <div class="metric-value">${metrics.operationMetrics.memory.operations}</div>
                <p>Avg Response: ${metrics.operationMetrics.memory.avgResponseTime}ms | Error Rate: ${metrics.operationMetrics.memory.errorRate}%</p>
            </div>
            <div class="metric-card">
                <h3>File Operations</h3>
                <div class="metric-value">${metrics.operationMetrics.file.operations}</div>
                <p>Avg Response: ${metrics.operationMetrics.file.avgResponseTime}ms | Error Rate: ${metrics.operationMetrics.file.errorRate}%</p>
            </div>
            <div class="metric-card">
                <h3>GitHub Operations</h3>
                <div class="metric-value">${metrics.operationMetrics.github.operations}</div>
                <p>Avg Response: ${metrics.operationMetrics.github.avgResponseTime}ms | Error Rate: ${metrics.operationMetrics.github.errorRate}%</p>
            </div>
        </div>
    </div>
    <script>
        setTimeout(() => location.reload(), 30000);
    </script>
</body>
</html>`

    return Object.freeze({
      success: true,
      data: Object.freeze({ html, metrics }),
      processingTime: Date.now() - startTime,
      timestamp: Date.now()
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: `Failed to generate dashboard: ${(error as Error).message}`,
      processingTime: Date.now() - startTime,
      timestamp: Date.now()
    })
  }
}

/**
 * F:executeMonitoringCommand - Execute monitoring command with resilience
 * @notation P:command F:executeMonitoringCommand CB:executeMonitoringCommand I:OperationResult DB:monitoring
 */
export const executeMonitoringCommand = async (
  command: MonitoringCommand
): Promise<OperationResult<MonitoringResult>> => {
  const config = createMonitoringConfig()

  return await executeWithResilience(command, config, async cmd => {
    const { action, options = {} } = cmd

    switch (action) {
      case 'metrics':
        return await executeMonitoringMetrics(options)

      case 'health':
        return await executeMonitoringHealth()

      case 'dashboard':
        return await executeMonitoringDashboard(options)

      case 'template':
        const templateSource = cmd.content || cmd.path
        if (!templateSource)
          throw new Error('Either path or content must be provided for template action')
        return await executeMonitoringTemplate(
          templateSource,
          options?.templateVars || {},
          options?.templateEngine || 'simple'
        )

      default:
        throw new Error(`Unknown monitoring action: ${action}`)
    }
  })
}

/**
 * F:executeMonitoringTemplate - Execute monitoring template operation
 * @notation P:templateSource,vars,engine F:executeMonitoringTemplate CB:executeMonitoringTemplate I:MonitoringResult DB:none
 */
export const executeMonitoringTemplate = async (
  templateSource: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple'
): Promise<MonitoringResult> => {
  try {
    const result = await processMultiHandlerTemplate(templateSource, vars, engine)

    return Object.freeze({
      success: true,
      data: result.content,
      processingTime: 0,
      timestamp: Date.now()
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message
    })
  }
}

/**
 * F:monitoringHandler - Create monitoring handler function
 * @notation P:db F:monitoringHandler CB:none I:function DB:monitoring
 */
export const monitoringHandler = () => {
  return {
    execute: async (input: unknown): Promise<MonitoringResult> => {
      if (!validateMonitoringCommand(input)) {
        return Object.freeze({
          success: false,
          error: 'Invalid monitoring command structure'
        })
      }

      const result = await executeMonitoringCommand(input)
      return result.success
        ? result.data
        : Object.freeze({
            success: false,
            error: result.error
          })
    }
  }
}
