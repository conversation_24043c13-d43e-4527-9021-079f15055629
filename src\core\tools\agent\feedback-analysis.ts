/**
 * Feedback Analysis - Pattern Detection and Performance Analysis
 * Split from feedbackProcessor.ts for constraint compliance (≤150 lines)
 *
 * @notation P:core/tools/agent/feedback-analysis F:detectSuccessPatterns,detectErrorPatterns,analyzePerformanceMetrics,generateContextTransformations,generateExecutionRefinements CB:none I:PatternAnalysis,PerformanceInsight,ContextTransformation,ExecutionRefinement DB:feedback
 */

import {
  TransformationType,
  RefinementType,
  ToolExecutionResult,
  ContextTransformation,
  ExecutionRefinement
} from '../../types'
import { PatternAnalysis, PerformanceInsight, FeedbackProcessingOptions, DEFAULT_FEEDBACK_OPTIONS, getPerformanceThresholds } from './feedback-core'

/**
 * F:detectSuccessPatterns - Detect success patterns from execution results
 * @notation P:results F:detectSuccessPatterns CB:none I:array DB:feedback
 */
export const detectSuccessPatterns = (
  results: readonly ToolExecutionResult[]
): readonly PatternAnalysis[] => {
  const successfulResults = results.filter(r => r.success)
  const patterns: Map<string, PatternAnalysis> = new Map()

  for (const result of successfulResults) {
    const handlerAction = `${result.handler}:${result.action}`

    if (patterns.has(handlerAction)) {
      const existing = patterns.get(handlerAction)!
      patterns.set(
        handlerAction,
        Object.freeze({
          ...existing,
          frequency: existing.frequency + 1,
          examples: Object.freeze([...existing.examples, result.taskId].slice(0, 5))
        })
      )
    } else {
      patterns.set(
        handlerAction,
        Object.freeze({
          pattern: handlerAction,
          frequency: 1,
          confidence: 0.8,
          examples: Object.freeze([result.taskId]),
          applicability: Object.freeze([result.handler])
        })
      )
    }
  }

  return Object.freeze(Array.from(patterns.values()))
}

/**
 * F:detectErrorPatterns - Detect error patterns from execution results
 * @notation P:results F:detectErrorPatterns CB:none I:array DB:feedback
 */
export const detectErrorPatterns = (
  results: readonly ToolExecutionResult[]
): readonly PatternAnalysis[] => {
  const failedResults = results.filter(r => !r.success)
  const patterns: Map<string, PatternAnalysis> = new Map()

  for (const result of failedResults) {
    if (!result.error) continue

    const errorType = result.error.name || 'UnknownError'

    if (patterns.has(errorType)) {
      const existing = patterns.get(errorType)!
      patterns.set(
        errorType,
        Object.freeze({
          ...existing,
          frequency: existing.frequency + 1,
          examples: Object.freeze([...existing.examples, result.taskId].slice(0, 5))
        })
      )
    } else {
      patterns.set(
        errorType,
        Object.freeze({
          pattern: errorType,
          frequency: 1,
          confidence: 0.9,
          examples: Object.freeze([result.taskId]),
          applicability: Object.freeze([result.handler])
        })
      )
    }
  }

  return Object.freeze(Array.from(patterns.values()))
}

/**
 * F:analyzePerformanceMetrics - Analyze performance metrics from execution results
 * @notation P:results F:analyzePerformanceMetrics CB:none I:array DB:feedback
 */
export const analyzePerformanceMetrics = (
  results: readonly ToolExecutionResult[]
): readonly PerformanceInsight[] => {
  const insights: PerformanceInsight[] = []

  if (results.length === 0) {
    return Object.freeze([])
  }

  const thresholds = getPerformanceThresholds(results)
  const executionTimes = results.map(r => r.executionTime)
  const avgExecutionTime =
    executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length
  const slowTasks = results.filter(r => r.executionTime > thresholds.executionTime.slow)

  if (avgExecutionTime > thresholds.executionTime.slow) {
    insights.push(
      Object.freeze({
        metric: 'execution_time',
        trend: 'degrading',
        impact: 'high',
        recommendation: `Performance degraded: avg ${avgExecutionTime.toFixed(0)}ms vs baseline ${thresholds.executionTime.average.toFixed(0)}ms`
      })
    )
  } else if (avgExecutionTime < thresholds.executionTime.fast) {
    insights.push(
      Object.freeze({
        metric: 'execution_time',
        trend: 'improving',
        impact: 'medium',
        recommendation: `Performance improved: avg ${avgExecutionTime.toFixed(0)}ms vs baseline ${thresholds.executionTime.average.toFixed(0)}ms`
      })
    )
  } else {
    insights.push(
      Object.freeze({
        metric: 'execution_time',
        trend: 'stable',
        impact: 'low',
        recommendation: `Performance stable: avg ${avgExecutionTime.toFixed(0)}ms within baseline range`
      })
    )
  }

  const retryResults = results.filter(
    r => r.metadata?.retryAttempts && r.metadata.retryAttempts > 0
  )
  const retryRate = retryResults.length / results.length

  if (retryRate > 0.3) {
    insights.push(
      Object.freeze({
        metric: 'retry_rate',
        trend: 'degrading',
        impact: 'medium',
        recommendation: `High retry rate: ${(retryRate * 100).toFixed(1)}% of tasks required retries`
      })
    )
  }

  const successRate = results.filter(r => r.success).length / results.length
  if (successRate < thresholds.successRate.poor) {
    insights.push(
      Object.freeze({
        metric: 'success_rate',
        trend: 'degrading',
        impact: 'high',
        recommendation: `Low success rate: ${(successRate * 100).toFixed(1)}% vs baseline ${(thresholds.successRate.good * 100).toFixed(1)}%`
      })
    )
  } else if (successRate > thresholds.successRate.good) {
    insights.push(
      Object.freeze({
        metric: 'success_rate',
        trend: 'stable',
        impact: 'low',
        recommendation: `Excellent success rate: ${(successRate * 100).toFixed(1)}%, maintain current practices`
      })
    )
  }

  return Object.freeze(insights)
}

/**
 * F:generateContextTransformations - Generate context transformations from patterns
 * @notation P:successPatterns,errorPatterns,options F:generateContextTransformations CB:none I:array DB:feedback
 */
export const generateContextTransformations = (
  successPatterns: readonly PatternAnalysis[],
  errorPatterns: readonly PatternAnalysis[],
  options: FeedbackProcessingOptions = DEFAULT_FEEDBACK_OPTIONS
): readonly ContextTransformation[] => {
  const transformations: ContextTransformation[] = []

  for (const pattern of successPatterns) {
    if (pattern.confidence >= options.minConfidenceThreshold && pattern.frequency >= 2) {
      transformations.push(
        Object.freeze({
          id: `success-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          sourceTaskId: pattern.examples[0],
          transformationType: TransformationType.SUCCESS_PATTERN,
          insights: Object.freeze({
            pattern: pattern.pattern,
            confidence: pattern.confidence,
            applicability: pattern.applicability,
            recommendations: Object.freeze([
              `Apply ${pattern.pattern} pattern to similar tasks`,
              `Consider standardizing this approach for ${pattern.applicability.join(', ')}`
            ])
          }),
          createdAt: Date.now()
        })
      )
    }
  }

  for (const pattern of errorPatterns) {
    if (pattern.confidence >= options.minConfidenceThreshold && pattern.frequency >= 2) {
      transformations.push(
        Object.freeze({
          id: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          sourceTaskId: pattern.examples[0],
          transformationType: TransformationType.ERROR_PATTERN,
          insights: Object.freeze({
            pattern: pattern.pattern,
            confidence: pattern.confidence,
            applicability: pattern.applicability,
            recommendations: Object.freeze([
              `Implement error handling for ${pattern.pattern}`,
              `Add retry logic for ${pattern.applicability.join(', ')} operations`
            ])
          }),
          createdAt: Date.now()
        })
      )
    }
  }

  return Object.freeze(transformations.slice(0, options.maxTransformations))
}

/**
 * F:generateExecutionRefinements - Generate execution refinements from insights
 * @notation P:insights,planId F:generateExecutionRefinements CB:none I:array DB:feedback
 */
export const generateExecutionRefinements = (
  insights: readonly PerformanceInsight[],
  planId: string
): readonly ExecutionRefinement[] => {
  const refinements: ExecutionRefinement[] = []

  for (const insight of insights) {
    if (insight.impact === 'high' || insight.impact === 'medium') {
      let refinementType: RefinementType
      let expectedImprovement = {
        performanceGain: 0,
        reliabilityIncrease: 0,
        resourceEfficiency: 0
      }

      switch (insight.metric) {
        case 'execution_time':
          refinementType = RefinementType.PARALLELIZATION_OPTIMIZATION
          expectedImprovement = {
            performanceGain: 0.3,
            reliabilityIncrease: 0.1,
            resourceEfficiency: 0.2
          }
          break
        case 'retry_rate':
          refinementType = RefinementType.RETRY_POLICY_ADJUSTMENT
          expectedImprovement = {
            performanceGain: 0.1,
            reliabilityIncrease: 0.4,
            resourceEfficiency: 0.2
          }
          break
        case 'success_rate':
          refinementType = RefinementType.DEPENDENCY_MODIFICATION
          expectedImprovement = {
            performanceGain: 0.2,
            reliabilityIncrease: 0.5,
            resourceEfficiency: 0.1
          }
          break
        default:
          refinementType = RefinementType.RESOURCE_REALLOCATION
          expectedImprovement = {
            performanceGain: 0.1,
            reliabilityIncrease: 0.1,
            resourceEfficiency: 0.3
          }
      }

      refinements.push(
        Object.freeze({
          planId,
          refinementType,
          changes: Object.freeze({
            before: insight.metric,
            after: insight.recommendation,
            reasoning: `Based on ${insight.trend} trend in ${insight.metric}`
          }),
          expectedImprovement: Object.freeze(expectedImprovement),
          appliedAt: Date.now()
        })
      )
    }
  }

  return Object.freeze(refinements)
}
