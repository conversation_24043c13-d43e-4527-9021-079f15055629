/**
 * Registry Validator - Tool Registry Corruption Detection and Cleanup
 * Validates tool registry entries and removes non-existent files
 *
 * @notation P:runtime/registryValidator F:validateRegistry,removeInvalidEntries,checkFileExists CB:tool-registry-corruption→CB:registry-clean I:RegistryValidationResult DB:none
 */

import * as fs from 'fs'
import * as path from 'path'

export type RegistryEntry = {
  readonly key: string
  readonly filePath: string
  readonly exists: boolean
}

export type RegistryValidationResult = {
  readonly totalEntries: number
  readonly validEntries: number
  readonly invalidEntries: number
  readonly removedEntries: readonly string[]
  readonly success: boolean
  readonly cleanedRegistry: Record<string, string>
}

/**
 * F:checkFileExists - Check if a file exists in the workspace
 * @notation P:filePath F:checkFileExists CB:none I:boolean DB:none
 */
export const checkFileExists = (filePath: string): boolean => {
  try {
    return fs.existsSync(filePath)
  } catch (error) {
    console.warn(`⚠️ FILE CHECK: Error checking ${filePath}:`, (error as Error).message)
    return false
  }
}

/**
 * F:validateRegistryEntry - Validate a single registry entry
 * @notation P:key,filePath F:validateRegistryEntry CB:none I:RegistryEntry DB:none
 */
export const validateRegistryEntry = (key: string, filePath: string): RegistryEntry => {
  const exists = checkFileExists(filePath)
  
  if (!exists) {
    console.warn(`❌ REGISTRY: Invalid entry ${key} -> ${filePath} (file not found)`)
  }
  
  return Object.freeze({
    key,
    filePath,
    exists
  })
}

/**
 * F:removeInvalidEntries - Remove invalid entries from registry
 * @notation P:registry F:removeInvalidEntries CB:registry-cleanup I:object DB:none
 */
export const removeInvalidEntries = (registry: Record<string, string>): Record<string, string> => {
  const cleanedRegistry: Record<string, string> = {}
  
  for (const [key, filePath] of Object.entries(registry)) {
    if (checkFileExists(filePath)) {
      cleanedRegistry[key] = filePath
    } else {
      console.log(`🗑️ REGISTRY: Removing invalid entry ${key} -> ${filePath}`)
    }
  }
  
  return Object.freeze(cleanedRegistry)
}

/**
 * F:validateRegistry - Validate complete tool registry
 * @notation P:registryPath F:validateRegistry CB:tool-registry-corruption→CB:registry-clean I:RegistryValidationResult DB:none
 */
export const validateRegistry = (registryPath: string): RegistryValidationResult => {
  try {
    // Load registry file
    if (!fs.existsSync(registryPath)) {
      throw new Error(`Registry file not found: ${registryPath}`)
    }
    
    const registryContent = fs.readFileSync(registryPath, 'utf-8')
    const registry = JSON.parse(registryContent)
    
    // Validate each entry
    const entries: RegistryEntry[] = []
    for (const [key, filePath] of Object.entries(registry)) {
      entries.push(validateRegistryEntry(key, filePath as string))
    }
    
    // Calculate statistics
    const totalEntries = entries.length
    const validEntries = entries.filter(e => e.exists).length
    const invalidEntries = totalEntries - validEntries
    const removedEntries = entries.filter(e => !e.exists).map(e => `${e.key} -> ${e.filePath}`)
    
    // Create cleaned registry
    const cleanedRegistry = removeInvalidEntries(registry)
    
    console.log(`📊 REGISTRY VALIDATION: ${validEntries}/${totalEntries} entries valid`)
    console.log(`🗑️ REGISTRY CLEANUP: ${invalidEntries} invalid entries removed`)
    
    return Object.freeze({
      totalEntries,
      validEntries,
      invalidEntries,
      removedEntries: Object.freeze(removedEntries),
      success: invalidEntries === 0,
      cleanedRegistry
    })
  } catch (error) {
    console.error('❌ REGISTRY VALIDATION: Failed to validate registry:', (error as Error).message)
    
    return Object.freeze({
      totalEntries: 0,
      validEntries: 0,
      invalidEntries: 0,
      removedEntries: [],
      success: false,
      cleanedRegistry: {}
    })
  }
}

/**
 * F:updateRegistryFile - Update registry file with cleaned entries
 * @notation P:registryPath,cleanedRegistry F:updateRegistryFile CB:file-write I:boolean DB:none
 */
export const updateRegistryFile = (registryPath: string, cleanedRegistry: Record<string, string>): boolean => {
  try {
    const updatedContent = JSON.stringify(cleanedRegistry, null, 2)
    fs.writeFileSync(registryPath, updatedContent, 'utf-8')
    
    console.log(`✅ REGISTRY: Updated ${registryPath} with ${Object.keys(cleanedRegistry).length} valid entries`)
    return true
  } catch (error) {
    console.error('❌ REGISTRY UPDATE: Failed to update registry file:', (error as Error).message)
    return false
  }
}

/**
 * F:identifyMissingFiles - Identify specific missing files from registry
 * @notation P:registry F:identifyMissingFiles CB:none I:array DB:none
 */
export const identifyMissingFiles = (registry: Record<string, string>): string[] => {
  const missingFiles: string[] = []
  
  for (const [key, filePath] of Object.entries(registry)) {
    if (!checkFileExists(filePath)) {
      missingFiles.push(path.basename(filePath))
    }
  }
  
  return missingFiles.sort()
}

/**
 * F:fixToolRegistryCorruption - Main function to fix tool registry corruption
 * @notation P:registryPath F:fixToolRegistryCorruption CB:tool-registry-corruption→CB:registry-clean I:RegistryValidationResult DB:none
 */
export const fixToolRegistryCorruption = (registryPath: string = '.augment/tool-registry.json'): RegistryValidationResult => {
  console.log('🔧 REGISTRY FIX: Starting tool registry corruption cleanup...')
  
  // Validate registry
  const validationResult = validateRegistry(registryPath)
  
  if (validationResult.invalidEntries > 0) {
    // Update registry file with cleaned entries
    const updateSuccess = updateRegistryFile(registryPath, validationResult.cleanedRegistry)
    
    if (updateSuccess) {
      console.log('✅ REGISTRY FIX: Tool registry corruption fixed successfully')
      console.log(`📊 REGISTRY FIX: Removed ${validationResult.invalidEntries} invalid entries`)
      console.log(`📊 REGISTRY FIX: ${validationResult.validEntries} valid entries retained`)
    } else {
      console.error('❌ REGISTRY FIX: Failed to update registry file')
    }
  } else {
    console.log('✅ REGISTRY FIX: No corruption detected, registry is clean')
  }
  
  return validationResult
}

/**
 * F:listSpecificMissingFiles - List the specific files mentioned in the task
 * @notation P:none F:listSpecificMissingFiles CB:none I:array DB:none
 */
export const listSpecificMissingFiles = (): string[] => {
  const expectedMissingFiles = [
    'tsValidator.ts',
    'dbMigrator.ts', 
    'contextEngine.ts',
    'symbolRegistry.ts',
    'undoTransformer.ts',
    'chainExecutor.ts',
    'environment.ts',
    'memoryState.ts',
    'schemaMigrator.ts',
    'validateSymbolContracts.ts',
    'performanceReporter.ts',
    'logWriter.ts'
  ]
  
  return expectedMissingFiles
}
