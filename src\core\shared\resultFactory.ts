/**
 * Result Factory - Consolidated Object.freeze Result Creation Patterns
 * @notation P:core/shared/resultFactory F:createDetectionResult,createCircuitBreakerResult,createTemplateResult,createHandlerResult CB:result-creation-consolidated I:all-result-types DB:none
 */

export type AntiPatternType =
  | 'class'
  | 'defaultExport'
  | 'anyType'
  | 'globalState'
  | 'thisKeyword'
  | 'newKeyword'
  | 'mutation'

export type DetectionResult = {
  readonly type: AntiPatternType
  readonly line: number
  readonly column: number
  readonly text: string
  readonly severity: 'error' | 'warning' | 'info'
  readonly message: string
  readonly fixable: boolean
}

export type DetectionReport = {
  readonly filePath: string
  readonly totalDetections: number
  readonly detectionsByType: Record<string, number>
  readonly fixableCount: number
  readonly riskAssessment: 'low' | 'medium' | 'high'
  readonly detections: readonly DetectionResult[]
}

export type FixResult = {
  readonly type: AntiPatternType
  readonly startLine: number
  readonly endLine: number
  readonly originalCode: string
  readonly fixedCode: string
  readonly description: string
}

export type FixPlan = {
  readonly filePath: string
  readonly detections: readonly DetectionResult[]
  readonly fixes: readonly FixResult[]
  readonly estimatedChanges: number
  readonly riskLevel: 'low' | 'medium' | 'high'
}

export type CircuitBreakerState = 'CLOSED' | 'OPEN' | 'HALF_OPEN'

export type CircuitBreakerConfig = {
  readonly failureThreshold: number
  readonly recoveryTimeout: number
  readonly monitoringWindow: number
  readonly halfOpenMaxCalls: number
}

export type CircuitBreakerInstance = {
  readonly name: string
  readonly config: CircuitBreakerConfig
  readonly state: CircuitBreakerState
  readonly failureCount: number
  readonly successCount: number
  readonly lastFailureTime: number
  readonly lastSuccessTime: number
  readonly totalCalls: number
  readonly rejectedCalls: number
  readonly halfOpenCalls: number
}

export type CircuitBreakerMetrics = {
  readonly state: CircuitBreakerState
  readonly failureCount: number
  readonly successCount: number
  readonly lastFailureTime: number
  readonly lastSuccessTime: number
  readonly totalCalls: number
  readonly rejectedCalls: number
}

export type TemplateResult = {
  readonly content: string
  readonly data: string
  readonly aiNotationMetadata: {
    readonly parametersDetected: Record<string, unknown>
    readonly toolsDetected: Record<string, unknown>
    readonly workflowCapable: boolean
    readonly validationRequired: boolean
  }
  readonly metadata: {
    readonly engine: 'simple' | 'mustache'
    readonly vars: Record<string, unknown>
    readonly processingTime: number
  }
}

export type StandardHandlerResult<TData = unknown> = {
  readonly success: boolean
  readonly data?: TData
  readonly error?: string
  readonly processingTime?: number
  readonly timestamp?: number
  readonly aiNotationMetadata?: unknown
  readonly rateLimit?: unknown
  readonly agentId?: string
  readonly templateGenerated?: boolean
  readonly templateVars?: Record<string, unknown>
  readonly analysis?: Record<string, unknown>
  readonly metadata?: Record<string, unknown>
}

/**
 * F:createDetectionResult - Create anti-pattern detection result
 * @notation P:type,line,column,text,severity,message,fixable F:createDetectionResult CB:none I:DetectionResult DB:none
 */
export const createDetectionResult = (
  type: AntiPatternType,
  line: number,
  column: number,
  text: string,
  severity: 'error' | 'warning' | 'info',
  message: string,
  fixable: boolean
): DetectionResult => {
  return Object.freeze({
    type,
    line,
    column,
    text,
    severity,
    message,
    fixable
  })
}

/**
 * F:createDetectionReport - Create detection report result
 * @notation P:filePath,detections,detectionsByType,fixableCount,riskAssessment F:createDetectionReport CB:none I:DetectionReport DB:none
 */
export const createDetectionReport = (
  filePath: string,
  detections: readonly DetectionResult[],
  detectionsByType: Record<string, number>,
  fixableCount: number,
  riskAssessment: 'low' | 'medium' | 'high'
): DetectionReport => {
  return Object.freeze({
    filePath,
    totalDetections: detections.length,
    detectionsByType: Object.freeze(detectionsByType),
    fixableCount,
    riskAssessment,
    detections: Object.freeze(detections)
  })
}

/**
 * F:createFixResult - Create fix result
 * @notation P:type,startLine,endLine,originalCode,fixedCode,description F:createFixResult CB:none I:FixResult DB:none
 */
export const createFixResult = (
  type: AntiPatternType,
  startLine: number,
  endLine: number,
  originalCode: string,
  fixedCode: string,
  description: string
): FixResult => {
  return Object.freeze({
    type,
    startLine,
    endLine,
    originalCode,
    fixedCode,
    description
  })
}

/**
 * F:createFixPlan - Create fix plan result
 * @notation P:filePath,detections,fixes,estimatedChanges,riskLevel F:createFixPlan CB:none I:FixPlan DB:none
 */
export const createFixPlan = (
  filePath: string,
  detections: readonly DetectionResult[],
  fixes: readonly FixResult[],
  estimatedChanges: number,
  riskLevel: 'low' | 'medium' | 'high'
): FixPlan => {
  return Object.freeze({
    filePath,
    detections,
    fixes: Object.freeze(fixes),
    estimatedChanges,
    riskLevel
  })
}

/**
 * F:createCircuitBreakerConfig - Create circuit breaker configuration
 * @notation P:failureThreshold,recoveryTimeout,monitoringWindow,halfOpenMaxCalls F:createCircuitBreakerConfig CB:none I:CircuitBreakerConfig DB:none
 */
export const createCircuitBreakerConfig = (
  failureThreshold: number = 5,
  recoveryTimeout: number = 30000,
  monitoringWindow: number = 60000,
  halfOpenMaxCalls: number = 3
): CircuitBreakerConfig => {
  return Object.freeze({
    failureThreshold,
    recoveryTimeout,
    monitoringWindow,
    halfOpenMaxCalls
  })
}

/**
 * F:createCircuitBreakerInstance - Create circuit breaker instance
 * @notation P:name,config,state,counters F:createCircuitBreakerInstance CB:none I:CircuitBreakerInstance DB:none
 */
export const createCircuitBreakerInstance = (
  name: string,
  config: CircuitBreakerConfig,
  state: CircuitBreakerState = 'CLOSED',
  failureCount: number = 0,
  successCount: number = 0,
  lastFailureTime: number = 0,
  lastSuccessTime: number = 0,
  totalCalls: number = 0,
  rejectedCalls: number = 0,
  halfOpenCalls: number = 0
): CircuitBreakerInstance => {
  return Object.freeze({
    name,
    config,
    state,
    failureCount,
    successCount,
    lastFailureTime,
    lastSuccessTime,
    totalCalls,
    rejectedCalls,
    halfOpenCalls
  })
}

/**
 * F:createCircuitBreakerMetrics - Create circuit breaker metrics
 * @notation P:instance F:createCircuitBreakerMetrics CB:none I:CircuitBreakerMetrics DB:none
 */
export const createCircuitBreakerMetrics = (
  instance: CircuitBreakerInstance
): CircuitBreakerMetrics => {
  return Object.freeze({
    state: instance.state,
    failureCount: instance.failureCount,
    successCount: instance.successCount,
    lastFailureTime: instance.lastFailureTime,
    lastSuccessTime: instance.lastSuccessTime,
    totalCalls: instance.totalCalls,
    rejectedCalls: instance.rejectedCalls
  })
}

/**
 * F:updateCircuitBreakerInstance - Update circuit breaker instance with new values
 * @notation P:instance,updates F:updateCircuitBreakerInstance CB:none I:CircuitBreakerInstance DB:none
 */
export const updateCircuitBreakerInstance = (
  instance: CircuitBreakerInstance,
  updates: Partial<Omit<CircuitBreakerInstance, 'name' | 'config'>>
): CircuitBreakerInstance => {
  return Object.freeze({
    ...instance,
    ...updates
  })
}

/**
 * F:createTemplateResult - Create template processing result
 * @notation P:content,data,aiNotationMetadata,metadata F:createTemplateResult CB:none I:TemplateResult DB:none
 */
export const createTemplateResult = (
  content: string,
  data: string,
  parametersDetected: Record<string, unknown> = {},
  toolsDetected: Record<string, unknown> = {},
  workflowCapable: boolean = false,
  validationRequired: boolean = false,
  engine: 'simple' | 'mustache' = 'simple',
  vars: Record<string, unknown> = {},
  processingTime: number = 0
): TemplateResult => {
  return Object.freeze({
    content,
    data,
    aiNotationMetadata: Object.freeze({
      parametersDetected: Object.freeze(parametersDetected),
      toolsDetected: Object.freeze(toolsDetected),
      workflowCapable,
      validationRequired
    }),
    metadata: Object.freeze({
      engine,
      vars: Object.freeze(vars),
      processingTime
    })
  })
}

/**
 * F:createTemplateErrorResult - Create template error result
 * @notation P:template,vars,engine F:createTemplateErrorResult CB:none I:TemplateResult DB:none
 */
export const createTemplateErrorResult = (
  template: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple'
): TemplateResult => {
  return Object.freeze({
    content: template,
    data: template,
    aiNotationMetadata: Object.freeze({
      parametersDetected: Object.freeze({}),
      toolsDetected: Object.freeze({}),
      workflowCapable: false,
      validationRequired: false
    }),
    metadata: Object.freeze({
      engine,
      vars: Object.freeze(vars),
      processingTime: 0
    })
  })
}

/**
 * F:createHandlerSuccessResult - Create successful handler result
 * @notation P:data,processingTime,timestamp,metadata F:createHandlerSuccessResult CB:none I:StandardHandlerResult DB:none
 */
export const createHandlerSuccessResult = <TData = unknown>(
  data?: TData,
  processingTime: number = 0,
  timestamp: number = Date.now(),
  metadata?: Record<string, unknown>
): StandardHandlerResult<TData> => {
  return Object.freeze({
    success: true,
    data,
    processingTime,
    timestamp,
    ...(metadata && { metadata: Object.freeze(metadata) })
  })
}

/**
 * F:createHandlerErrorResult - Create error handler result
 * @notation P:error,processingTime,timestamp F:createHandlerErrorResult CB:none I:StandardHandlerResult DB:none
 */
export const createHandlerErrorResult = <TData = unknown>(
  error: string | Error,
  processingTime: number = 0,
  timestamp: number = Date.now()
): StandardHandlerResult<TData> => {
  const errorMessage = error instanceof Error ? error.message : error
  return Object.freeze({
    success: false,
    error: errorMessage,
    processingTime,
    timestamp
  })
}

/**
 * F:createMemoryResult - Create memory operation result
 * @notation P:success,data,error,aiNotationMetadata F:createMemoryResult CB:none I:StandardHandlerResult DB:none
 */
export const createMemoryResult = (
  success: boolean,
  data?: unknown,
  error?: string,
  aiNotationMetadata?: unknown
): StandardHandlerResult => {
  const result: StandardHandlerResult = {
    success
  }

  if (data !== undefined) result.data = data
  if (error) result.error = error
  if (aiNotationMetadata) result.aiNotationMetadata = aiNotationMetadata

  return Object.freeze(result)
}

/**
 * F:createFileResult - Create file operation result
 * @notation P:success,content,data,error,processingTime,timestamp F:createFileResult CB:none I:StandardHandlerResult DB:none
 */
export const createFileResult = (
  success: boolean,
  content?: string,
  data?: Record<string, unknown>,
  error?: string,
  processingTime: number = 0,
  timestamp: number = Date.now()
): StandardHandlerResult => {
  return Object.freeze({
    success,
    ...(content !== undefined && { content }),
    ...(data && { data: Object.freeze(data) }),
    ...(error && { error }),
    processingTime,
    timestamp
  })
}

/**
 * F:createDatabaseResult - Create database operation result
 * @notation P:success,data,error,processingTime,timestamp F:createDatabaseResult CB:none I:StandardHandlerResult DB:none
 */
export const createDatabaseResult = (
  success: boolean,
  data?: unknown,
  error?: string,
  processingTime: number = 0,
  timestamp: number = Date.now()
): StandardHandlerResult => {
  return Object.freeze({
    success,
    ...(data !== undefined && { data }),
    ...(error && { error }),
    processingTime,
    timestamp
  })
}

/**
 * F:createGitHubResult - Create GitHub operation result
 * @notation P:success,data,error,processingTime,timestamp F:createGitHubResult CB:none I:StandardHandlerResult DB:none
 */
export const createGitHubResult = (
  success: boolean,
  data?: unknown,
  error?: string,
  processingTime: number = 0,
  timestamp: number = Date.now()
): StandardHandlerResult => {
  return Object.freeze({
    success,
    ...(data !== undefined && { data }),
    ...(error && { error }),
    processingTime,
    timestamp
  })
}
