/**
 * Runtime Router - Agent-Centric Architecture
 * Migrated from legacy handler routing to unified agent execution
 *
 * @notation P:runtime/router F:buildRouter,executeAgentCommand CB:buildRouter I:MCPCommand,AgentResult DB:agent
 */

import {
  createAgentWorkflow,
  executeAgentWorkflow,
  createExecutionTask,
  createExecutionPlan,
  processFeedback,
  consumeContextualState,
  enforceDirectiveCompliance,
  executeHandlerCommand
} from '../core/tools/agent'

import {
  formatTrace,
  createAgentTraceOutput,
  validateTraceFormat,
  implementTraceFormat
} from './traceFormatter'

import { preemptiveValidationPipeline } from '../core/tools/agent/planner'
import { createResilienceMonitor } from '../core/antiPatterns/resilienceMonitor'
import { memoryHandler } from '../core/handlers/memory'
import { fileHandler } from '../core/handlers/file'
import { databaseHandler } from '../core/handlers/database'
import { githubHandler } from '../core/handlers/github'
import { monitoringHandler } from '../core/handlers/monitoring'
import { timeHandler } from '../core/handlers/time'
import { fetchHandler } from '../core/handlers/fetch'
import { coordinationHandler } from '../core/handlers/coordination'
import { gitHandler } from '../core/handlers/git'
import { terminalHandler } from '../core/handlers/terminal'
import { enhancedToolHandler } from '../core/handlers/enhancedTool'
import { Database } from 'sqlite3'
import {
  createUnifiedResilienceSystem,
  executeWithCircuitBreaker,
  executeWithRetry
} from '../core/antiPatterns'
import {
  createLoggingState,
  logMCPCall,
  flushLogs,
  type LoggingState
} from '../core/handlers/CentralLoggingDispatcher'
import { TaskType } from '../core/types'

export type MCPCommand = {
  readonly command: string
  readonly payload: unknown
}

export type AgentExecutionResult = {
  readonly success: boolean
  readonly data?: unknown
  readonly error?: string
  readonly timestamp: number
  readonly processingTime: number
  readonly agentMetadata?: {
    readonly sessionId: string
    readonly planId?: string
    readonly taskCount?: number
    readonly optimizations?: readonly string[]
  }
}

/**
 * F:buildRouter - Build agent-centric router with legacy compatibility
 * @notation P:db F:buildRouter CB:buildRouter I:function DB:agent
 */
export const buildRouter = (db: Database) => {
  let agentWorkflow: Awaited<ReturnType<typeof createAgentWorkflow>> | null = null
  let loggingState: LoggingState = createLoggingState(db)

  const resilienceMonitor = createResilienceMonitor()
  console.log('🔧 AGENT RUNTIME: Resilience monitor initialized')
  console.log('🔧 AGENT RUNTIME: All tool execution routed through agent system')

  const resilienceSystem = createUnifiedResilienceSystem(
    { failureThreshold: 10 },
    { maxRetries: 2, baseDelay: 500, maxDelay: 5000, jitterPercent: 25 },
    { maxEventHistory: 1000 }
  )

  const routerCircuitBreaker = resilienceSystem.getOrCreateCircuitBreaker('mcp-router')
  const routerRetryManager = resilienceSystem.getOrCreateRetryManager('mcp-router')

  /**
   * F:executeCompleteAgentWorkflow - COMPLETE agent workflow execution (ONLY execution path)
   * @notation P:command,payload F:executeCompleteAgentWorkflow CB:executeCompleteAgentWorkflow I:AgentExecutionResult DB:agent
   */
  const executeCompleteAgentWorkflow = async (
    command: string,
    payload: unknown
  ): Promise<AgentExecutionResult> => {
    const startTime = Date.now()

    console.log(`🤖 COMPLETE AGENT WORKFLOW: Executing ${command}`)

    console.log(`📋 STEP 1: Template parsing for ${command}`)
    const templateEnhancement = {
      enhancedCommand: command,
      enhancedPayload: payload,
      symbolicTrace: {
        'P:': `runtime/router/${command}`,
        'F:': 'executeCompleteAgentWorkflow',
        'CB:': 'executeCompleteAgentWorkflow',
        'I:': 'AgentExecutionResult',
        'DB:': 'agent'
      },
      sessionId: loggingState.sessionId,
      timestamp: Date.now()
    }

    console.log(`🔧 STEP 2: Creating agent workflow for ${command}`)
    if (!agentWorkflow) {
      agentWorkflow = await createAgentWorkflow(loggingState.sessionId, undefined, db)
    }

    console.log(`📝 STEP 3: Creating execution task for ${command}`)
    const taskType = mapCommandToTaskType(command)
    const task = createExecutionTask(
      `task-${Date.now()}`,
      taskType,
      {
        originalCommand: command,
        enhancedCommand: templateEnhancement.enhancedCommand,
        payload: templateEnhancement.enhancedPayload,
        symbolicTrace: templateEnhancement.symbolicTrace
      },
      []
    )

    console.log(`📊 STEP 4: Creating execution plan for ${command}`)
    const planResult = createExecutionPlan(agentWorkflow.sessionId, [task])

    if (!planResult.success || !planResult.plan) {
      throw new Error(planResult.error || 'Failed to create execution plan')
    }

    console.log(`⚡ STEP 5: Executing complete agent workflow for ${command}`)
    const workflowResult = await executeAgentWorkflow(agentWorkflow, [task])

    console.log(`🔧 STEP 5.1: Executing direct handler for ${command}`)
    let handlerResult = null
    try {
      const [handlerName, action] = command.split('.')

      const payloadObj = payload as Record<string, unknown>

      if (handlerName === 'memory') {
        const memory = memoryHandler(db)
        handlerResult = await memory.execute({ action, ...payloadObj })
      } else if (handlerName === 'file') {
        const file = fileHandler(db)
        handlerResult = await file.execute({ action, ...payloadObj })
      } else if (handlerName === 'database') {
        const database = databaseHandler()
        handlerResult = await database.execute({ action, ...payloadObj })
      } else if (handlerName === 'github') {
        const github = githubHandler()
        handlerResult = await github.execute({ action, ...payloadObj })
      } else if (handlerName === 'monitoring') {
        const monitoring = monitoringHandler()
        handlerResult = await monitoring.execute({ action, ...payloadObj })
      } else if (handlerName === 'time') {
        const time = timeHandler(db)
        handlerResult = await time.execute({ action, ...payloadObj })
      } else if (handlerName === 'fetch') {
        const fetch = fetchHandler()
        handlerResult = await fetch.execute({ action, ...payloadObj })
      } else if (handlerName === 'coordination') {
        const coordination = coordinationHandler(db)
        handlerResult = await coordination.execute({ action, ...payloadObj })
      } else if (handlerName === 'git') {
        const git = gitHandler()
        handlerResult = await git.execute({ action, ...payloadObj })
      } else if (handlerName === 'terminal') {
        const terminal = terminalHandler()
        handlerResult = await terminal.execute({ action, ...payloadObj })
      } else if (handlerName === 'enhancedTool') {
        const enhancedTool = enhancedToolHandler(db)
        handlerResult = await enhancedTool.execute({ action, ...payloadObj })
      } else {
        console.warn(`⚠️ UNKNOWN HANDLER: ${handlerName}`)
      }

      if (handlerResult) {
        console.log(`✅ DIRECT HANDLER EXECUTED: ${command}`)
      }
    } catch (handlerError) {
      console.warn(`⚠️ DIRECT HANDLER FAILED: ${command} - ${(handlerError as Error).message}`)
    }

    console.log(`🔄 STEP 6: Processing feedback for ${command}`)
    const feedbackResult = processFeedback(
      [
        {
          taskId: task.id,
          planId: planResult.plan.id,
          handler: 'complete-agent-workflow',
          action: command,
          success: true,
          output: workflowResult,
          executionTime: Date.now() - startTime,
          timestamp: Date.now()
        }
      ],
      agentWorkflow.sessionId
    )

    console.log(`✅ COMPLETE AGENT WORKFLOW: Success for ${command}`)

    return Object.freeze({
      success: true,
      data: {
        workflow: workflowResult,
        handlerResult: handlerResult,
        command: command,
        payload: payload
      },
      timestamp: Date.now(),
      processingTime: Date.now() - startTime,
      agentMetadata: Object.freeze({
        sessionId: agentWorkflow.sessionId,
        planId: planResult.plan.id,
        taskCount: 1,
        optimizations: planResult.optimizations,
        symbolicTrace: templateEnhancement.symbolicTrace,
        feedbackInsights: feedbackResult.insights,
        executionPath: 'COMPLETE_AGENT_WORKFLOW_WITH_HANDLER',
        stepsCompleted: [
          'template_parsing',
          'workflow_creation',
          'task_creation',
          'plan_creation',
          'workflow_execution',
          'handler_execution',
          'feedback_processing'
        ],
        handlerExecuted: handlerResult !== null
      })
    })
  }

  /**
   * F:mapCommandToTaskType - Map MCP command to agent task type with enhanced MCP patterns
   * @notation P:command F:mapCommandToTaskType CB:none I:TaskType DB:none
   */
  const mapCommandToTaskType = (command: string): TaskType => {
    if (command.startsWith('memory.')) {
      if (command.includes('query') || command.includes('search')) return TaskType.EXTRACT
      if (command.includes('insert') || command.includes('update')) return TaskType.IMPLEMENT
      return TaskType.EXTRACT
    }

    if (command.startsWith('file.')) {
      if (command.includes('read') || command.includes('list') || command.includes('exists'))
        return TaskType.EXTRACT
      if (command.includes('write') || command.includes('create')) return TaskType.IMPLEMENT
      if (command.includes('backup')) return TaskType.CONFIGURE
      return TaskType.EXTRACT
    }

    if (command.startsWith('database.')) {
      if (command.includes('query') || command.includes('schema')) return TaskType.EXTRACT
      if (command.includes('execute') || command.includes('migrate')) return TaskType.IMPLEMENT
      if (command.includes('backup')) return TaskType.CONFIGURE
      return TaskType.EXTRACT
    }

    if (command.startsWith('github.')) return TaskType.COORDINATE
    if (command.startsWith('monitoring.')) return TaskType.MEASURE
    if (command.startsWith('coordination.')) return TaskType.COORDINATE
    if (command.startsWith('terminal.')) return TaskType.DEPLOY
    if (command.startsWith('git.')) return TaskType.DEPLOY
    if (command.startsWith('time.')) return TaskType.EXTRACT
    if (command.startsWith('fetch.')) return TaskType.EXTRACT
    if (command.includes('test') || command.includes('validate')) return TaskType.TEST
    if (command.includes('implement') || command.includes('create')) return TaskType.IMPLEMENT
    if (command.includes('extract') || command.includes('analyze')) return TaskType.EXTRACT
    if (command.includes('document') || command.includes('template')) return TaskType.DOCUMENT
    if (command.includes('deploy') || command.includes('execute')) return TaskType.DEPLOY
    if (command.includes('measure') || command.includes('monitor')) return TaskType.MEASURE
    if (command.includes('coordinate') || command.includes('sync')) return TaskType.COORDINATE
    if (command.includes('compile') || command.includes('build')) return TaskType.COMPILE
    if (command.includes('configure') || command.includes('setup')) return TaskType.CONFIGURE

    return TaskType.EXTRACT // Default fallback
  }

  /**
   * F:executeAgentValidatedCommand - Execute command through agent validation pipeline with graceful degradation
   * @notation P:command,payload F:executeAgentValidatedCommand CB:executeAgentValidatedCommand I:AgentExecutionResult DB:agent
   */
  const executeAgentValidatedCommand = async (
    command: string,
    payload: Record<string, unknown>
  ): Promise<AgentExecutionResult> => {
    console.log(`🔍 AGENT VALIDATION: Running preemptive validation for ${command}`)

    try {
      const validation = await preemptiveValidationPipeline(
        'src/runtime/router.ts',
        [`Executing command: ${command}`],
        process.cwd()
      )

      if (!validation.valid) {
        console.warn('⚠️ PREEMPTIVE VALIDATION FAILED (continuing anyway):', validation.errors)
        console.log(
          '🔄 GRACEFUL DEGRADATION: Proceeding with agent execution despite validation issues'
        )
      } else {
        console.log('✅ PREEMPTIVE VALIDATION PASSED: Proceeding with agent execution')
      }
    } catch (validationError) {
      console.warn('⚠️ VALIDATION ERROR (continuing anyway):', (validationError as Error).message)
      console.log(
        '🔄 GRACEFUL DEGRADATION: Proceeding with agent execution despite validation error'
      )
    }

    return await executeCompleteAgentWorkflow(command, payload)
  }

  return async (input: unknown): Promise<AgentExecutionResult> => {
    const startTime = Date.now()
    const mcpInput = input as MCPCommand
    const commandName = mcpInput?.command || 'unknown'

    try {
      if (!mcpInput || typeof mcpInput.command !== 'string') {
        throw new Error('Invalid input: expected { command: string, payload: object }')
      }

      const { command, payload } = mcpInput

      try {
        console.log(
          `🤖 AGENT-ONLY EXECUTION: Processing ${command} through validated agent workflow`
        )
        const agentResult = await executeAgentValidatedCommand(
          command,
          payload as Record<string, unknown>
        )
        console.log(`✅ AGENT-ONLY EXECUTION: Success for ${command}`)
        return agentResult
      } catch (agentError) {
        console.error(`❌ AGENT EXECUTION FAILED for ${command}:`, (agentError as Error).message)

        throw new Error(`Agent execution failed for ${command}: ${(agentError as Error).message}`)
      }
    } catch (error) {
      const processingTime = Date.now() - startTime
      const errorMessage = (error as Error).message

      loggingState = await logMCPCall(
        loggingState,
        {
          operation: commandName,
          result: { success: false, error: errorMessage },
          context: mcpInput
        },
        {
          sessionId: loggingState.sessionId,
          toolName: 'agent-router',
          operation: commandName,
          performanceMetrics: {
            responseTime: processingTime,
            circuitBreakerState: 'closed',
            retryCount: 0
          },
          errorContext: {
            errorType: 'routing-error',
            errorMessage,
            recoveryAttempts: 0,
            recoverySuccess: false
          }
        }
      )

      return Object.freeze({
        success: false,
        error: errorMessage,
        timestamp: Date.now(),
        processingTime
      })
    }
  }
}
