{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Database Operations Schema", "description": "Validation schema for database operations handler commands", "type": "object", "properties": {"action": {"type": "string", "enum": ["connect", "execute", "query", "schema", "backup", "migrate", "template"]}, "path": {"type": "string", "minLength": 1, "description": "Database file path or template path"}, "sql": {"type": "string", "description": "SQL statement for execute/query operations"}, "content": {"type": "string", "description": "Template content for template action"}, "options": {"type": "object", "properties": {"mode": {"type": "string", "enum": ["readonly", "readwrite", "create"], "default": "readwrite"}, "timeout": {"type": "number"}, "params": {"type": "array", "description": "SQL parameters for prepared statements"}, "limit": {"type": "number", "minimum": 1, "maximum": 10000}, "offset": {"type": "number", "minimum": 0}, "table": {"type": "string"}, "detailed": {"type": "boolean", "default": false}, "backupPath": {"type": "string"}, "compress": {"type": "boolean", "default": false}, "migrationSql": {"type": "array", "items": {"type": "string"}}, "version": {"type": "string"}, "rollback": {"type": "boolean", "default": false}, "templateEngine": {"type": "string", "enum": ["simple", "mustache"], "default": "simple"}, "templateVars": {"type": "object", "description": "Template variables for substitution"}, "templateOutputPath": {"type": "string", "description": "Output path for processed template"}}}}, "required": ["action", "path"], "additionalProperties": false}