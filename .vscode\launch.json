{"version": "0.2.0", "configurations": [{"name": "Debug MCP (dev)", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run", "mcp:dev"], "console": "integratedTerminal", "preLaunchTask": "build"}, {"name": "Debug MCP (prod)", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run", "mcp:prod"], "console": "integratedTerminal", "preLaunchTask": "build"}, {"name": "Profile MCP (dev)", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run", "profile:dev"], "console": "integratedTerminal", "preLaunchTask": "build"}, {"name": "Profile MCP (prod)", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run", "profile:prod"], "console": "integratedTerminal", "preLaunchTask": "build"}]}