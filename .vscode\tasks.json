{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "build",
      "type": "npm",
      "script": "build",
      "group": "build",
      "problemMatcher": ["$tsc"]
    },
    {
      "label": "mcp:dev",
      "type": "npm",
      "script": "mcp:dev",
      "group": "none"
    },
    {
      "label": "mcp:prod",
      "type": "npm",
      "script": "mcp:prod",
      "group": "none"
    },
    {
      "label": "mcp:dev:profile",
      "type": "npm",
      "script": "profile:dev",
      "group": "none"
    },
    {
      "label": "mcp:prod:profile",
      "type": "npm",
      "script": "profile:prod",
      "group": "none"
    },
    {
      "label": "Ollama: Serve",
      "type": "npm",
      "script": "ollama:serve",
      "isBackground": true,
      "problemMatcher": {
        "owner": "custom",
        "pattern": { "regexp": ".+", "file": 1, "location": 2, "message": 3 },
        "background": {
          "activeOnStart": true,
          "beginsPattern": "Serving model",
          "endsPattern": "Listening"
        }
      },
      "presentation": { "reveal": "always", "panel": "dedicated" },
    }
  ]
}
