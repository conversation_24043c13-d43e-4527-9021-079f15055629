/**
 * Shared Error Handling - Consolidated Error Patterns for All Handlers
 * Eliminates duplication across file.ts, memory.ts, database.ts, coordination.ts, fetch.ts
 *
 * @notation P:core/shared/errorHandling F:createErrorResult,createSuccessResult,handleOperationError CB:error-handling-shared I:StandardResult,ErrorContext DB:none
 */

export type StandardResult<TData = unknown> = {
  readonly success: boolean
  readonly data?: TData
  readonly error?: string
  readonly processingTime?: number
  readonly timestamp?: number
}

export type ErrorContext = {
  readonly operation: string
  readonly component: string
  readonly startTime?: number
  readonly additionalData?: Record<string, unknown>
}

/**
 * F:createErrorResult - Create standardized error result
 * @notation P:error,context F:createErrorResult CB:none I:StandardResult DB:none
 */
export const createErrorResult = <TData = unknown>(
  error: Error | string,
  context?: ErrorContext
): StandardResult<TData> => {
  const errorMessage = error instanceof Error ? error.message : error
  const processingTime = context?.startTime ? Date.now() - context.startTime : 0
  
  return Object.freeze({
    success: false,
    error: errorMessage,
    processingTime,
    timestamp: Date.now()
  })
}

/**
 * F:createSuccessResult - Create standardized success result
 * @notation P:data,context F:createSuccessResult CB:none I:StandardResult DB:none
 */
export const createSuccessResult = <TData>(
  data: TData,
  context?: ErrorContext
): StandardResult<TData> => {
  const processingTime = context?.startTime ? Date.now() - context.startTime : 0
  
  return Object.freeze({
    success: true,
    data,
    processingTime,
    timestamp: Date.now()
  })
}

/**
 * F:handleOperationError - Handle operation errors with context
 * @notation P:error,operation,component F:handleOperationError CB:error-handling I:StandardResult DB:none
 */
export const handleOperationError = <TData = unknown>(
  error: Error | string,
  operation: string,
  component: string,
  startTime?: number
): StandardResult<TData> => {
  return createErrorResult<TData>(error, {
    operation,
    component,
    startTime
  })
}

/**
 * F:wrapWithErrorHandling - Wrap async operation with standardized error handling
 * @notation P:operation,context,fn F:wrapWithErrorHandling CB:error-handling I:StandardResult DB:none
 */
export const wrapWithErrorHandling = async <TData>(
  operation: string,
  component: string,
  fn: () => Promise<TData>
): Promise<StandardResult<TData>> => {
  const startTime = Date.now()
  
  try {
    const data = await fn()
    return createSuccessResult(data, { operation, component, startTime })
  } catch (error) {
    return handleOperationError(error as Error, operation, component, startTime)
  }
}

/**
 * F:createFileOperationResult - Create file operation result with metadata
 * @notation P:success,data,path,stats F:createFileOperationResult CB:none I:StandardResult DB:none
 */
export const createFileOperationResult = (
  success: boolean,
  data?: unknown,
  error?: string,
  filePath?: string,
  stats?: { size: number; mtime: Date },
  encoding?: string,
  atomic?: boolean
): StandardResult => {
  const baseResult = {
    success,
    processingTime: 0,
    timestamp: Date.now()
  }
  
  if (!success) {
    return Object.freeze({
      ...baseResult,
      error
    })
  }
  
  return Object.freeze({
    ...baseResult,
    data: Object.freeze({
      path: filePath,
      size: stats?.size,
      modified: stats?.mtime.toISOString(),
      encoding,
      atomic,
      ...data
    })
  })
}

/**
 * F:createDatabaseOperationResult - Create database operation result
 * @notation P:success,data,error F:createDatabaseOperationResult CB:none I:StandardResult DB:none
 */
export const createDatabaseOperationResult = (
  success: boolean,
  data?: unknown,
  error?: string
): StandardResult => {
  return Object.freeze({
    success,
    data,
    error,
    processingTime: 0,
    timestamp: Date.now()
  })
}

/**
 * F:createMemoryOperationResult - Create memory operation result
 * @notation P:success,data,error,metadata F:createMemoryOperationResult CB:none I:StandardResult DB:none
 */
export const createMemoryOperationResult = (
  success: boolean,
  data?: unknown,
  error?: string,
  aiNotationMetadata?: unknown
): StandardResult => {
  return Object.freeze({
    success,
    data,
    error,
    aiNotationMetadata
  })
}

/**
 * F:createCoordinationOperationResult - Create coordination operation result
 * @notation P:success,data,error F:createCoordinationOperationResult CB:none I:StandardResult DB:none
 */
export const createCoordinationOperationResult = (
  success: boolean,
  data?: unknown,
  error?: string
): StandardResult => {
  return Object.freeze({
    success,
    data,
    error,
    processingTime: 0,
    timestamp: Date.now()
  })
}

/**
 * F:createFetchOperationResult - Create fetch operation result
 * @notation P:success,data,error F:createFetchOperationResult CB:none I:StandardResult DB:none
 */
export const createFetchOperationResult = (
  success: boolean,
  data?: unknown,
  error?: string
): StandardResult => {
  return Object.freeze({
    success,
    data,
    error,
    timestamp: Date.now(),
    processingTime: 0
  })
}
