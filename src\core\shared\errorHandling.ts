/**
 * Shared Error Handling - Consolidated Error Patterns for ALL Handlers
 * @notation P:core/shared/errorHandling F:createStandardErrorResult,createStandardSuccessResult,wrapWithStandardErrorHandling CB:error-handling-consolidated I:StandardResult,ErrorContext DB:none
 */

export type StandardResult<TData = unknown> = {
  readonly success: boolean
  readonly data?: TData
  readonly error?: string
  readonly processingTime?: number
  readonly timestamp?: number
  readonly aiNotationMetadata?: unknown
  readonly rateLimit?: unknown
  readonly agentId?: string
  readonly templateGenerated?: boolean
  readonly templateVars?: Record<string, unknown>
  readonly analysis?: Record<string, unknown>
  readonly metadata?: Record<string, unknown>
}

export type ErrorContext = {
  readonly operation: string
  readonly component: string
  readonly startTime?: number
  readonly additionalData?: Record<string, unknown>
}

/**
 * F:createStandardErrorResult - Create standardized error result (CONSOLIDATED PATTERN)
 * @notation P:error,context F:createStandardErrorResult CB:none I:StandardResult DB:none
 */
export const createStandardErrorResult = <TData = unknown>(
  error: Error | string,
  context?: ErrorContext
): StandardResult<TData> => {
  const errorMessage = error instanceof Error ? error.message : error
  const processingTime = context?.startTime ? Date.now() - context.startTime : 0

  return Object.freeze({
    success: false,
    error: errorMessage,
    processingTime,
    timestamp: Date.now()
  })
}

/**
 * F:createStandardSuccessResult - Create standardized success result (CONSOLIDATED PATTERN)
 * @notation P:data,context F:createStandardSuccessResult CB:none I:StandardResult DB:none
 */
export const createStandardSuccessResult = <TData>(
  data: TData,
  context?: ErrorContext & {
    readonly aiNotationMetadata?: unknown
    readonly rateLimit?: unknown
    readonly agentId?: string
    readonly templateGenerated?: boolean
    readonly templateVars?: Record<string, unknown>
    readonly analysis?: Record<string, unknown>
    readonly metadata?: Record<string, unknown>
  }
): StandardResult<TData> => {
  const processingTime = context?.startTime ? Date.now() - context.startTime : 0

  return Object.freeze({
    success: true,
    data,
    processingTime,
    timestamp: Date.now(),
    ...(context?.aiNotationMetadata ? { aiNotationMetadata: context.aiNotationMetadata } : {}),
    ...(context?.rateLimit ? { rateLimit: context.rateLimit } : {}),
    ...(context?.agentId ? { agentId: context.agentId } : {}),
    ...(context?.templateGenerated ? { templateGenerated: context.templateGenerated } : {}),
    ...(context?.templateVars ? { templateVars: context.templateVars } : {}),
    ...(context?.analysis ? { analysis: context.analysis } : {}),
    ...(context?.metadata ? { metadata: context.metadata } : {}),
    ...(context?.additionalData || {})
  } as StandardResult<TData>)
}

/**
 * F:wrapWithStandardErrorHandling - Wrap operation with standardized error handling (CONSOLIDATED PATTERN)
 * @notation P:operation,context F:wrapWithStandardErrorHandling CB:error-handling-wrapper I:StandardResult DB:none
 */
export const wrapWithStandardErrorHandling = async <TData>(
  operation: () => Promise<TData>,
  context: ErrorContext
): Promise<StandardResult<TData>> => {
  const startTime = Date.now()
  try {
    const result = await operation()
    return createStandardSuccessResult(result, { ...context, startTime })
  } catch (error) {
    return createStandardErrorResult(error as Error, { ...context, startTime })
  }
}

/**
 * F:handleOperationError - Handle operation errors with context
 * @notation P:error,operation,component F:handleOperationError CB:error-handling I:StandardResult DB:none
 */
export const handleOperationError = <TData = unknown>(
  error: Error | string,
  operation: string,
  component: string,
  startTime?: number
): StandardResult<TData> => {
  return createStandardErrorResult<TData>(error, {
    operation,
    component,
    startTime
  })
}

/**
 * F:wrapWithErrorHandling - Wrap async operation with standardized error handling
 * @notation P:operation,context,fn F:wrapWithErrorHandling CB:error-handling I:StandardResult DB:none
 */
export const wrapWithErrorHandling = async <TData>(
  operation: string,
  component: string,
  fn: () => Promise<TData>
): Promise<StandardResult<TData>> => {
  const startTime = Date.now()
  
  try {
    const data = await fn()
    return createStandardSuccessResult(data, { operation, component, startTime })
  } catch (error) {
    return handleOperationError(error as Error, operation, component, startTime)
  }
}

/**
 * F:createFileOperationResult - Create file operation result with metadata
 * @notation P:success,data,path,stats F:createFileOperationResult CB:none I:StandardResult DB:none
 */
export const createFileOperationResult = (
  success: boolean,
  data?: unknown,
  error?: string,
  filePath?: string,
  stats?: { size: number; mtime: Date },
  encoding?: string,
  atomic?: boolean
): StandardResult => {
  const baseResult = {
    success,
    processingTime: 0,
    timestamp: Date.now()
  }
  
  if (!success) {
    return Object.freeze({
      ...baseResult,
      error
    })
  }
  
  return Object.freeze({
    ...baseResult,
    data: Object.freeze({
      path: filePath,
      size: stats?.size,
      modified: stats?.mtime.toISOString(),
      encoding,
      atomic,
      ...data
    })
  })
}

/**
 * F:createDatabaseOperationResult - Create database operation result
 * @notation P:success,data,error F:createDatabaseOperationResult CB:none I:StandardResult DB:none
 */
export const createDatabaseOperationResult = (
  success: boolean,
  data?: unknown,
  error?: string
): StandardResult => {
  return Object.freeze({
    success,
    data,
    error,
    processingTime: 0,
    timestamp: Date.now()
  })
}

/**
 * F:createMemoryOperationResult - Create memory operation result
 * @notation P:success,data,error,metadata F:createMemoryOperationResult CB:none I:StandardResult DB:none
 */
export const createMemoryOperationResult = (
  success: boolean,
  data?: unknown,
  error?: string,
  aiNotationMetadata?: unknown
): StandardResult => {
  return Object.freeze({
    success,
    data,
    error,
    aiNotationMetadata
  })
}

/**
 * F:createCoordinationOperationResult - Create coordination operation result
 * @notation P:success,data,error F:createCoordinationOperationResult CB:none I:StandardResult DB:none
 */
export const createCoordinationOperationResult = (
  success: boolean,
  data?: unknown,
  error?: string
): StandardResult => {
  return Object.freeze({
    success,
    data,
    error,
    processingTime: 0,
    timestamp: Date.now()
  })
}

/**
 * F:createFetchOperationResult - Create fetch operation result
 * @notation P:success,data,error F:createFetchOperationResult CB:none I:StandardResult DB:none
 */
export const createFetchOperationResult = (
  success: boolean,
  data?: unknown,
  error?: string
): StandardResult => {
  return Object.freeze({
    success,
    data,
    error,
    timestamp: Date.now(),
    processingTime: 0
  })
}
