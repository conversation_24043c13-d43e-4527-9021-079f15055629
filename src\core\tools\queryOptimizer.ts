/**
 * Query Optimizer - AI-Optimized Pure Functions
 * Simple database query optimization and building utilities
 *
 * @notation P:core/tools/queryOptimizer F:executeOptimizedQuery,QueryBuilder CB:executeOptimizedQuery I:QueryResult,QueryBuilderState DB:queries
 */

import { Database } from 'sqlite3'

/**
 * @I:QueryResult - Query execution result
 * @notation P:none F:none CB:none I:QueryResult DB:queries
 */
export type QueryResult = {
  readonly data?: unknown
  readonly changes?: number
  readonly lastID?: number
}

/**
 * @I:QueryBuilderState - Query builder state
 * @notation P:none F:none CB:none I:QueryBuilderState DB:queries
 */
export type QueryBuilderState = {
  readonly table: string
  readonly selectFields: readonly string[]
  readonly whereConditions: readonly string[]
  readonly whereParams: readonly unknown[]
  readonly limitValue?: number
  readonly offsetValue?: number
  readonly orderBy?: string
}

/**
 * F:executeOptimizedQuery - Execute optimized database query
 * @notation P:db,sql,params F:executeOptimizedQuery CB:executeOptimizedQuery I:QueryResult DB:queries
 */
export const executeOptimizedQuery = async (
  db: Database,
  sql: string,
  params: unknown[] = []
): Promise<QueryResult> => {
  return new Promise((resolve, reject) => {
    const sqlLower = sql.toLowerCase().trim()

    if (sqlLower.startsWith('select')) {
      db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err)
        } else {
          resolve(
            Object.freeze({
              data: Object.freeze(rows)
            })
          )
        }
      })
    } else if (sqlLower.startsWith('insert')) {
      db.run(sql, params, function (err) {
        if (err) {
          reject(err)
        } else {
          resolve(
            Object.freeze({
              data: Object.freeze({ id: this.lastID }),
              changes: this.changes,
              lastID: this.lastID
            })
          )
        }
      })
    } else {
      db.run(sql, params, function (err) {
        if (err) {
          reject(err)
        } else {
          resolve(
            Object.freeze({
              data: Object.freeze({ changes: this.changes }),
              changes: this.changes
            })
          )
        }
      })
    }
  })
}

/**
 * F:createQueryBuilderState - Create initial query builder state
 * @notation P:table F:createQueryBuilderState CB:none I:QueryBuilderState DB:queries
 */
export const createQueryBuilderState = (table: string): QueryBuilderState => {
  return Object.freeze({
    table,
    selectFields: Object.freeze(['*']),
    whereConditions: Object.freeze([]),
    whereParams: Object.freeze([])
  })
}

/**
 * F:addWhereCondition - Add WHERE condition to query builder state
 * @notation P:state,condition,param F:addWhereCondition CB:none I:QueryBuilderState DB:queries
 */
export const addWhereCondition = (
  state: QueryBuilderState,
  condition: string,
  param?: unknown
): QueryBuilderState => {
  return Object.freeze({
    ...state,
    whereConditions: Object.freeze([...state.whereConditions, condition]),
    whereParams:
      param !== undefined ? Object.freeze([...state.whereParams, param]) : state.whereParams
  })
}

/**
 * F:setLimit - Set LIMIT for query builder state
 * @notation P:state,limit F:setLimit CB:none I:QueryBuilderState DB:queries
 */
export const setLimit = (state: QueryBuilderState, limit: number): QueryBuilderState => {
  return Object.freeze({
    ...state,
    limitValue: limit
  })
}

/**
 * F:buildSelectQuery - Build SELECT query from state
 * @notation P:state F:buildSelectQuery CB:none I:object DB:queries
 */
export const buildSelectQuery = (state: QueryBuilderState) => {
  let sql = `SELECT ${state.selectFields.join(', ')} FROM ${state.table}`
  const params = [...state.whereParams]

  if (state.whereConditions.length > 0) {
    sql += ` WHERE ${state.whereConditions.join(' AND ')}`
  }

  if (state.orderBy) {
    sql += ` ORDER BY ${state.orderBy}`
  }

  if (state.limitValue) {
    sql += ` LIMIT ${state.limitValue}`
  }

  if (state.offsetValue) {
    sql += ` OFFSET ${state.offsetValue}`
  }

  return Object.freeze({
    sql,
    params: Object.freeze(params)
  })
}

/**
 * QueryBuilder - Functional query builder class for compatibility
 * @notation P:table F:QueryBuilder CB:none I:QueryBuilder DB:queries
 */
export class QueryBuilder {
  private state: QueryBuilderState

  constructor(table: string) {
    this.state = createQueryBuilderState(table)
  }

  /**
   * F:where - Add WHERE condition
   * @notation P:condition,param F:where CB:none I:QueryBuilder DB:queries
   */
  where(condition: string, param?: unknown): QueryBuilder {
    this.state = addWhereCondition(this.state, condition, param)
    return this
  }

  /**
   * F:limit - Set LIMIT
   * @notation P:limit F:limit CB:none I:QueryBuilder DB:queries
   */
  limit(limit: number): QueryBuilder {
    this.state = setLimit(this.state, limit)
    return this
  }

  /**
   * F:execute - Execute the built query
   * @notation P:db F:execute CB:execute I:QueryResult DB:queries
   */
  async execute(db: Database): Promise<QueryResult> {
    const query = buildSelectQuery(this.state)
    return await executeOptimizedQuery(db, query.sql, [...query.params])
  }

  /**
   * F:toSQL - Get SQL and parameters
   * @notation P:none F:toSQL CB:none I:object DB:queries
   */
  toSQL() {
    return buildSelectQuery(this.state)
  }
}

/**
 * F:sanitizeTableName - Sanitize table name for SQL injection prevention
 * @notation P:tableName F:sanitizeTableName CB:none I:string DB:queries
 */
export const sanitizeTableName = (tableName: string): string => {
  // Remove any non-alphanumeric characters except underscores
  return tableName.replace(/[^a-zA-Z0-9_]/g, '')
}

/**
 * F:sanitizeColumnName - Sanitize column name for SQL injection prevention
 * @notation P:columnName F:sanitizeColumnName CB:none I:string DB:queries
 */
export const sanitizeColumnName = (columnName: string): string => {
  return columnName.replace(/[^a-zA-Z0-9_]/g, '')
}

/**
 * F:createInsertQuery - Create INSERT query
 * @notation P:table,data F:createInsertQuery CB:none I:object DB:queries
 */
export const createInsertQuery = (table: string, data: Record<string, unknown>) => {
  const keys = Object.keys(data)
  const placeholders = keys.map(() => '?').join(',')
  const sql = `INSERT INTO ${sanitizeTableName(table)} (${keys.map(sanitizeColumnName).join(',')}) VALUES (${placeholders})`
  const params = Object.values(data)

  return Object.freeze({
    sql,
    params: Object.freeze(params)
  })
}

/**
 * F:createUpdateQuery - Create UPDATE query
 * @notation P:table,data,filters F:createUpdateQuery CB:none I:object DB:queries
 */
export const createUpdateQuery = (
  table: string,
  data: Record<string, unknown>,
  filters: Record<string, unknown> = {}
) => {
  const setClause = Object.keys(data)
    .map(key => `${sanitizeColumnName(key)} = ?`)
    .join(', ')
  let sql = `UPDATE ${sanitizeTableName(table)} SET ${setClause}`
  let params = [...Object.values(data)]

  if (Object.keys(filters).length > 0) {
    const whereClause = Object.keys(filters)
      .map(key => `${sanitizeColumnName(key)} = ?`)
      .join(' AND ')
    sql += ` WHERE ${whereClause}`
    params = [...params, ...Object.values(filters)]
  }

  return Object.freeze({
    sql,
    params: Object.freeze(params)
  })
}
