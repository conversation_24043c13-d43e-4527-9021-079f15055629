/**
 * Feedback Core - Types, Interfaces, and Core Utilities
 * Split from feedbackProcessor.ts for constraint compliance (≤150 lines)
 *
 * @notation P:core/tools/agent/feedback-core F:calculateDynamicBaseline,getPerformanceThresholds CB:none I:FeedbackResult,PatternAnalysis,PerformanceInsight DB:feedback
 */

import {
  TransformationType,
  RefinementType,
  ToolExecutionResult,
  ContextTransformation,
  ExecutionRefinement,
  FeedbackContext
} from '../../types'

export type FeedbackResult = {
  readonly success: boolean
  readonly transformations: readonly ContextTransformation[]
  readonly refinements: readonly ExecutionRefinement[]
  readonly insights: readonly string[]
  readonly recommendations: readonly string[]
  readonly timestamp: number
  readonly processingTime: number
}

export type PatternAnalysis = {
  readonly pattern: string
  readonly frequency: number
  readonly confidence: number
  readonly examples: readonly string[]
  readonly applicability: readonly string[]
}

export type PerformanceInsight = {
  readonly metric: string
  readonly trend: 'improving' | 'degrading' | 'stable'
  readonly impact: 'low' | 'medium' | 'high'
  readonly recommendation: string
}

export type FeedbackProcessingOptions = {
  readonly enablePatternDetection: boolean
  readonly enablePerformanceAnalysis: boolean
  readonly enableContextTransformation: boolean
  readonly minConfidenceThreshold: number
  readonly maxTransformations: number
}

export const DEFAULT_FEEDBACK_OPTIONS: FeedbackProcessingOptions = Object.freeze({
  enablePatternDetection: true,
  enablePerformanceAnalysis: true,
  enableContextTransformation: true,
  minConfidenceThreshold: 0.7,
  maxTransformations: 10
})

type PerformanceBaseline = {
  readonly executionTime: {
    readonly fast: number
    readonly slow: number
    readonly average: number
  }
  readonly retryCount: {
    readonly acceptable: number
    readonly concerning: number
  }
  readonly successRate: {
    readonly good: number
    readonly poor: number
  }
  readonly lastUpdated: number
}

let performanceBaseline: PerformanceBaseline | null = null

const DEFAULT_PERFORMANCE_THRESHOLDS = Object.freeze({
  executionTime: {
    fast: 1000,
    slow: 5000,
    average: 2500
  },
  retryCount: {
    acceptable: 2,
    concerning: 5
  },
  successRate: {
    good: 0.95,
    poor: 0.8
  },
  lastUpdated: Date.now()
})

/**
 * F:calculateDynamicBaseline - Calculate performance baseline from execution history
 * @notation P:results F:calculateDynamicBaseline CB:none I:PerformanceBaseline DB:feedback
 */
const calculateDynamicBaseline = (results: readonly ToolExecutionResult[]): PerformanceBaseline => {
  if (results.length === 0) {
    return DEFAULT_PERFORMANCE_THRESHOLDS
  }

  const executionTimes = results.map(r => r.executionTime).filter(t => t > 0)
  const retryAttempts = results.map(r => r.metadata?.retryAttempts || 0)
  const successCount = results.filter(r => r.success).length

  const avgExecutionTime =
    executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length
  const maxRetryCount = Math.max(...retryAttempts)
  const successRate = successCount / results.length

  return Object.freeze({
    executionTime: {
      fast: Math.max(500, avgExecutionTime * 0.5),
      slow: avgExecutionTime * 2,
      average: avgExecutionTime
    },
    retryCount: {
      acceptable: Math.min(2, Math.ceil(maxRetryCount * 0.5)),
      concerning: Math.max(3, maxRetryCount)
    },
    successRate: {
      good: Math.max(0.9, successRate * 1.05),
      poor: Math.max(0.7, successRate * 0.8)
    },
    lastUpdated: Date.now()
  })
}

/**
 * F:getPerformanceThresholds - Get current performance thresholds (dynamic or default)
 * @notation P:results F:getPerformanceThresholds CB:none I:PerformanceBaseline DB:feedback
 */
export const getPerformanceThresholds = (results: readonly ToolExecutionResult[]): PerformanceBaseline => {
  const shouldUpdate =
    !performanceBaseline ||
    Date.now() - performanceBaseline.lastUpdated > 3600000 ||
    results.length >= 10

  if (shouldUpdate) {
    performanceBaseline = calculateDynamicBaseline(results)
    console.log('📊 FEEDBACK: Updated dynamic performance baseline')
  }

  return performanceBaseline || DEFAULT_PERFORMANCE_THRESHOLDS
}
