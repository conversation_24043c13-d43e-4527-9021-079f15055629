{"mcp": {"server": {"command": "node", "args": ["launch.js"], "cwd": ".", "env": {"MCP_DB_PATH": ".augment/db/augster.db", "MCP_PORT": "8081", "NODE_ENV": "production"}, "timeout": 30000, "restart_on_failure": true, "max_restarts": 5}, "tools": {"memory.insert": {"auto_approve": true, "schema_validation": true, "allowed_tables": ["traits", "sessions", "failures", "reflections", "tools"], "rate_limit": {"requests_per_minute": 100}}, "memory.query": {"auto_approve": true, "schema_validation": true, "allowed_tables": ["traits", "sessions", "failures", "reflections", "tools"], "max_results": 1000, "rate_limit": {"requests_per_minute": 200}}, "memory.update": {"auto_approve": true, "schema_validation": true, "allowed_tables": ["traits", "sessions", "failures", "reflections", "tools"], "rate_limit": {"requests_per_minute": 50}}}, "logging": {"level": "info", "file": ".augment/logs/mcp-server.log", "max_size": "10MB", "rotate": true}, "database": {"path": ".augment/db/augster.db", "backup_interval": "1h", "backup_retention": "7d"}, "traits": {"quality_threshold": 0.67, "auto_track": true, "evidence_required": true}}, "agent": {"version": "1.0.0", "mode": "distributed", "session_timeout": "30m", "trace_format": "TRACE_STATUS_SUMMARY"}}