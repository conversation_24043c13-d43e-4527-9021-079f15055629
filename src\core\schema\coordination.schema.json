{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Coordination Operations Schema", "description": "Validation schema for coordination operations handler commands", "type": "object", "properties": {"action": {"type": "string", "enum": ["discover", "register", "heartbeat", "message", "delegate", "sync", "resolve", "status", "agent_proposal", "agent_vote", "agent_status", "template"]}, "agentId": {"type": "string", "description": "Agent identifier"}, "targetAgent": {"type": "string", "description": "Target agent identifier"}, "path": {"type": "string", "description": "Template file path for template action"}, "content": {"type": "string", "description": "Template content for template action"}, "options": {"type": "object", "properties": {"capabilities": {"type": "array", "items": {"type": "string"}, "description": "Agent capabilities"}, "location": {"type": "string", "enum": ["local", "network", "all"], "default": "all"}, "timeout": {"type": "integer", "minimum": 1000, "maximum": 30000, "default": 5000}, "endpoint": {"type": "string"}, "metadata": {"type": "object"}, "status": {"type": "string", "enum": ["healthy", "degraded", "unhealthy"]}, "load": {"type": "number", "minimum": 0, "maximum": 1}, "messageType": {"type": "string", "enum": ["request", "response", "notification", "broadcast"]}, "priority": {"type": "string", "enum": ["low", "normal", "high", "urgent"], "default": "normal"}, "taskId": {"type": "string"}, "taskType": {"type": "string"}, "requirements": {"type": "object"}, "payload": {"type": "object"}, "deadline": {"type": "string", "format": "date-time"}, "syncType": {"type": "string", "enum": ["state", "configuration", "data", "full"]}, "targetAgents": {"type": "array", "items": {"type": "string"}}, "data": {"type": "object"}, "consensus": {"type": "boolean", "default": false}, "conflictId": {"type": "string"}, "resolution": {"type": "object"}, "component": {"type": "string", "enum": ["all", "agents", "tasks", "messages", "sync"], "default": "all"}, "detailed": {"type": "boolean", "default": false}, "templateEngine": {"type": "string", "enum": ["simple", "mustache"], "default": "simple"}, "templateVars": {"type": "object", "description": "Template variables for substitution"}, "templateOutputPath": {"type": "string", "description": "Output path for processed template"}}}}, "required": ["action"], "additionalProperties": false}