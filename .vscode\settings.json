{"terminal.integrated.profiles.windows": {"MCP Dev": {"path": "powershell.exe", "args": ["-NoExit", "-ExecutionPolicy", "Bypass", "-Command", "$env:Path='C:\\Users\\<USER>\\node\\node-v20.11.1-win-x64;' + $env:Path; Set-Location -Path '${workspaceFolder}'; npm run mcp:dev"]}, "MCP Prod": {"path": "powershell.exe", "args": ["-NoExit", "-ExecutionPolicy", "Bypass", "-Command", "$env:Path='C:\\Users\\<USER>\\node\\node-v20.11.1-win-x64;' + $env:Path; Set-Location -Path '${workspaceFolder}'; npm run mcp:dev"]}, "MCP Prod (Compiled)": {"path": "powershell.exe", "args": ["-NoExit", "-ExecutionPolicy", "Bypass", "-Command", "$env:Path='C:\\Users\\<USER>\\node\\node-v20.11.1-win-x64;' + $env:Path; Set-Location -Path '${workspaceFolder}'; npm run mcp:dev"]}, "Clean": {"path": "powershell.exe", "args": ["-NoExit", "-ExecutionPolicy", "Bypass", "-Command", "$env:Path='C:\\Users\\<USER>\\node\\node-v20.11.1-win-x64;' + $env:Path; Set-Location -Path '${workspaceFolder}'; npm run mcp:dev"]}, "Ollama Serve": {"path": "powershell.exe", "args": ["-NoExit", "-ExecutionPolicy", "Bypass", "-Command", "$env:Path='C:\\Users\\<USER>\\node\\node-v20.11.1-win-x64;' + $env:Path; Set-Location -Path '${workspaceFolder}'; npm run mcp:dev"]}}, "terminal.integrated.defaultProfile.windows": "<PERSON><PERSON>", "typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "files.associations": {"*.ps1": "powershell"}}