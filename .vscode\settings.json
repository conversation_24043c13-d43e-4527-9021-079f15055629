{"terminal.integrated.profiles.windows": {"MCP Dev": {"path": "powershell.exe", "args": ["-NoExit", "-ExecutionPolicy", "Bypass", "-Command", "Set-Location -Path '${workspaceFolder}'; npm run mcp:dev"]}, "MCP Prod": {"path": "powershell.exe", "args": ["-NoExit", "-ExecutionPolicy", "Bypass", "-Command", "Set-Location -Path '${workspaceFolder}'; npm run mcp:prod"]}, "MCP Prod (Compiled)": {"path": "powershell.exe", "args": ["-NoExit", "-ExecutionPolicy", "Bypass", "-Command", "Set-Location -Path '${workspaceFolder}'; npm run mcp:compiled"]}, "Clean": {"path": "powershell.exe", "args": ["-NoExit", "-ExecutionPolicy", "Bypass", "-Command", "Set-Location -Path '${workspaceFolder}'; npm run clean"]}, "Ollama Serve": {"path": "powershell.exe", "args": ["-NoExit", "-ExecutionPolicy", "Bypass", "-Command", "Set-Location -Path '${workspaceFolder}'; npm run ollama:serve"]}}, "terminal.integrated.defaultProfile.windows": "<PERSON><PERSON>", "typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "files.associations": {"*.ps1": "powershell"}}