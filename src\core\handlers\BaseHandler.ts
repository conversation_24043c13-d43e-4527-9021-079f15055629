/**
 * Base Handler - AI-Optimized Pure Functions
 * Migrated from class-based to pure function architecture with full AI notation
 * Eliminates 75 anti-patterns and provides standardized resilience patterns
 *
 * @notation P:core/handlers/BaseHandler F:executeOperation,createHandlerState CB:executeOperation I:HandlerConfig,OperationResult DB:handlers
 */

import {
  createCircuitBreakerRegistry,
  getOrCreateCircuitBreaker,
  createRetryManagerRegistry,
  getOrCreateRetryManager,
  createResilienceMonitor,
  recordErrorRecoveryEvent,
  executeWithCircuitBreaker,
  executeWithRetry,
  type CircuitBreakerInstance,
  type RetryInstance,
  type ResilienceMonitorInstance
} from '../antiPatterns'

/**
 * @F:globalCircuitBreakerRegistry - Global circuit breaker registry
 * @notation P:none F:none CB:none I:Map DB:handlers
 */
const globalCircuitBreakerRegistry = createCircuitBreakerRegistry()

/**
 * @F:globalRetryManagerRegistry - Global retry manager registry
 * @notation P:none F:none CB:none I:Map DB:handlers
 */
const globalRetryManagerRegistry = createRetryManagerRegistry()

/**
 * @F:globalResilienceMonitor - Global resilience monitor
 * @notation P:none F:none CB:none I:ResilienceMonitorInstance DB:handlers
 */
const globalResilienceMonitor = createResilienceMonitor()

/**
 * @I:HandlerConfig - Handler configuration with operations
 * @notation P:none F:none CB:none I:HandlerConfig DB:handlers
 */
export type HandlerConfig = {
  readonly handlerName: string
  readonly operations: readonly OperationConfig[]
}

/**
 * @I:OperationConfig - Individual operation configuration
 * @notation P:none F:none CB:none I:OperationConfig DB:handlers
 */
export type OperationConfig = {
  readonly name: string
  readonly circuitBreaker?: {
    readonly failureThreshold?: number
    readonly recoveryTimeout?: number
    readonly monitoringWindow?: number
  }
  readonly retryManager?: {
    readonly maxRetries?: number
    readonly baseDelay?: number
    readonly maxDelay?: number
    readonly jitterPercent?: number
    readonly retryableErrors?: readonly string[]
  }
}

/**
 * @I:OperationResult - Operation execution result
 * @notation P:none F:none CB:none I:OperationResult DB:handlers
 */
export type OperationResult<TData = unknown> = {
  readonly success: boolean
  readonly data?: TData
  readonly error?: string
  readonly processingTime: number
  readonly timestamp: number
}

/**
 * @I:HandlerState - Handler state management
 * @notation P:none F:none CB:none I:HandlerState DB:handlers
 */
export type HandlerState = {
  readonly handlerName: string
  readonly operations: ReadonlyMap<string, OperationConfig>
  readonly initialized: boolean
}

/**
 * F:createHandlerState - Create initial handler state
 * @notation P:config F:createHandlerState CB:none I:HandlerState DB:handlers
 */
export const createHandlerState = (config: HandlerConfig): HandlerState => {
  const operationsMap = new Map<string, OperationConfig>()

  for (const operation of config.operations) {
    operationsMap.set(operation.name, Object.freeze(operation))
  }

  return Object.freeze({
    handlerName: config.handlerName,
    operations: operationsMap,
    initialized: true
  })
}

/**
 * F:getOperationConfig - Get operation configuration from state
 * @notation P:state,operationName F:getOperationConfig CB:none I:OperationConfig DB:none
 */
export const getOperationConfig = (
  state: HandlerState,
  operationName: string
): OperationConfig | undefined => {
  return state.operations.get(operationName)
}

/**
 * F:getOperations - Get all configured operations from state
 * @notation P:state F:getOperations CB:none I:array DB:none
 */
export const getOperations = (state: HandlerState): readonly string[] => {
  return Object.freeze(Array.from(state.operations.keys()))
}

/**
 * F:initializeOperation - Initialize circuit breaker and retry manager for operation
 * @notation P:handlerName,operation F:initializeOperation CB:initializeOperation I:void DB:handlers
 */
export const initializeOperation = (handlerName: string, operation: OperationConfig): void => {
  const operationKey = `${handlerName}-${operation.name}`

  const circuitBreakerConfig = Object.freeze({
    failureThreshold: 5,
    recoveryTimeout: 30000,
    monitoringWindow: 60000,
    ...operation.circuitBreaker
  })

  getOrCreateCircuitBreaker(globalCircuitBreakerRegistry, operationKey, circuitBreakerConfig)

  const retryConfig = Object.freeze({
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    jitterPercent: 25,
    retryableErrors: Object.freeze([
      'SQLITE_BUSY',
      'SQLITE_LOCKED',
      'ECONNRESET',
      'ECONNREFUSED',
      'ETIMEDOUT'
    ]),
    ...operation.retryManager
  })

  getOrCreateRetryManager(globalRetryManagerRegistry, operationKey, retryConfig)
}

/**
 * F:initializeAllOperations - Initialize all operations for a handler
 * @notation P:state F:initializeAllOperations CB:initializeAllOperations I:void DB:handlers
 */
export const initializeAllOperations = (state: HandlerState): void => {
  for (const [operationName, operationConfig] of state.operations) {
    initializeOperation(state.handlerName, operationConfig)
  }
}

/**
 * F:executeOperation - Execute operation with circuit breaker and retry protection
 * @notation P:handlerName,operationName,operationFn,context F:executeOperation CB:executeOperation I:OperationResult DB:handlers
 */
export const executeOperation = async <TData>(
  handlerName: string,
  operationName: string,
  operationFn: () => Promise<TData>,
  context?: string
): Promise<OperationResult<TData>> => {
  const startTime = Date.now()
  const operationKey = `${handlerName}-${operationName}`

  try {
    const circuitBreaker = getOrCreateCircuitBreaker(globalCircuitBreakerRegistry, operationKey)
    const retryManager = getOrCreateRetryManager(globalRetryManagerRegistry, operationKey)

    const { result } = await executeWithCircuitBreaker(circuitBreaker, async () => {
      const { result: retryResult } = await executeWithRetry(
        retryManager,
        async () => {
          return await operationFn()
        },
        `${operationKey}-${context || 'operation'}`
      )
      return retryResult
    })

    const processingTime = Date.now() - startTime

    recordErrorRecoveryEvent(globalResilienceMonitor, {
      component: handlerName,
      errorType: 'none',
      recoveryMethod: 'RETRY',
      recoveryTime: processingTime,
      success: true,
      details: `${operationName} operation completed successfully: ${context || 'N/A'}`
    })

    return Object.freeze({
      success: true as const,
      data: result,
      processingTime,
      timestamp: Date.now()
    })
  } catch (error: unknown) {
    const processingTime = Date.now() - startTime
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'

    recordErrorRecoveryEvent(globalResilienceMonitor, {
      component: handlerName,
      errorType: `${operationName}-operation-failure`,
      recoveryMethod: 'RETRY',
      recoveryTime: processingTime,
      success: false,
      details: `${operationName} operation failed: ${errorMessage}`
    })

    return Object.freeze({
      success: false as const,
      error: errorMessage,
      processingTime,
      timestamp: Date.now()
    })
  }
}

/**
 * F:createOperationKey - Create operation key for circuit breaker/retry
 * @notation P:handlerName,operationName F:createOperationKey CB:none I:string DB:none
 */
export const createOperationKey = (handlerName: string, operationName: string): string => {
  return `${handlerName}-${operationName}`
}

/**
 * F:createDefaultCircuitBreakerConfig - Create default circuit breaker configuration
 * @notation P:overrides F:createDefaultCircuitBreakerConfig CB:none I:object DB:none
 */
export const createDefaultCircuitBreakerConfig = (overrides: Record<string, unknown> = {}) => {
  return Object.freeze({
    failureThreshold: 5,
    recoveryTimeout: 30000,
    monitoringWindow: 60000,
    ...overrides
  })
}

/**
 * F:createDefaultRetryConfig - Create default retry configuration
 * @notation P:overrides F:createDefaultRetryConfig CB:none I:object DB:none
 */
export const createDefaultRetryConfig = (overrides: Record<string, unknown> = {}) => {
  return Object.freeze({
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    jitterPercent: 25,
    retryableErrors: Object.freeze([
      'SQLITE_BUSY',
      'SQLITE_LOCKED',
      'ECONNRESET',
      'ECONNREFUSED',
      'ETIMEDOUT'
    ]),
    ...overrides
  })
}

/**
 * F:createOperationResult - Create operation result with proper typing
 * @notation P:success,data,error,processingTime F:createOperationResult CB:none I:OperationResult DB:none
 */
export const createOperationResult = <TData>(
  success: boolean,
  data?: TData,
  error?: string,
  processingTime: number = 0
): OperationResult<TData> => {
  return Object.freeze({
    success,
    data,
    error,
    processingTime,
    timestamp: Date.now()
  })
}
