{"BA": "src/core/tools/batchAnalyzer.ts", "TS": "src/core/tools/tsValidator.ts", "DB": "src/core/tools/dbMigrator.ts", "ANTI": "src/core/antiPatterns/detector.ts", "CTX": "src/core/notation/contextEngine.ts", "REPORT": "src/core/report/generateManifest.ts", "SYMBOL": "src/core/notation/symbolRegistry.ts", "UNDO": "src/core/report/undoTransformer.ts", "CHAIN": "src/core/handlers/chainExecutor.ts", "EXECUTE": "src/core/handlers/executeCommand.ts", "LAUNCH": "src/runtime/launch.ts", "ENV": "src/core/state/environment.ts", "STATE": "src/core/state/memoryState.ts", "VALIDATE_SCHEMA": "src/core/schema/validateSchema.ts", "BACKUP": "src/core/state/dbBackup.ts", "MIGRATE": "src/core/tools/schemaMigrator.ts", "TEMPLATE": "src/core/tools/templateProcessor.ts", "AI_VALIDATE": "src/core/notation/validateSymbolContracts.ts", "PERF": "src/core/report/performanceReporter.ts", "LOG": "src/core/state/logWriter.ts"}