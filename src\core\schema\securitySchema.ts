/**
 * Security Schema - AI-Optimized Security Validation Types
 * Migrated from utils/security.ts to pure schema architecture
 *
 * @notation P:core/schema/securitySchema F:validateSecurityEvent,validateSecurityResult CB:none I:SecurityEvent,SecurityValidationResult DB:security
 */

export type SecurityValidationResult = {
  readonly isValid: boolean
  readonly reason?: string
  readonly sanitized?: string
}

export type SecurityEvent = {
  readonly type: 'command_injection' | 'ssrf_attempt' | 'validation_failure'
  readonly severity: 'low' | 'medium' | 'high' | 'critical'
  readonly source: string
  readonly details: unknown
  readonly timestamp: string
  readonly blocked: boolean
}

export type SecurityConfig = {
  readonly maxCommandLength: number
  readonly maxArgLength: number
  readonly allowedProtocols: readonly string[]
  readonly blockedDomains: readonly string[]
  readonly privateIPRanges: readonly RegExp[]
  readonly dangerousPatterns: RegExp
}

export type CommandValidationOptions = {
  readonly command: string
  readonly args: readonly string[]
  readonly allowedCommands: ReadonlySet<string>
  readonly maxLength: number
}

export type URLValidationOptions = {
  readonly url: string
  readonly allowedDomains?: readonly string[]
  readonly allowPrivateIPs?: boolean
  readonly allowedProtocols?: readonly string[]
}

/**
 * F:validateSecurityEvent - Validate security event structure
 * @notation P:event F:validateSecurityEvent CB:none I:boolean DB:none
 */
export const validateSecurityEvent = (event: unknown): event is SecurityEvent => {
  if (!event || typeof event !== 'object') return false

  const e = event as Record<string, unknown>

  return (
    typeof e.type === 'string' &&
    ['command_injection', 'ssrf_attempt', 'validation_failure'].includes(e.type) &&
    typeof e.severity === 'string' &&
    ['low', 'medium', 'high', 'critical'].includes(e.severity) &&
    typeof e.source === 'string' &&
    typeof e.timestamp === 'string' &&
    typeof e.blocked === 'boolean'
  )
}

/**
 * F:validateSecurityResult - Validate security validation result structure
 * @notation P:result F:validateSecurityResult CB:none I:boolean DB:none
 */
export const validateSecurityResult = (result: unknown): result is SecurityValidationResult => {
  if (!result || typeof result !== 'object') return false

  const r = result as Record<string, unknown>

  return (
    typeof r.isValid === 'boolean' &&
    (r.reason === undefined || typeof r.reason === 'string') &&
    (r.sanitized === undefined || typeof r.sanitized === 'string')
  )
}

/**
 * F:validateCommandValidationOptions - Validate command validation options
 * @notation P:options F:validateCommandValidationOptions CB:none I:boolean DB:none
 */
export const validateCommandValidationOptions = (
  options: unknown
): options is CommandValidationOptions => {
  if (!options || typeof options !== 'object') return false

  const o = options as Record<string, unknown>

  return (
    typeof o.command === 'string' &&
    Array.isArray(o.args) &&
    o.args.every(arg => typeof arg === 'string') &&
    o.allowedCommands instanceof Set &&
    typeof o.maxLength === 'number'
  )
}

/**
 * F:validateURLValidationOptions - Validate URL validation options
 * @notation P:options F:validateURLValidationOptions CB:none I:boolean DB:none
 */
export const validateURLValidationOptions = (options: unknown): options is URLValidationOptions => {
  if (!options || typeof options !== 'object') return false

  const o = options as Record<string, unknown>

  return (
    typeof o.url === 'string' &&
    (o.allowedDomains === undefined ||
      (Array.isArray(o.allowedDomains) && o.allowedDomains.every(d => typeof d === 'string'))) &&
    (o.allowPrivateIPs === undefined || typeof o.allowPrivateIPs === 'boolean') &&
    (o.allowedProtocols === undefined ||
      (Array.isArray(o.allowedProtocols) && o.allowedProtocols.every(p => typeof p === 'string')))
  )
}

export const DEFAULT_SECURITY_CONFIG: SecurityConfig = Object.freeze({
  maxCommandLength: 1000,
  maxArgLength: 500,
  allowedProtocols: Object.freeze(['http:', 'https:']),
  blockedDomains: Object.freeze([
    'localhost',
    'metadata.google.internal',
    'metadata.goog',
    '***************',
    'metadata.azure.com'
  ]),
  privateIPRanges: Object.freeze([
    /^10\./,
    /^172\.(1[6-9]|2[0-9]|3[01])\./,
    /^192\.168\./,
    /^127\./,
    /^169\.254\./,
    /^0\./
  ]),
  dangerousPatterns: /[;&|`$(){}[\]\\]|\$\(|`[^`]*`|\|\||&&|[><*]/
})

export const ALLOWED_GIT_COMMANDS = Object.freeze(
  new Set([
    'status',
    'log',
    'diff',
    'branch',
    'checkout',
    'commit',
    'push',
    'pull',
    'remote',
    'add',
    'reset',
    'show',
    'config'
  ])
)

export const ALLOWED_TERMINAL_COMMANDS = Object.freeze(
  new Set([
    'pwd',
    'ls',
    'cat',
    'echo',
    'which',
    'env',
    'ps',
    'whoami',
    'date',
    'uptime',
    'df',
    'du',
    'free',
    'top',
    'htop'
  ])
)

export const SECURITY_EVENT_TYPES = Object.freeze([
  'command_injection',
  'ssrf_attempt',
  'validation_failure'
] as const)

export const SECURITY_SEVERITY_LEVELS = Object.freeze([
  'low',
  'medium',
  'high',
  'critical'
] as const)

/**
 * F:createSecurityValidationResult - Create security validation result
 * @notation P:isValid,reason,sanitized F:createSecurityValidationResult CB:none I:SecurityValidationResult DB:none
 */
export const createSecurityValidationResult = (
  isValid: boolean,
  reason?: string,
  sanitized?: string
): SecurityValidationResult => {
  return Object.freeze({
    isValid,
    reason,
    sanitized
  })
}

/**
 * F:createSecurityEvent - Create security event
 * @notation P:type,severity,source,details,blocked F:createSecurityEvent CB:none I:SecurityEvent DB:security
 */
export const createSecurityEvent = (
  type: SecurityEvent['type'],
  severity: SecurityEvent['severity'],
  source: string,
  details: unknown,
  blocked: boolean
): SecurityEvent => {
  return Object.freeze({
    type,
    severity,
    source,
    details,
    timestamp: new Date().toISOString(),
    blocked
  })
}

export class SecurityValidationError extends Error {
  constructor(
    message: string,
    public readonly validationResult: SecurityValidationResult
  ) {
    super(message)
    this.name = 'SecurityValidationError'
  }
}

export class CommandInjectionError extends SecurityValidationError {
  constructor(command: string, reason: string) {
    super(`Command injection detected: ${command}`, createSecurityValidationResult(false, reason))
    this.name = 'CommandInjectionError'
  }
}

export class SSRFAttemptError extends SecurityValidationError {
  constructor(url: string, reason: string) {
    super(`SSRF attempt detected: ${url}`, createSecurityValidationResult(false, reason))
    this.name = 'SSRFAttemptError'
  }
}

// === TYPE GUARDS ===

/**
 * F:isSecurityEvent - Type guard for security event
 * @notation P:value F:isSecurityEvent CB:none I:boolean DB:none
 */
export const isSecurityEvent = (value: unknown): value is SecurityEvent => {
  return validateSecurityEvent(value)
}

/**
 * F:isSecurityValidationResult - Type guard for security validation result
 * @notation P:value F:isSecurityValidationResult CB:none I:boolean DB:none
 */
export const isSecurityValidationResult = (value: unknown): value is SecurityValidationResult => {
  return validateSecurityResult(value)
}

/**
 * F:isCommandValidationOptions - Type guard for command validation options
 * @notation P:value F:isCommandValidationOptions CB:none I:boolean DB:none
 */
export const isCommandValidationOptions = (value: unknown): value is CommandValidationOptions => {
  return validateCommandValidationOptions(value)
}

/**
 * F:isURLValidationOptions - Type guard for URL validation options
 * @notation P:value F:isURLValidationOptions CB:none I:boolean DB:none
 */
export const isURLValidationOptions = (value: unknown): value is URLValidationOptions => {
  return validateURLValidationOptions(value)
}
