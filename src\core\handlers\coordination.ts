/**
 * Coordination Handler - AI-Optimized Pure Functions
 * Migrated from BaseHandler class to pure function architecture
 *
 * @notation P:core/handlers/coordination F:coordinationHandler,executeCoordinationCommand CB:executeCoordinationCommand I:CoordinationCommand,CoordinationResult DB:coordination
 */

import { Database } from 'sqlite3'
import { processMultiHandlerTemplate } from '../tools'
import { CoordinationCommand, Agent, Message, WorkspaceResource, AgentProposal } from '../types'
import {
  HandlerConfig,
  OperationResult,
  createHandlerConfig,
  executeWithResilience
} from './executeCommand'

export type CoordinationResult = {
  readonly success: boolean
  readonly data?: unknown
  readonly error?: string
  readonly agentId?: string
  readonly processingTime?: number
  readonly timestamp?: number
}

/**
 * F:createCoordinationConfig - Create coordination handler configuration
 * @notation P:none F:createCoordinationConfig CB:none I:HandlerConfig DB:none
 */
export const createCoordinationConfig = (): HandlerConfig => {
  return createHandlerConfig(
    'coordination',
    [
      'discover',
      'register',
      'heartbeat',
      'message',
      'delegate',
      'sync',
      'resolve',
      'status',
      'agent_proposal',
      'agent_vote',
      'agent_status',
      'template'
    ],
    {
      failureThreshold: 5,
      recoveryTimeout: 30000,
      maxRetries: 3,
      baseDelay: 1000
    }
  )
}

/**
 * F:validateCoordinationCommand - Validate coordination command structure
 * @notation P:command F:validateCoordinationCommand CB:none I:boolean DB:none
 */
export const validateCoordinationCommand = (command: unknown): command is CoordinationCommand => {
  if (!command || typeof command !== 'object') return false

  const cmd = command as Record<string, unknown>

  return (
    typeof cmd.action === 'string' &&
    [
      'discover',
      'register',
      'heartbeat',
      'message',
      'delegate',
      'sync',
      'resolve',
      'status',
      'agent_proposal',
      'agent_vote',
      'agent_status',
      'template'
    ].includes(cmd.action)
  )
}

// State management - functional approach with immutable patterns
const coordinationState = Object.freeze({
  agents: new Map<string, Agent>(),
  messages: new Map<string, Message>(),
  messageQueue: [] as Message[],
  resources: new Map<string, WorkspaceResource>(),
  consensusState: new Map<string, any>(),
  agentProposals: new Map<string, AgentProposal>(),
  freeToolAgents: new Map<string, any>()
})

/**
 * F:generateId - Generate unique ID with prefix
 * @notation P:prefix F:generateId CB:none I:string DB:none
 */
export const generateId = (prefix: string): string =>
  `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

/**
 * F:validateAgent - Validate agent ID for operation
 * @notation P:agentId,operation F:validateAgent CB:none I:void DB:none
 */
export const validateAgent = (agentId?: string, operation = 'operation'): void => {
  if (!agentId) throw new Error(`Agent ID required for ${operation}`)
}

/**
 * F:executeCoordinationDiscover - Execute coordination discover operation
 * @notation P:none F:executeCoordinationDiscover CB:executeCoordinationDiscover I:CoordinationResult DB:coordination
 */
export const executeCoordinationDiscover = async (): Promise<CoordinationResult> => {
  try {
    const agents = Array.from(coordinationState.agents.values())
    const activeAgents = agents.filter(
      (agent: Agent) => Date.now() - (agent.lastHeartbeat || 0) < 30000
    )

    return Object.freeze({
      success: true,
      data: Object.freeze({
        agents: Object.freeze(activeAgents),
        totalCount: activeAgents.length,
        timestamp: Date.now()
      }),
      processingTime: 0,
      timestamp: Date.now()
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message
    })
  }
}

/**
 * F:executeCoordinationRegister - Execute coordination register operation
 * @notation P:agentId,options F:executeCoordinationRegister CB:executeCoordinationRegister I:CoordinationResult DB:coordination
 */
export const executeCoordinationRegister = async (
  agentId: string,
  options: Record<string, unknown> = {}
): Promise<CoordinationResult> => {
  try {
    validateAgent(agentId, 'registration')

    const agent: Agent = Object.freeze({
      id: agentId,
      capabilities: Object.freeze((options.capabilities as string[]) || []),
      priority: (options.agentPriority as number) || 1,
      maxLoad: (options.maxLoad as number) || 10,
      currentLoad: 0,
      status: 'active',
      lastHeartbeat: Date.now()
    })

    coordinationState.agents.set(agentId, agent)

    return Object.freeze({
      success: true,
      data: Object.freeze({
        agentId,
        status: 'registered',
        capabilities: agent.capabilities,
        priority: agent.priority,
        maxLoad: agent.maxLoad
      }),
      agentId,
      processingTime: 0,
      timestamp: Date.now()
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message
    })
  }
}

/**
 * F:executeCoordinationHeartbeat - Execute coordination heartbeat operation
 * @notation P:agentId F:executeCoordinationHeartbeat CB:executeCoordinationHeartbeat I:CoordinationResult DB:coordination
 */
export const executeCoordinationHeartbeat = async (
  agentId: string
): Promise<CoordinationResult> => {
  try {
    validateAgent(agentId, 'heartbeat')

    const agent = coordinationState.agents.get(agentId)
    if (!agent) {
      throw new Error(`Agent ${agentId} not found. Please register first.`)
    }

    const updatedAgent = Object.freeze({
      ...agent,
      lastHeartbeat: Date.now(),
      status: 'active' as const
    })

    coordinationState.agents.set(agentId, updatedAgent)

    return Object.freeze({
      success: true,
      data: Object.freeze({
        agentId,
        timestamp: Date.now(),
        status: 'heartbeat_received'
      }),
      agentId,
      processingTime: 0,
      timestamp: Date.now()
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message
    })
  }
}

/**
 * F:executeCoordinationStatus - Execute coordination status operation
 * @notation P:none F:executeCoordinationStatus CB:executeCoordinationStatus I:CoordinationResult DB:coordination
 */
export const executeCoordinationStatus = async (): Promise<CoordinationResult> => {
  try {
    const allAgents = Array.from(coordinationState.agents.values())
    const activeAgents = allAgents.filter(a => Date.now() - (a.lastHeartbeat || 0) < 30000)

    return Object.freeze({
      success: true,
      data: Object.freeze({
        totalAgents: coordinationState.agents.size,
        activeAgents: activeAgents.length,
        messageQueue: coordinationState.messageQueue.length,
        resources: coordinationState.resources.size,
        proposals: coordinationState.agentProposals.size
      }),
      processingTime: 0,
      timestamp: Date.now()
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message
    })
  }
}

/**
 * F:executeCoordinationCommand - Execute coordination command with resilience
 * @notation P:command F:executeCoordinationCommand CB:executeCoordinationCommand I:OperationResult DB:coordination
 */
export const executeCoordinationCommand = async (
  command: CoordinationCommand
): Promise<OperationResult<CoordinationResult>> => {
  const config = createCoordinationConfig()

  return await executeWithResilience(command, config, async cmd => {
    const { action, agentId, options = {} } = cmd

    switch (action) {
      case 'discover':
        return await executeCoordinationDiscover()

      case 'register':
        if (!agentId) throw new Error('Agent ID required for registration')
        return await executeCoordinationRegister(agentId, options)

      case 'heartbeat':
        if (!agentId) throw new Error('Agent ID required for heartbeat')
        return await executeCoordinationHeartbeat(agentId)

      case 'status':
        return await executeCoordinationStatus()

      case 'template':
        const templateSource = cmd.content || cmd.path
        if (!templateSource)
          throw new Error('Either path or content must be provided for template action')
        return await executeCoordinationTemplate(
          templateSource,
          (options?.templateVars as Record<string, unknown>) || {},
          (options?.templateEngine as 'simple' | 'mustache') || 'simple'
        )

      default:
        throw new Error(`Unknown coordination action: ${action}`)
    }
  })
}

/**
 * F:executeCoordinationTemplate - Execute coordination template operation
 * @notation P:templateSource,vars,engine F:executeCoordinationTemplate CB:executeCoordinationTemplate I:CoordinationResult DB:none
 */
export const executeCoordinationTemplate = async (
  templateSource: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple'
): Promise<CoordinationResult> => {
  try {
    const result = await processMultiHandlerTemplate(templateSource, vars, engine)

    return Object.freeze({
      success: true,
      data: result.content,
      processingTime: 0,
      timestamp: Date.now()
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message
    })
  }
}

/**
 * F:coordinationHandler - Create coordination handler function
 * @notation P:db F:coordinationHandler CB:none I:function DB:coordination
 */
export const coordinationHandler = (_db: Database) => {
  return {
    execute: async (input: unknown): Promise<CoordinationResult> => {
      if (!validateCoordinationCommand(input)) {
        return Object.freeze({
          success: false,
          error: 'Invalid coordination command structure'
        })
      }

      const result = await executeCoordinationCommand(input)
      return result.success
        ? result.data
        : Object.freeze({
            success: false,
            error: result.error
          })
    }
  }
}
