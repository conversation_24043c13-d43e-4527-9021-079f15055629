{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Monitoring Operations Schema", "description": "Validation schema for monitoring operations handler commands", "type": "object", "properties": {"action": {"type": "string", "enum": ["health", "metrics", "dashboard", "analytics", "template"]}, "path": {"type": "string", "description": "Template file path for template action"}, "content": {"type": "string", "description": "Template content for template action"}, "options": {"type": "object", "properties": {"component": {"type": "string", "enum": ["all", "circuit-breakers", "handlers", "database", "memory", "file", "github", "coordination"], "default": "all"}, "detailed": {"type": "boolean", "default": false}, "timeRange": {"type": "string", "enum": ["1h", "6h", "24h", "7d", "30d"], "default": "1h"}, "handler": {"type": "string", "enum": ["all", "memory", "file", "database", "github", "monitoring", "coordination"], "default": "all"}, "includePerformance": {"type": "boolean", "default": true}, "includeErrors": {"type": "boolean", "default": true}, "format": {"type": "string", "enum": ["html", "json", "text"], "default": "html"}, "refresh": {"type": "boolean", "default": true}, "theme": {"type": "string", "enum": ["light", "dark", "auto"], "default": "auto"}, "sections": {"type": "array", "items": {"type": "string", "enum": ["overview", "circuit-breakers", "performance", "errors", "handlers", "system-health"]}, "default": ["overview", "circuit-breakers", "performance", "handlers"]}, "templateEngine": {"type": "string", "enum": ["simple", "mustache"], "default": "simple"}, "templateVars": {"type": "object", "description": "Template variables for substitution"}, "templateOutputPath": {"type": "string", "description": "Output path for processed template"}}}}, "required": ["action"], "additionalProperties": false}