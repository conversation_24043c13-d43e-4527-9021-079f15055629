/**
 * Terminal Handler - AI-Optimized Pure Functions
 * Migrated from BaseHandler class to pure function architecture
 *
 * @notation P:core/handlers/terminal F:terminalHand<PERSON>,executeTerminalCommand CB:executeTerminalCommand I:TerminalCommand,TerminalResult DB:terminal
 */

import * as fs from 'fs'
import * as path from 'path'
import { execSync } from 'child_process'
import { processMultiHandlerTemplate } from '../tools'
import {
  HandlerConfig,
  OperationResult,
  createHandlerConfig,
  executeWithResilience
} from './executeCommand'

export type TerminalCommand = {
  readonly action: 'execute' | 'which' | 'pwd' | 'env' | 'ps' | 'template'
  readonly cmd?: string
  readonly args?: readonly string[]
  readonly workingDirectory?: string
  readonly path?: string
  readonly content?: string
  readonly options?: {
    readonly timeout?: number
    readonly maxBuffer?: number
    readonly env?: Record<string, string>
    readonly shell?: boolean
    readonly encoding?: 'utf8' | 'buffer'
    readonly templateEngine?: 'simple' | 'mustache'
    readonly templateVars?: Record<string, unknown>
    readonly templateOutputPath?: string
  }
}

export type TerminalResult = {
  readonly success: boolean
  readonly data?: {
    readonly command: string
    readonly workingDirectory: string
    readonly stdout: string
    readonly stderr: string
    readonly exitCode: number
    readonly executionTime: number
    readonly pid?: number
  }
  readonly error?: string
  readonly timestamp: number
  readonly processingTime: number
}

/**
 * F:createTerminalConfig - Create terminal handler configuration
 * @notation P:none F:createTerminalConfig CB:none I:HandlerConfig DB:none
 */
export const createTerminalConfig = (): HandlerConfig => {
  return createHandlerConfig('terminal', ['execute', 'which', 'pwd', 'env', 'ps', 'template'], {
    failureThreshold: 5,
    recoveryTimeout: 30000,
    maxRetries: 3,
    baseDelay: 1000
  })
}

/**
 * F:validateTerminalCommand - Validate terminal command structure
 * @notation P:command F:validateTerminalCommand CB:none I:boolean DB:none
 */
export const validateTerminalCommand = (command: unknown): command is TerminalCommand => {
  if (!command || typeof command !== 'object') return false

  const cmd = command as Record<string, unknown>

  return (
    typeof cmd.action === 'string' &&
    ['execute', 'which', 'pwd', 'env', 'ps', 'template'].includes(cmd.action)
  )
}

// @removed:ALLOWED_COMMANDS - Phase 11A.P.8 - unused constant

const BLOCKED_COMMANDS = Object.freeze([
  'rm',
  'rmdir',
  'del',
  'delete',
  'format',
  'fdisk',
  'sudo',
  'su',
  'chmod',
  'chown',
  'passwd',
  'kill',
  'killall',
  'pkill',
  'shutdown',
  'reboot',
  'halt',
  'dd',
  'mount',
  'umount',
  'fsck',
  'iptables',
  'ufw',
  'firewall-cmd',
  'crontab',
  'at',
  'batch',
  'nc',
  'netcat',
  'telnet',
  'ssh',
  'scp',
  'rsync'
])

/**
 * F:validateTerminalCommandSecurity - Validate command for security using enhanced CommandSanitizer
 * @notation P:command F:validateTerminalCommandSecurity CB:validateTerminalCommandSecurity I:object DB:terminal
 */
export const validateTerminalCommandSecurity = async (
  command: string
): Promise<{ allowed: boolean; reason?: string }> => {
  if (!command || typeof command !== 'string') {
    return { allowed: false, reason: 'Command must be a non-empty string' }
  }

  const commandParts = command.trim().split(/\s+/)
  const baseCommand = commandParts[0]
  // @removed:args - Phase 11A.P.8 - unused after security validation removal

  // @removed:CommandSanitizer_validation - Phase 11A.P.8 - security validation removed for core functionality

  if (BLOCKED_COMMANDS.includes(baseCommand)) {
    // @removed:SecurityLogger - Phase 11A.P.8 - security logging removed for core functionality
    return { allowed: false, reason: `Command '${baseCommand}' is blocked for security reasons` }
  }

  return { allowed: true }
}

/**
 * F:validateWorkingDirectory - Validate working directory
 * @notation P:workingDir F:validateWorkingDirectory CB:none I:object DB:none
 */
export const validateWorkingDirectory = (
  workingDir?: string
): {
  valid: boolean
  path?: string
  error?: string
} => {
  try {
    const workspaceRoot = process.cwd()
    const targetDir = workingDir ? path.resolve(workspaceRoot, workingDir) : workspaceRoot

    if (!targetDir.startsWith(workspaceRoot)) {
      return { valid: false, error: 'Working directory must be within workspace root' }
    }

    if (!fs.existsSync(targetDir)) {
      return { valid: false, error: `Working directory does not exist: ${targetDir}` }
    }

    if (!fs.statSync(targetDir).isDirectory()) {
      return { valid: false, error: `Path is not a directory: ${targetDir}` }
    }

    return { valid: true, path: targetDir }
  } catch (error) {
    return { valid: false, error: `Invalid working directory: ${(error as Error).message}` }
  }
}

/**
 * F:executeTerminalCommandSafely - Execute terminal command safely
 * @notation P:command,workingDirectory,options F:executeTerminalCommandSafely CB:executeTerminalCommandSafely I:TerminalResult DB:terminal
 */
export const executeTerminalCommandSafely = async (
  command: string,
  workingDirectory?: string,
  options: TerminalCommand['options'] = {}
): Promise<TerminalResult> => {
  const startTime = Date.now()

  try {
    const commandValidation = await validateTerminalCommandSecurity(command)
    if (!commandValidation.allowed) {
      throw new Error(commandValidation.reason)
    }

    const dirValidation = validateWorkingDirectory(workingDirectory)
    if (!dirValidation.valid) {
      throw new Error(dirValidation.error)
    }

    const workingDir = dirValidation.path!
    const timeout = options?.timeout || 30000
    const maxBuffer = options?.maxBuffer || 1024 * 1024
    const encoding = options?.encoding || 'utf8'

    const execOptions: Record<string, unknown> = {
      cwd: workingDir,
      timeout,
      maxBuffer,
      env: {
        ...process.env,
        ...options?.env,
        PATH: process.env.PATH,
        HOME: process.env.HOME,
        USER: process.env.USER
      },
      shell: options?.shell !== false
    }

    if (encoding === 'utf8') {
      execOptions.encoding = 'utf8'
    }

    const result = execSync(command, execOptions)

    return Object.freeze({
      success: true,
      data: Object.freeze({
        command,
        workingDirectory: workingDir,
        stdout: result.toString(),
        stderr: '',
        exitCode: 0,
        executionTime: Date.now() - startTime
      }),
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message || 'Command execution failed',
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  }
}

/**
 * F:executeTerminalPwd - Get current working directory
 * @notation P:none F:executeTerminalPwd CB:executeTerminalPwd I:TerminalResult DB:terminal
 */
export const executeTerminalPwd = async (): Promise<TerminalResult> => {
  const startTime = Date.now()

  try {
    const cwd = process.cwd()

    return Object.freeze({
      success: true,
      data: Object.freeze({
        command: 'pwd',
        workingDirectory: cwd,
        stdout: cwd,
        stderr: '',
        exitCode: 0,
        executionTime: Date.now() - startTime
      }),
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message,
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  }
}

/**
 * F:executeTerminalWhich - Check if command exists
 * @notation P:command F:executeTerminalWhich CB:executeTerminalWhich I:TerminalResult DB:terminal
 */
export const executeTerminalWhich = async (command: string): Promise<TerminalResult> => {
  const startTime = Date.now()

  try {
    if (!command || !/^[a-zA-Z0-9._-]+$/.test(command)) {
      throw new Error('Invalid command name for which operation')
    }

    const result = execSync(`which ${command}`, {
      encoding: 'utf8',
      timeout: 5000
    })

    return Object.freeze({
      success: true,
      data: Object.freeze({
        command: `which ${command}`,
        workingDirectory: process.cwd(),
        stdout: result.toString().trim(),
        stderr: '',
        exitCode: 0,
        executionTime: Date.now() - startTime
      }),
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  } catch {
    return Object.freeze({
      success: false,
      error: `Command '${command}' not found`,
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  }
}

/**
 * F:executeTerminalEnv - Get environment variables (filtered for security)
 * @notation P:none F:executeTerminalEnv CB:executeTerminalEnv I:TerminalResult DB:terminal
 */
export const executeTerminalEnv = async (): Promise<TerminalResult> => {
  const startTime = Date.now()

  try {
    const safeEnvVars = Object.freeze([
      'PATH',
      'HOME',
      'USER',
      'SHELL',
      'TERM',
      'LANG',
      'LC_ALL',
      'NODE_ENV',
      'NODE_VERSION',
      'NPM_VERSION',
      'PWD',
      'OLDPWD'
    ])

    const filteredEnv = Object.entries(process.env)
      .filter(([key]) => safeEnvVars.includes(key))
      .map(([key, value]) => `${key}=${value}`)
      .join('\n')

    return Object.freeze({
      success: true,
      data: Object.freeze({
        command: 'env',
        workingDirectory: process.cwd(),
        stdout: filteredEnv,
        stderr: '',
        exitCode: 0,
        executionTime: Date.now() - startTime
      }),
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message,
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  }
}

/**
 * F:executeTerminalCommand - Execute terminal command with resilience
 * @notation P:command F:executeTerminalCommand CB:executeTerminalCommand I:OperationResult DB:terminal
 */
export const executeTerminalCommand = async (
  command: TerminalCommand
): Promise<OperationResult<TerminalResult>> => {
  const config = createTerminalConfig()

  return await executeWithResilience(command, config, async cmd => {
    const { action, cmd: cmdString, workingDirectory, options } = cmd

    switch (action) {
      case 'execute':
        if (!cmdString) throw new Error('Command is required for execute action')
        return await executeTerminalCommandSafely(cmdString, workingDirectory, options)

      case 'pwd':
        return await executeTerminalPwd()

      case 'which':
        if (!cmdString) throw new Error('Command is required for which action')
        return await executeTerminalWhich(cmdString)

      case 'env':
        return await executeTerminalEnv()

      case 'ps':
        return await executeTerminalCommandSafely('ps aux', workingDirectory, options)

      case 'template':
        const templateSource = cmd.content || cmd.path
        if (!templateSource)
          throw new Error('Either path or content must be provided for template action')
        return await executeTerminalTemplate(
          templateSource,
          options?.templateVars || {},
          options?.templateEngine || 'simple'
        )

      default:
        throw new Error(`Unknown terminal action: ${action}`)
    }
  })
}

/**
 * F:executeTerminalTemplate - Execute terminal template operation
 * @notation P:templateSource,vars,engine F:executeTerminalTemplate CB:executeTerminalTemplate I:TerminalResult DB:none
 */
export const executeTerminalTemplate = async (
  templateSource: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple'
): Promise<TerminalResult> => {
  try {
    const result = await processMultiHandlerTemplate(templateSource, vars, engine)

    return Object.freeze({
      success: true,
      data: Object.freeze({
        command: 'template',
        workingDirectory: process.cwd(),
        stdout: result.content,
        stderr: '',
        exitCode: 0,
        executionTime: 0
      }),
      timestamp: Date.now(),
      processingTime: 0
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message,
      timestamp: Date.now(),
      processingTime: 0
    })
  }
}

/**
 * F:terminalHandler - Create terminal handler function
 * @notation P:db F:terminalHandler CB:none I:function DB:terminal
 */
export const terminalHandler = () => {
  return {
    execute: async (input: unknown): Promise<TerminalResult> => {
      if (!validateTerminalCommand(input)) {
        return Object.freeze({
          success: false,
          error: 'Invalid terminal command structure',
          timestamp: Date.now(),
          processingTime: 0
        })
      }

      const result = await executeTerminalCommand(input)
      return result.success
        ? result.data
        : Object.freeze({
            success: false,
            error: result.error,
            timestamp: Date.now(),
            processingTime: 0
          })
    }
  }
}
