/**
 * Agent Engine Lifecycle - Agent Lifecycle Management and Main Loop
 * @notation P:core/tools/agent/engine-lifecycle F:executeAgentLoop,startAgentState,stopAgentState CB:agentLifecycle I:AgentState,AgentResult DB:agent
 */

import { Database } from 'sqlite3'
import { AgentState, AgentConfig, AgentResult, createDefaultAgentConfig, createAgentState, consumeContextualState, loadAgentDirectives } from './engine-core'
import { initializeHandlerRegistry } from './engine-handlers'

/**
 * F:startAgentState - Start agent state
 * @notation P:state F:startAgentState CB:none I:AgentState DB:agent
 */
export const startAgentState = (state: AgentState): AgentState => {
  return Object.freeze({
    ...state,
    isRunning: true,
    startTime: Date.now(),
    lastHeartbeat: Date.now()
  })
}

/**
 * F:stopAgentState - Stop agent state
 * @notation P:state F:stopAgentState CB:none I:AgentState DB:agent
 */
export const stopAgentState = (state: AgentState): AgentState => {
  return Object.freeze({
    ...state,
    isRunning: false
  })
}

/**
 * F:updateHeartbeat - Update agent heartbeat
 * @notation P:state F:updateHeartbeat CB:none I:AgentState DB:agent
 */
export const updateHeartbeat = (state: AgentState): AgentState => {
  return Object.freeze({
    ...state,
    lastHeartbeat: Date.now()
  })
}

/**
 * F:logHeartbeat - Log agent heartbeat
 * @notation P:state,config F:logHeartbeat CB:logging I:void DB:agent
 */
export const logHeartbeat = (state: AgentState, config: AgentConfig): void => {
  if (config.enableLogging) {
    const uptime = Math.floor((Date.now() - state.startTime) / 1000)
    const heartbeatAge = Math.floor((Date.now() - state.lastHeartbeat) / 1000)
    console.log(`💓 HEARTBEAT: Agent running for ${uptime}s, last heartbeat ${heartbeatAge}s ago`)
  }
}

/**
 * F:setupShutdownHandlers - Setup graceful shutdown handlers
 * @notation P:state,config,cleanup F:setupShutdownHandlers CB:shutdown I:void DB:agent
 */
export const setupShutdownHandlers = (
  state: AgentState,
  config: AgentConfig,
  cleanup: () => void
): void => {
  const gracefulShutdown = (signal: string) => {
    console.log(`🛑 SHUTDOWN: Received ${signal}, shutting down gracefully...`)
    
    if (state.database) {
      console.log('📦 SHUTDOWN: Closing database connection...')
      state.database.close((err) => {
        if (err) {
          console.error('❌ SHUTDOWN: Database close error:', err.message)
        } else {
          console.log('✅ SHUTDOWN: Database closed successfully')
        }
      })
    }

    cleanup()
    
    setTimeout(() => {
      console.log('⏰ SHUTDOWN: Timeout reached, forcing exit')
      process.exit(1)
    }, config.shutdownTimeout)

    console.log('✅ SHUTDOWN: Graceful shutdown complete')
    process.exit(0)
  }

  process.on('SIGINT', () => gracefulShutdown('SIGINT'))
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'))
}

/**
 * F:executeAgentLoop - Main agent execution loop
 * @notation P:database,config,workspaceRoot F:executeAgentLoop CB:executeAgentLoop I:AgentResult DB:agent
 */
export const executeAgentLoop = async (
  database: Database,
  config: AgentConfig = createDefaultAgentConfig(),
  workspaceRoot: string = process.cwd()
): Promise<AgentResult> => {
  try {
    console.log('🚀 AGENT LOOP: Starting agent execution loop')
    
    // Initialize agent state
    let agentState = createAgentState(database, config)
    agentState = startAgentState(agentState)

    // Load contextual state and directives
    const contextualState = consumeContextualState(workspaceRoot)
    const directives = loadAgentDirectives('.augment-guidelines')

    console.log('📋 AGENT LOOP: Loaded directives and contextual state')
    console.log('🎯 AGENT LOOP: Context keys:', Object.keys(contextualState))

    // Initialize handler registry
    const handlerRegistry = await initializeHandlerRegistry(database)
    console.log('🔧 AGENT LOOP: Handler registry initialized')

    // Setup heartbeat monitoring
    const heartbeatTimer = setInterval(() => {
      agentState = updateHeartbeat(agentState)
      logHeartbeat(agentState, config)
    }, config.heartbeatInterval)

    const cleanup = () => {
      clearInterval(heartbeatTimer)
    }

    process.once('exit', cleanup)

    return Object.freeze({
      success: true,
      state: agentState,
      timestamp: Date.now()
    })
  } catch (error) {
    console.error('❌ Agent loop failed:', error)

    return Object.freeze({
      success: false,
      state: createAgentState(null, config),
      error: (error as Error).message,
      timestamp: Date.now()
    })
  }
}

/**
 * F:getAgentUptime - Get agent uptime in seconds
 * @notation P:state F:getAgentUptime CB:none I:number DB:agent
 */
export const getAgentUptime = (state: AgentState): number => {
  return Math.floor((Date.now() - state.startTime) / 1000)
}

/**
 * F:isAgentHealthy - Check if agent is healthy
 * @notation P:state,maxHeartbeatAge F:isAgentHealthy CB:health I:boolean DB:agent
 */
export const isAgentHealthy = (state: AgentState, maxHeartbeatAge: number = 60000): boolean => {
  if (!state.isRunning) return false
  if (!state.database) return false
  
  const heartbeatAge = Date.now() - state.lastHeartbeat
  return heartbeatAge <= maxHeartbeatAge
}

/**
 * F:getAgentStatus - Get comprehensive agent status
 * @notation P:state F:getAgentStatus CB:status I:object DB:agent
 */
export const getAgentStatus = (state: AgentState) => {
  return Object.freeze({
    isRunning: state.isRunning,
    uptime: getAgentUptime(state),
    isHealthy: isAgentHealthy(state),
    lastHeartbeat: state.lastHeartbeat,
    heartbeatAge: Date.now() - state.lastHeartbeat,
    hasDatabaseConnection: state.database !== null,
    timestamp: Date.now()
  })
}

/**
 * F:executeAgentLoopLegacy - Legacy wrapper for backward compatibility
 * @notation P:db F:executeAgentLoopLegacy CB:legacy I:void DB:agent
 */
export const executeAgentLoopLegacy = async (db: Database): Promise<void> => {
  const result = await executeAgentLoop(db)
  if (!result.success) {
    throw new Error(result.error || 'Agent loop execution failed')
  }
}
