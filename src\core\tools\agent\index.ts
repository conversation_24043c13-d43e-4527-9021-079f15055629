/**
 * Agent Tools - AI-Optimized Agent Module Index
 * @notation P:core/tools/agent/index F:all CB:none I:all DB:agent
 */

export {
  type AgentState,
  type AgentConfig,
  type AgentResult,
  type ContextualState,
  type DirectiveEnforcementResult,
  createDefaultAgentConfig,
  createAgentState,
  startAgentState,
  stopAgentState,
  updateHeartbeat,
  logHeartbeat,
  setupShutdownHandlers,
  executeAgentLoop,
  loadAgentDirectives,
  consumeContextualState,
  enforceDirectiveCompliance,
  getAgentUptime,
  isAgentHealthy,
  getAgentStatus,
  executeAgentLoopLegacy,
  executeHandlerCommand
} from './engine'

export {
  type PromptAnalysis,
  type TemplateParseResult,
  type TemplateValidationR<PERSON>ult,
  type TemplateParseOptions,
  validatePromptAnalysis,
  validateTemplateContent,
  extractGoal,
  extractTraits,
  extractTools,
  parseTemplateContent,
  parsePromptTemplate,
  createEmptyPromptAnalysis,
  mergePromptAnalyses,
  parsePromptTemplateLegacy
} from './templateParser'

export {
  type PlanningOptions,
  type PlanningResult,
  type TaskDependencyGraph,
  createExecutionTask,
  buildDependencyGraph,
  validateDependencies,
  generateExecutionSequence,
  createExecutionPlan,
  optimizeExecutionPlan,
  getTaskConfig,
  estimatePlanDuration,
  ExecutionTask,
  ExecutionPlan
} from './planner'

export {
  type FeedbackResult,
  type PatternAnalysis,
  type PerformanceInsight,
  type FeedbackProcessingOptions,
  detectSuccessPatterns,
  detectErrorPatterns,
  analyzePerformanceMetrics,
  generateContextTransformations,
  generateExecutionRefinements,
  processFeedback,
  analyzeExecutionResults,
  createFeedbackSummary
} from './feedback'

/**
 * F:createAgentWorkflow - Create complete agent workflow
 * @notation P:sessionId,templatePath,database F:createAgentWorkflow CB:createAgentWorkflow I:object DB:agent
 */
export const createAgentWorkflow = async (
  sessionId: string,
  templatePath?: string,
  database?: import('sqlite3').Database
) => {
  const { createDefaultAgentConfig, createAgentState } = await import('./engine')
  const { parsePromptTemplate } = await import('./templateParser')
  const { createExecutionPlan } = await import('./planner')

  const config = createDefaultAgentConfig()
  const agentState = createAgentState(database || null, config)

  let templateAnalysis = null
  if (templatePath) {
    const parseResult = parsePromptTemplate(templatePath)
    if (parseResult.success) {
      templateAnalysis = parseResult.analysis
    }
  }

  return Object.freeze({
    sessionId,
    agentState,
    config,
    templateAnalysis,
    timestamp: Date.now()
  })
}

/**
 * F:executeAgentWorkflow - Execute complete agent workflow
 * @notation P:workflow,tasks F:executeAgentWorkflow CB:executeAgentWorkflow I:object DB:agent
 */
export const executeAgentWorkflow = async (
  workflow: Awaited<ReturnType<typeof createAgentWorkflow>>,
  tasks: readonly import('../../types').ExecutionTask[] = []
) => {
  const { executeAgentLoop } = await import('./engine')
  const { createExecutionPlan } = await import('./planner')
  const { processFeedback } = await import('./feedback')

  const results = []

  if (workflow.agentState.database) {
    const agentResult = await executeAgentLoop(workflow.agentState.database, workflow.config)
    results.push({ type: 'agent', result: agentResult })
  }

  if (tasks.length > 0) {
    const planResult = createExecutionPlan(workflow.sessionId, tasks)
    results.push({ type: 'plan', result: planResult })

    if (planResult.success && planResult.plan) {
      const mockResults: import('../../types').ToolExecutionResult[] = tasks.map(task => ({
        taskId: task.id,
        planId: planResult.plan!.id,
        handler: task.resources.handler,
        action: task.resources.action,
        success: Math.random() > 0.2,
        output: { status: 'completed' },
        executionTime: Math.random() * 3000 + 500,
        timestamp: Date.now()
      }))

      const feedbackResult = processFeedback(mockResults, planResult.plan.id)
      results.push({ type: 'feedback', result: feedbackResult })
    }
  }

  return Object.freeze({
    workflow,
    results: Object.freeze(results),
    timestamp: Date.now()
  })
}

/**
 * F:getAgentModuleInfo - Get agent module information
 * @notation P:none F:getAgentModuleInfo CB:none I:object DB:none
 */
export const getAgentModuleInfo = () => {
  return Object.freeze({
    name: 'Agent Tools',
    version: '1.0.0',
    description: 'AI-optimized agent functionality with pure functions',
    modules: Object.freeze(['engine', 'templateParser', 'executionPlanner', 'feedbackProcessor']),
    features: Object.freeze([
      'Agent lifecycle management',
      'Template parsing and validation',
      'Execution planning and optimization',
      'Feedback processing and insights'
    ]),
    architecture: 'Pure functions with immutable data',
    compatibility: 'Backward compatible with legacy interfaces'
  })
}

/**
 * F:validateAgentModule - Validate agent module integrity
 * @notation P:none F:validateAgentModule CB:none I:object DB:none
 */
export const validateAgentModule = async () => {
  const validations = []

  try {
    const { createDefaultAgentConfig } = await import('./engine')
    const config = createDefaultAgentConfig()
    validations.push({ module: 'engine', valid: !!config, error: null })
  } catch (error) {
    validations.push({ module: 'engine', valid: false, error: (error as Error).message })
  }

  try {
    const { createEmptyPromptAnalysis } = await import('./templateParser')
    const analysis = createEmptyPromptAnalysis()
    validations.push({ module: 'templateParser', valid: !!analysis, error: null })
  } catch (error) {
    validations.push({ module: 'templateParser', valid: false, error: (error as Error).message })
  }

  try {
    const { getTaskConfig } = await import('./planner')
    const { TaskType } = await import('../../types')
    const config = getTaskConfig(TaskType.EXTRACT)
    validations.push({ module: 'executionPlanner', valid: !!config, error: null })
  } catch (error) {
    validations.push({ module: 'executionPlanner', valid: false, error: (error as Error).message })
  }

  try {
    const { analyzeExecutionResults } = await import('./feedback')
    const analysis = analyzeExecutionResults([])
    validations.push({ module: 'feedbackProcessor', valid: !!analysis, error: null })
  } catch (error) {
    validations.push({ module: 'feedbackProcessor', valid: false, error: (error as Error).message })
  }

  const allValid = validations.every(v => v.valid)

  return Object.freeze({
    valid: allValid,
    validations: Object.freeze(validations),
    timestamp: Date.now()
  })
}

/**
 * F:initializeAgentRuntime - Initialize agent runtime with all subsystems
 * @notation P:workspaceRoot,config F:initializeAgentRuntime CB:initializeAgentRuntime I:object DB:agent
 */
export const initializeAgentRuntime = async (
  workspaceRoot: string,
  config: Record<string, unknown> = {}
) => {
  console.log('🤖 AGENT RUNTIME: Initializing all subsystems')

  const moduleValidation = await validateAgentModule()
  if (!moduleValidation.valid) {
    throw new Error(
      `Agent module validation failed: ${moduleValidation.validations
        .filter(v => !v.valid)
        .map(v => v.error)
        .join(', ')}`
    )
  }

  console.log('✅ AGENT RUNTIME: All modules validated')

  const runtimeState = Object.freeze({
    workspaceRoot,
    config: Object.freeze(config),
    initialized: true,
    timestamp: Date.now(),
    modules: moduleValidation.validations.map(v => v.module)
  })

  console.log('✅ AGENT RUNTIME: Runtime state initialized')
  console.log(`📦 AGENT RUNTIME: Modules loaded: ${runtimeState.modules.join(', ')}`)

  return runtimeState
}

/**
 * F:registerRuntimeSymbols - Register runtime symbols for agent system
 * @notation P:workspaceRoot F:registerRuntimeSymbols CB:registerRuntimeSymbols I:object DB:agent
 */
export const registerRuntimeSymbols = async (workspaceRoot: string) => {
  console.log('🔧 AGENT RUNTIME: Registering runtime symbols')

  const symbols = Object.freeze({
    'P:runtime/launch': 'launchServer,initializeAgentSystem',
    'P:runtime/router': 'buildRouter,executeAgentCommand',
    'P:runtime/server': 'processInput,handleError',
    'F:agent-enhanced-execution': 'executeCompleteAgentWorkflow',
    'CB:agent-validation': 'preemptiveValidationPipeline',
    'I:AgentExecutionResult': 'AgentExecutionResult',
    'DB:agent-runtime': 'agent'
  })

  console.log('✅ AGENT RUNTIME: Runtime symbols registered')
  console.log(`🔧 AGENT RUNTIME: Symbol count: ${Object.keys(symbols).length}`)

  return symbols
}
