/**
 * Resilience Monitor - AI-Optimized Pure Functions
 * @notation P:core/antiPatterns/resilienceMonitor F:createResilienceMonitor,recordErrorRecoveryEvent CB:recordErrorRecoveryEvent I:SystemHealthMetrics,ErrorRecoveryEvent DB:resilience
 */

import { CircuitBreakerMetrics } from './circuitBreaker'
import { RetryMetrics } from './retryManager'

export type SystemHealthMetrics = {
  readonly timestamp: number
  readonly uptime: number
  readonly circuitBreakers: Record<string, CircuitBreakerMetrics>
  readonly retryManagers: Record<string, RetryMetrics>
  readonly errorRecoveryEvents: readonly ErrorRecoveryEvent[]
  readonly systemStatus: 'HEALTHY' | 'DEGRADED' | 'CRITICAL'
  readonly availabilityPercent: number
}

export type ErrorRecoveryEvent = {
  readonly timestamp: number
  readonly component: string
  readonly errorType: string
  readonly recoveryMethod: 'RETRY' | 'CIRCUIT_BREAKER' | 'FALLBACK'
  readonly recoveryTime: number
  readonly success: boolean
  readonly details?: string
}

export type ResilienceMonitorInstance = {
  readonly startTime: number
  readonly errorRecoveryEvents: readonly ErrorRecoveryEvent[]
  readonly maxEventHistory: number
}

/**
 * F:createResilienceMonitor - Create new resilience monitor instance
 * @notation P:maxEventHistory F:createResilienceMonitor CB:none I:ResilienceMonitorInstance DB:resilience
 */
export const createResilienceMonitor = (
  maxEventHistory: number = 1000
): ResilienceMonitorInstance => {
  return Object.freeze({
    startTime: Date.now(),
    errorRecoveryEvents: Object.freeze([]),
    maxEventHistory
  })
}

/**
 * F:recordErrorRecoveryEvent - Record error recovery event
 * @notation P:instance,event F:recordErrorRecoveryEvent CB:recordErrorRecoveryEvent I:ResilienceMonitorInstance DB:resilience
 */
export const recordErrorRecoveryEvent = (
  instance: ResilienceMonitorInstance,
  event: Omit<ErrorRecoveryEvent, 'timestamp'>
): ResilienceMonitorInstance => {
  const fullEvent: ErrorRecoveryEvent = Object.freeze({
    timestamp: Date.now(),
    ...event
  })

  const updatedEvents = [...instance.errorRecoveryEvents, fullEvent]

  const trimmedEvents =
    updatedEvents.length > instance.maxEventHistory
      ? updatedEvents.slice(-instance.maxEventHistory)
      : updatedEvents

  return Object.freeze({
    ...instance,
    errorRecoveryEvents: Object.freeze(trimmedEvents)
  })
}

/**
 * F:getRecentErrorRecoveryEvents - Get recent error recovery events within time window
 * @notation P:instance,timeWindowMs F:getRecentErrorRecoveryEvents CB:none I:array DB:none
 */
export const getRecentErrorRecoveryEvents = (
  instance: ResilienceMonitorInstance,
  timeWindowMs: number
): readonly ErrorRecoveryEvent[] => {
  const cutoffTime = Date.now() - timeWindowMs
  return Object.freeze(instance.errorRecoveryEvents.filter(event => event.timestamp >= cutoffTime))
}

/**
 * F:calculateSystemStatus - Calculate system status based on metrics
 * @notation P:circuitBreakers,retryManagers F:calculateSystemStatus CB:none I:string DB:none
 */
export const calculateSystemStatus = (
  circuitBreakers: Record<string, CircuitBreakerMetrics>,
  retryManagers: Record<string, RetryMetrics>
): 'HEALTHY' | 'DEGRADED' | 'CRITICAL' => {
  let openCircuitBreakers = 0
  let totalCircuitBreakers = 0
  let highFailureRateComponents = 0

  for (const [name, metrics] of Object.entries(circuitBreakers)) {
    totalCircuitBreakers++
    if (metrics.state === 'OPEN') {
      openCircuitBreakers++
    }
  }

  for (const [name, metrics] of Object.entries(retryManagers)) {
    if (metrics.totalAttempts > 0) {
      const failureRate = metrics.failedRetries / metrics.totalAttempts
      if (failureRate > 0.1) {
        highFailureRateComponents++
      }
    }
  }

  if (openCircuitBreakers > 0 || highFailureRateComponents > 2) {
    return 'CRITICAL'
  } else if (openCircuitBreakers > 0 || highFailureRateComponents > 0) {
    return 'DEGRADED'
  } else {
    return 'HEALTHY'
  }
}

/**
 * F:calculateAvailability - Calculate system availability percentage
 * @notation P:instance F:calculateAvailability CB:none I:number DB:none
 */
export const calculateAvailability = (instance: ResilienceMonitorInstance): number => {
  const recentEvents = getRecentErrorRecoveryEvents(instance, 3600000) // Last hour
  if (recentEvents.length === 0) {
    return 100.0
  }

  const successfulRecoveries = recentEvents.filter(event => event.success).length
  return (successfulRecoveries / recentEvents.length) * 100
}

/**
 * F:getSystemHealthMetrics - Get comprehensive system health metrics
 * @notation P:instance,circuitBreakers,retryManagers F:getSystemHealthMetrics CB:none I:SystemHealthMetrics DB:resilience
 */
export const getSystemHealthMetrics = (
  instance: ResilienceMonitorInstance,
  circuitBreakers: Record<string, CircuitBreakerMetrics>,
  retryManagers: Record<string, RetryMetrics>
): SystemHealthMetrics => {
  const systemStatus = calculateSystemStatus(circuitBreakers, retryManagers)
  const availabilityPercent = calculateAvailability(instance)

  return Object.freeze({
    timestamp: Date.now(),
    uptime: Date.now() - instance.startTime,
    circuitBreakers: Object.freeze(circuitBreakers),
    retryManagers: Object.freeze(retryManagers),
    errorRecoveryEvents: getRecentErrorRecoveryEvents(instance, 300000), // Last 5 minutes
    systemStatus,
    availabilityPercent
  })
}

/**
 * F:exportMetricsForPrometheus - Export metrics in Prometheus format
 * @notation P:metrics F:exportMetricsForPrometheus CB:none I:string DB:none
 */
export const exportMetricsForPrometheus = (metrics: SystemHealthMetrics): string => {
  let output = ''

  output += '# HELP augster_uptime_seconds System uptime in seconds\n'
  output += '# TYPE augster_uptime_seconds counter\n'
  output += `augster_uptime_seconds ${metrics.uptime / 1000}\n\n`
  output += '# HELP augster_availability_percent System availability percentage\n'
  output += '# TYPE augster_availability_percent gauge\n'
  output += `augster_availability_percent ${metrics.availabilityPercent}\n\n`

  for (const [name, cbMetrics] of Object.entries(metrics.circuitBreakers)) {
    const stateValue = cbMetrics.state === 'CLOSED' ? 0 : cbMetrics.state === 'HALF_OPEN' ? 1 : 2
    output += `augster_circuit_breaker_state{name="${name}"} ${stateValue}\n`
    output += `augster_circuit_breaker_failures{name="${name}"} ${cbMetrics.failureCount}\n`
    output += `augster_circuit_breaker_total_calls{name="${name}"} ${cbMetrics.totalCalls}\n`
  }

  return output
}

/**
 * F:getSummaryReport - Get summary report for logging
 * @notation P:metrics F:getSummaryReport CB:none I:string DB:none
 */
export const getSummaryReport = (metrics: SystemHealthMetrics): string => {
  const recentEvents = metrics.errorRecoveryEvents

  let report = '\n📊 Resilience Summary Report\n'
  report += `${'='.repeat(50)}\n`
  report += `🕐 Uptime: ${Math.floor(metrics.uptime / 1000 / 60)} minutes\n`
  report += `🎯 Status: ${metrics.systemStatus}\n`
  report += `📈 Availability: ${metrics.availabilityPercent.toFixed(2)}%\n`
  report += `🔄 Recovery Events (5m): ${recentEvents.length}\n\n`

  const cbCount = Object.keys(metrics.circuitBreakers).length
  const openCBs = Object.values(metrics.circuitBreakers).filter(cb => cb.state === 'OPEN').length
  report += `🔌 Circuit Breakers: ${cbCount} total, ${openCBs} open\n`

  if (recentEvents.length > 0) {
    const successfulRecoveries = recentEvents.filter(e => e.success).length
    report += `✅ Successful Recoveries: ${successfulRecoveries}/${recentEvents.length}\n`
  }

  return report
}

/**
 * F:performHealthCheck - Perform health check and log results
 * @notation P:metrics F:performHealthCheck CB:none I:void DB:resilience
 */
export const performHealthCheck = (metrics: SystemHealthMetrics): void => {
  console.log(
    `🏥 System Health Check - Status: ${metrics.systemStatus} - Availability: ${metrics.availabilityPercent.toFixed(2)}%`
  )

  if (metrics.systemStatus !== 'HEALTHY') {
    console.warn('⚠️  System health degraded:')

    for (const [name, cbMetrics] of Object.entries(metrics.circuitBreakers)) {
      if (cbMetrics.state === 'OPEN') {
        console.warn(`   - Circuit breaker '${name}' is OPEN (${cbMetrics.failureCount} failures)`)
      }
    }

    for (const [name, retryMetrics] of Object.entries(metrics.retryManagers)) {
      if (retryMetrics.totalAttempts > 0) {
        const failureRate = (retryMetrics.failedRetries / retryMetrics.totalAttempts) * 100
        if (failureRate > 10) {
          console.warn(
            `   - Retry manager '${name}' has high failure rate: ${failureRate.toFixed(1)}%`
          )
        }
      }
    }
  }
}

/**
 * F:logErrorRecoveryEvent - Log error recovery event
 * @notation P:event F:logErrorRecoveryEvent CB:none I:void DB:none
 */
export const logErrorRecoveryEvent = (event: ErrorRecoveryEvent): void => {
  const logLevel = event.success ? 'info' : 'warn'
  console[logLevel](
    `🔄 Error recovery: ${event.component} - ${event.errorType} - ${event.recoveryMethod} - ${event.success ? 'SUCCESS' : 'FAILED'} (${event.recoveryTime}ms)`
  )
}
