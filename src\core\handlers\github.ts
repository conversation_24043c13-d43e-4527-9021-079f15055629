/**
 * GitHub Handler - AI-Optimized Pure Functions
 * Migrated from BaseHandler class to pure function architecture
 *
 * @notation P:core/handlers/github F:github<PERSON><PERSON><PERSON>,executeGitHubCommand CB:executeGitHubCommand I:GitHubCommand,GitHubResult DB:github
 */

import { processMultiHandlerTemplate } from '../tools'
import { GitHubCommand, GitHubResponse, GitHubRateLimit } from '../types'
import {
  GITHUB_API_CONFIG,
  GITHUB_RATE_LIMITS,
  GITHUB_ERROR_CODES,
  GITHUB_ERROR_MESSAGES
} from '../constants'
import {
  HandlerConfig,
  OperationResult,
  createHandlerConfig,
  executeWithResilience
} from './executeCommand'

// === TYPES ===

export type GitHubResult = {
  readonly success: boolean
  readonly data?: unknown
  readonly error?: string
  readonly rateLimit?: GitHubRateLimit
  readonly processingTime?: number
  readonly timestamp?: number
}

// === CONFIGURATION ===

/**
 * F:createGitHubConfig - Create GitHub handler configuration
 * @notation P:none F:createGitHubConfig CB:none I:HandlerConfig DB:none
 */
export const createGitHubConfig = (): HandlerConfig => {
  return createHandlerConfig(
    'github',
    [
      'repo',
      'issues',
      'commits',
      'branches',
      'pulls',
      'files',
      'search',
      'create-issue',
      'template'
    ],
    {
      failureThreshold: 5,
      recoveryTimeout: 60000,
      maxRetries: 2,
      baseDelay: 2000
    }
  )
}

// === VALIDATION ===

/**
 * F:validateGitHubCommand - Validate GitHub command structure
 * @notation P:command F:validateGitHubCommand CB:none I:boolean DB:none
 */
export const validateGitHubCommand = (command: unknown): command is GitHubCommand => {
  if (!command || typeof command !== 'object') return false

  const cmd = command as Record<string, unknown>

  return (
    typeof cmd.action === 'string' &&
    [
      'repo',
      'issues',
      'commits',
      'branches',
      'pulls',
      'files',
      'search',
      'create-issue',
      'template'
    ].includes(cmd.action)
  )
}

// === CONSTANTS ===

const ALLOWED_REPOSITORIES = process.env.GITHUB_ALLOWED_REPOS?.split(',') || []
const WORKSPACE_SECURITY_ENABLED = process.env.GITHUB_WORKSPACE_SECURITY === 'true'
let lastRateLimit: GitHubRateLimit | null = null

const GitHubUtils = Object.freeze({
  async resolveRepository(
    owner?: string,
    repo?: string
  ): Promise<Readonly<{ owner: string; repo: string }>> {
    const repoInfo = await getRepositoryInfo()
    const targetOwner = owner || repoInfo?.owner
    const targetRepo = repo || repoInfo?.repo
    if (!targetOwner || !targetRepo) throw new Error(GITHUB_ERROR_MESSAGES.REPO_REQUIRED)
    return Object.freeze({ owner: targetOwner, repo: targetRepo })
  },

  buildParams(options: any = {}): URLSearchParams {
    const params = new URLSearchParams()
    if (options.per_page)
      params.append(
        'per_page',
        Math.min(options.per_page, GITHUB_API_CONFIG.MAX_PER_PAGE).toString()
      )
    if (options.page) params.append('page', options.page.toString())
    if (options.state) params.append('state', options.state)
    if (options.since) params.append('since', options.since)
    if (options.until) params.append('until', options.until)
    if (options.sort) params.append('sort', options.sort)
    if (options.direction) params.append('direction', options.direction)
    if (options.base) params.append('base', options.base)
    if (options.head) params.append('head', options.head)
    return params
  },

  async executeWithErrorHandling<T>(
    operation: () => Promise<T>,
    operationName: string
  ): Promise<T> {
    try {
      return await operation()
    } catch (error) {
      throw new Error(`GitHub ${operationName} operation failed: ${(error as Error).message}`)
    }
  },

  buildResponse(data: any, rateLimit?: GitHubRateLimit): GitHubResponse {
    return Object.freeze({ success: true, data, rateLimit })
  },

  async executeRepoOperation(
    endpoint: string,
    owner?: string,
    repo?: string,
    options: any = {},
    processor?: (data: any) => any
  ): Promise<GitHubResponse> {
    return this.executeWithErrorHandling(
      async () => {
        const { owner: targetOwner, repo: targetRepo } = await this.resolveRepository(owner, repo)
        const params = this.buildParams(options)
        const queryString = params.toString()
        const fullEndpoint =
          endpoint.replace('{owner}', targetOwner).replace('{repo}', targetRepo) +
          (queryString ? `?${queryString}` : '')
        const result = await makeGitHubRequest(fullEndpoint)
        const processedData = processor ? processor(result.data) : result.data
        return this.buildResponse(Object.freeze(processedData), result.rateLimit)
      },
      endpoint.split('/').pop() || 'operation'
    )
  }
})

function getGitHubToken(): string | null {
  const token = process.env.GITHUB_TOKEN || process.env.GH_TOKEN || null
  if (!token) throw new Error(GITHUB_ERROR_MESSAGES.TOKEN_NOT_FOUND)
  return token
}

async function validateGitHubToken(
  token: string
): Promise<Readonly<{ valid: boolean; scopes?: ReadonlyArray<string>; error?: string }>> {
  try {
    const response = await fetch(`${GITHUB_API_CONFIG.BASE_URL}/user`, {
      headers: {
        Authorization: `token ${token}`,
        Accept: 'application/vnd.github.v3+json',
        'User-Agent': GITHUB_API_CONFIG.USER_AGENT
      }
    })
    if (!response.ok)
      return Object.freeze({ valid: false, error: `Token validation failed: ${response.status}` })
    const scopes = response.headers.get('X-OAuth-Scopes')?.split(', ') || []
    return Object.freeze({ valid: true, scopes: Object.freeze(scopes) })
  } catch (error) {
    return Object.freeze({
      valid: false,
      error: `Token validation error: ${(error as Error).message}`
    })
  }
}

function checkRateLimit(): Readonly<{ allowed: boolean; waitTime?: number; message?: string }> {
  if (!lastRateLimit) return Object.freeze({ allowed: true })
  const now = Date.now() / 1000
  const resetTime = lastRateLimit.reset
  if (now >= resetTime) return Object.freeze({ allowed: true })
  if (lastRateLimit.remaining >= GITHUB_RATE_LIMITS.THRESHOLD)
    return Object.freeze({ allowed: true })
  const waitTime = (resetTime - now) * 1000 + GITHUB_RATE_LIMITS.RESET_BUFFER
  return Object.freeze({
    allowed: false,
    waitTime,
    message: `Rate limit exceeded. ${lastRateLimit.remaining} requests remaining. Reset in ${Math.ceil(waitTime / 1000)}s`
  })
}

function validateRepositoryAccess(
  owner: string,
  repo: string
): Readonly<{ allowed: boolean; error?: string }> {
  if (!WORKSPACE_SECURITY_ENABLED) return Object.freeze({ allowed: true })
  const fullName = `${owner}/${repo}`
  if (ALLOWED_REPOSITORIES.length > 0 && !ALLOWED_REPOSITORIES.includes(fullName)) {
    return Object.freeze({
      allowed: false,
      error: `Repository ${fullName} not in allowed list. Configure GITHUB_ALLOWED_REPOS environment variable.`
    })
  }
  return Object.freeze({ allowed: true })
}

async function getRepositoryInfo(): Promise<Readonly<{ owner: string; repo: string }> | null> {
  try {
    const envOwner = process.env.GITHUB_OWNER
    const envRepo = process.env.GITHUB_REPO
    if (envOwner && envRepo) return Object.freeze({ owner: envOwner, repo: envRepo })
    return Object.freeze({ owner: 'cryptokairo', repo: 'augster_mvp_scaffold' })
  } catch {
    return null
  }
}

async function makeGitHubRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
  const rateLimitCheck = checkRateLimit()
  if (!rateLimitCheck.allowed) throw new Error(rateLimitCheck.message || 'Rate limit exceeded')

  const token = getGitHubToken()
  const headers: Record<string, string> = {
    Accept: 'application/vnd.github.v3+json',
    'User-Agent': GITHUB_API_CONFIG.USER_AGENT,
    ...((options.headers as Record<string, string>) || {})
  }
  if (token) headers['Authorization'] = `token ${token}`

  const url = endpoint.startsWith('http') ? endpoint : `${GITHUB_API_CONFIG.BASE_URL}${endpoint}`
  const response = await fetch(url, { ...options, headers })

  const rateLimit = Object.freeze({
    remaining: parseInt(response.headers.get(GITHUB_RATE_LIMITS.HEADERS.REMAINING) || '0'),
    reset: parseInt(response.headers.get(GITHUB_RATE_LIMITS.HEADERS.RESET) || '0'),
    limit: parseInt(response.headers.get(GITHUB_RATE_LIMITS.HEADERS.LIMIT) || '0')
  })
  lastRateLimit = rateLimit

  if (!response.ok) {
    const errorBody = await response.text()
    let errorMessage = `GitHub API error ${response.status}: ${errorBody}`
    if (response.status === GITHUB_ERROR_CODES.UNAUTHORIZED) {
      errorMessage = GITHUB_ERROR_MESSAGES.AUTH_FAILED
    } else if (response.status === GITHUB_ERROR_CODES.FORBIDDEN) {
      errorMessage =
        rateLimit.remaining === 0
          ? `GitHub rate limit exceeded. Reset at ${new Date(rateLimit.reset * 1000).toISOString()}`
          : GITHUB_ERROR_MESSAGES.ACCESS_FORBIDDEN
    } else if (response.status === GITHUB_ERROR_CODES.NOT_FOUND) {
      errorMessage = GITHUB_ERROR_MESSAGES.RESOURCE_NOT_FOUND
    }
    throw new Error(errorMessage)
  }

  const data = await response.json()
  return { data, rateLimit }
}

async function executeGitHubRepo(owner?: string, repo?: string): Promise<GitHubResponse> {
  return GitHubUtils.executeRepoOperation('/repos/{owner}/{repo}', owner, repo, {}, data => ({
    name: data.name,
    full_name: data.full_name,
    description: data.description,
    private: data.private,
    html_url: data.html_url,
    clone_url: data.clone_url,
    ssh_url: data.ssh_url,
    default_branch: data.default_branch,
    language: data.language,
    languages_url: data.languages_url,
    stargazers_count: data.stargazers_count,
    watchers_count: data.watchers_count,
    forks_count: data.forks_count,
    open_issues_count: data.open_issues_count,
    created_at: data.created_at,
    updated_at: data.updated_at,
    pushed_at: data.pushed_at
  }))
}

async function executeGitHubIssues(
  owner?: string,
  repo?: string,
  options: any = {}
): Promise<GitHubResponse> {
  return GitHubUtils.executeRepoOperation(
    '/repos/{owner}/{repo}/issues',
    owner,
    repo,
    options,
    data =>
      data.map((issue: any) =>
        Object.freeze({
          number: issue.number,
          title: issue.title,
          body: issue.body,
          state: issue.state,
          user: Object.freeze({
            login: issue.user.login,
            avatar_url: issue.user.avatar_url
          }),
          labels: Object.freeze(
            issue.labels.map((label: any) =>
              Object.freeze({
                name: label.name,
                color: label.color
              })
            )
          ),
          assignees: Object.freeze(issue.assignees.map((assignee: any) => assignee.login)),
          milestone: issue.milestone
            ? Object.freeze({
                title: issue.milestone.title,
                state: issue.milestone.state
              })
            : null,
          created_at: issue.created_at,
          updated_at: issue.updated_at,
          closed_at: issue.closed_at,
          html_url: issue.html_url
        })
      )
  )
}

async function executeGitHubCommits(
  owner?: string,
  repo?: string,
  options: any = {}
): Promise<GitHubResponse> {
  return GitHubUtils.executeRepoOperation(
    '/repos/{owner}/{repo}/commits',
    owner,
    repo,
    options,
    data =>
      data.map((commit: any) =>
        Object.freeze({
          sha: commit.sha,
          message: commit.commit.message,
          author: Object.freeze({
            name: commit.commit.author.name,
            email: commit.commit.author.email,
            date: commit.commit.author.date
          }),
          committer: Object.freeze({
            name: commit.commit.committer.name,
            email: commit.commit.committer.email,
            date: commit.commit.committer.date
          }),
          html_url: commit.html_url,
          stats: commit.stats
            ? Object.freeze({
                additions: commit.stats.additions,
                deletions: commit.stats.deletions,
                total: commit.stats.total
              })
            : null
        })
      )
  )
}

async function executeGitHubBranches(
  owner?: string,
  repo?: string,
  options: any = {}
): Promise<GitHubResponse> {
  return GitHubUtils.executeRepoOperation(
    '/repos/{owner}/{repo}/branches',
    owner,
    repo,
    options,
    data =>
      data.map((branch: any) =>
        Object.freeze({
          name: branch.name,
          commit: Object.freeze({
            sha: branch.commit.sha,
            url: branch.commit.url
          }),
          protected: branch.protected
        })
      )
  )
}

async function executeGitHubPulls(
  owner?: string,
  repo?: string,
  options: any = {}
): Promise<GitHubResponse> {
  return GitHubUtils.executeWithErrorHandling(async () => {
    const { owner: targetOwner, repo: targetRepo } = await GitHubUtils.resolveRepository(
      owner,
      repo
    )
    const params = GitHubUtils.buildParams(options)

    const queryString = params.toString()
    const endpoint = `/repos/${targetOwner}/${targetRepo}/pulls${queryString ? `?${queryString}` : ''}`
    const result = await makeGitHubRequest(endpoint)

    const pullsData = Object.freeze(
      result.data.map((pull: any) =>
        Object.freeze({
          number: pull.number,
          title: pull.title,
          body: pull.body,
          state: pull.state,
          draft: pull.draft,
          user: Object.freeze({
            login: pull.user.login,
            avatar_url: pull.user.avatar_url
          }),
          head: Object.freeze({
            ref: pull.head.ref,
            sha: pull.head.sha,
            repo: pull.head.repo
              ? Object.freeze({
                  name: pull.head.repo.name,
                  full_name: pull.head.repo.full_name
                })
              : null
          }),
          base: Object.freeze({
            ref: pull.base.ref,
            sha: pull.base.sha,
            repo: Object.freeze({
              name: pull.base.repo.name,
              full_name: pull.base.repo.full_name
            })
          }),
          mergeable: pull.mergeable,
          mergeable_state: pull.mergeable_state,
          merged: pull.merged,
          merged_at: pull.merged_at,
          merge_commit_sha: pull.merge_commit_sha,
          assignees: Object.freeze(pull.assignees.map((assignee: any) => assignee.login)),
          requested_reviewers: Object.freeze(
            pull.requested_reviewers.map((reviewer: any) => reviewer.login)
          ),
          labels: Object.freeze(
            pull.labels.map((label: any) =>
              Object.freeze({
                name: label.name,
                color: label.color
              })
            )
          ),
          milestone: pull.milestone
            ? Object.freeze({
                title: pull.milestone.title,
                state: pull.milestone.state
              })
            : null,
          created_at: pull.created_at,
          updated_at: pull.updated_at,
          closed_at: pull.closed_at,
          html_url: pull.html_url,
          diff_url: pull.diff_url,
          patch_url: pull.patch_url
        })
      )
    )

    return GitHubUtils.buildResponse(pullsData, result.rateLimit)
  }, 'pulls')
}

async function executeGitHubFiles(
  owner?: string,
  repo?: string,
  options: any = {}
): Promise<GitHubResponse> {
  return GitHubUtils.executeWithErrorHandling(async () => {
    const { owner: targetOwner, repo: targetRepo } = await GitHubUtils.resolveRepository(
      owner,
      repo
    )
    const path = options.path || ''
    const ref = options.ref || 'main'

    const endpoint = `/repos/${targetOwner}/${targetRepo}/contents/${path}?ref=${ref}`
    const result = await makeGitHubRequest(endpoint)

    const processFileData = (item: any) =>
      Object.freeze({
        name: item.name,
        path: item.path,
        sha: item.sha,
        size: item.size,
        type: item.type,
        download_url: item.download_url,
        html_url: item.html_url,
        content: item.content ? Buffer.from(item.content, 'base64').toString('utf-8') : undefined,
        encoding: item.encoding
      })

    const filesData = Array.isArray(result.data)
      ? Object.freeze(result.data.map(processFileData))
      : processFileData(result.data)

    return GitHubUtils.buildResponse(filesData, result.rateLimit)
  }, 'files')
}

async function executeGitHubSearch(
  owner?: string,
  repo?: string,
  options: any = {}
): Promise<GitHubResponse> {
  return GitHubUtils.executeWithErrorHandling(async () => {
    if (!options.q) {
      throw new Error('Search query (q) is required')
    }

    const searchType = options.type || 'repositories'
    const params = GitHubUtils.buildParams(options)

    let query = options.q
    if (owner && repo) {
      query += ` repo:${owner}/${repo}`
    }

    params.append('q', query)
    if (options.direction) params.append('order', options.direction)

    const queryString = params.toString()
    const endpoint = `/search/${searchType}?${queryString}`
    const result = await makeGitHubRequest(endpoint)

    const processSearchItem = (item: any) => {
      switch (searchType) {
        case 'repositories':
          return Object.freeze({
            name: item.name,
            full_name: item.full_name,
            description: item.description,
            html_url: item.html_url,
            stargazers_count: item.stargazers_count,
            language: item.language,
            updated_at: item.updated_at
          })
        case 'code':
          return Object.freeze({
            name: item.name,
            path: item.path,
            sha: item.sha,
            html_url: item.html_url,
            repository: Object.freeze({
              name: item.repository.name,
              full_name: item.repository.full_name
            })
          })
        case 'commits':
          return Object.freeze({
            sha: item.sha,
            commit: Object.freeze({
              message: item.commit.message,
              author: item.commit.author
            }),
            html_url: item.html_url,
            repository: Object.freeze({
              name: item.repository.name,
              full_name: item.repository.full_name
            })
          })
        case 'issues':
          return Object.freeze({
            number: item.number,
            title: item.title,
            state: item.state,
            html_url: item.html_url,
            user: Object.freeze({
              login: item.user.login
            }),
            created_at: item.created_at,
            updated_at: item.updated_at
          })
        default:
          return item
      }
    }

    const searchData = Object.freeze({
      total_count: result.data.total_count,
      incomplete_results: result.data.incomplete_results,
      items: Object.freeze(result.data.items.map(processSearchItem))
    })

    return GitHubUtils.buildResponse(searchData, result.rateLimit)
  }, 'search')
}

async function executeCreatePull(
  owner?: string,
  repo?: string,
  options: any = {}
): Promise<GitHubResponse> {
  return GitHubUtils.executeWithErrorHandling(async () => {
    const { owner: targetOwner, repo: targetRepo } = await GitHubUtils.resolveRepository(
      owner,
      repo
    )

    const accessCheck = validateRepositoryAccess(targetOwner, targetRepo)
    if (!accessCheck.allowed) {
      throw new Error(accessCheck.error)
    }

    if (!options.title || !options.head || !options.base) {
      throw new Error('Pull request title, head branch, and base branch are required')
    }

    const pullData = Object.freeze({
      title: options.title,
      body: options.body || '',
      head: options.head,
      base: options.base,
      draft: options.draft || false,
      maintainer_can_modify: options.maintainer_can_modify !== false
    })

    const result = await makeGitHubRequest(`/repos/${targetOwner}/${targetRepo}/pulls`, {
      method: 'POST',
      body: JSON.stringify(pullData)
    })

    const pullResponseData = Object.freeze({
      number: result.data.number,
      title: result.data.title,
      body: result.data.body,
      state: result.data.state,
      draft: result.data.draft,
      html_url: result.data.html_url,
      head: Object.freeze({
        ref: result.data.head.ref,
        sha: result.data.head.sha
      }),
      base: Object.freeze({
        ref: result.data.base.ref,
        sha: result.data.base.sha
      }),
      created_at: result.data.created_at
    })

    return GitHubUtils.buildResponse(pullResponseData, result.rateLimit)
  }, 'create-pull')
}

async function executeUpdatePull(
  owner?: string,
  repo?: string,
  options: any = {}
): Promise<GitHubResponse> {
  return GitHubUtils.executeWithErrorHandling(async () => {
    const { owner: targetOwner, repo: targetRepo } = await GitHubUtils.resolveRepository(
      owner,
      repo
    )

    const accessCheck = validateRepositoryAccess(targetOwner, targetRepo)
    if (!accessCheck.allowed) {
      throw new Error(accessCheck.error)
    }

    if (!options.pull_number) {
      throw new Error('Pull request number is required')
    }

    const updateData: any = {}
    if (options.title) updateData.title = options.title
    if (options.body !== undefined) updateData.body = options.body
    if (options.state) updateData.state = options.state
    if (options.base) updateData.base = options.base

    const result = await makeGitHubRequest(
      `/repos/${targetOwner}/${targetRepo}/pulls/${options.pull_number}`,
      {
        method: 'PATCH',
        body: JSON.stringify(updateData)
      }
    )

    const updateResponseData = Object.freeze({
      number: result.data.number,
      title: result.data.title,
      body: result.data.body,
      state: result.data.state,
      updated_at: result.data.updated_at,
      html_url: result.data.html_url
    })

    return GitHubUtils.buildResponse(updateResponseData, result.rateLimit)
  }, 'update-pull')
}

async function executeMergePull(
  owner?: string,
  repo?: string,
  options: any = {}
): Promise<GitHubResponse> {
  return GitHubUtils.executeWithErrorHandling(async () => {
    const { owner: targetOwner, repo: targetRepo } = await GitHubUtils.resolveRepository(
      owner,
      repo
    )
    const accessCheck = validateRepositoryAccess(targetOwner, targetRepo)
    if (!accessCheck.allowed) throw new Error(accessCheck.error)
    if (!options.pull_number) throw new Error('Pull request number is required')

    const mergeData = Object.freeze({
      commit_title: options.title || `Merge pull request #${options.pull_number}`,
      commit_message: options.body || '',
      merge_method: 'merge'
    })

    const result = await makeGitHubRequest(
      `/repos/${targetOwner}/${targetRepo}/pulls/${options.pull_number}/merge`,
      {
        method: 'PUT',
        body: JSON.stringify(mergeData)
      }
    )

    return GitHubUtils.buildResponse(
      Object.freeze({
        sha: result.data.sha,
        merged: result.data.merged,
        message: result.data.message
      }),
      result.rateLimit
    )
  }, 'merge-pull')
}

async function executeReviewPull(
  owner?: string,
  repo?: string,
  options: GitHubCommand['options'] = {}
): Promise<GitHubResponse> {
  try {
    const repoInfo = await getRepositoryInfo()
    const targetOwner = owner || repoInfo?.owner
    const targetRepo = repo || repoInfo?.repo

    if (!targetOwner || !targetRepo) {
      throw new Error('Repository owner and name are required')
    }

    const accessCheck = validateRepositoryAccess(targetOwner, targetRepo)
    if (!accessCheck.allowed) {
      throw new Error(accessCheck.error)
    }

    if (!options.pull_number || !options.event) {
      throw new Error('Pull request number and review event are required')
    }

    const reviewData = {
      body: options.review_body || ''
    }

    const result = await makeGitHubRequest(
      `/repos/${targetOwner}/${targetRepo}/pulls/${options.pull_number}/reviews`,
      {
        method: 'POST',
        body: JSON.stringify(reviewData)
      }
    )

    return {
      success: true,
      data: {
        id: result.data.id,
        body: result.data.body,
        state: result.data.state,
        html_url: result.data.html_url,
        submitted_at: result.data.submitted_at
      },
      rateLimit: result.rateLimit
    }
  } catch (error) {
    throw new Error(`GitHub review pull request failed: ${(error as Error).message}`)
  }
}

async function executeAnalyzePR(
  owner?: string,
  repo?: string,
  options: GitHubCommand['options'] = {}
): Promise<GitHubResponse> {
  try {
    const repoInfo = await getRepositoryInfo()
    const targetOwner = owner || repoInfo?.owner
    const targetRepo = repo || repoInfo?.repo

    if (!targetOwner || !targetRepo) {
      throw new Error('Repository owner and name are required')
    }

    const accessCheck = validateRepositoryAccess(targetOwner, targetRepo)
    if (!accessCheck.allowed) {
      throw new Error(accessCheck.error)
    }

    if (!options.pull_number) {
      throw new Error('Pull request number is required')
    }

    const prResult = await makeGitHubRequest(
      `/repos/${targetOwner}/${targetRepo}/pulls/${options.pull_number}`
    )

    const filesResult = await makeGitHubRequest(
      `/repos/${targetOwner}/${targetRepo}/pulls/${options.pull_number}/files`
    )

    const reviewsResult = await makeGitHubRequest(
      `/repos/${targetOwner}/${targetRepo}/pulls/${options.pull_number}/reviews`
    )

    const analysis = {
      pr_info: {
        number: prResult.data.number,
        title: prResult.data.title,
        state: prResult.data.state,
        mergeable: prResult.data.mergeable,
        mergeable_state: prResult.data.mergeable_state
      },
      files_changed: filesResult.data.length,
      total_additions: filesResult.data.reduce((sum: number, file: any) => sum + file.additions, 0),
      total_deletions: filesResult.data.reduce((sum: number, file: any) => sum + file.deletions, 0),
      reviews: {
        total: reviewsResult.data.length,
        approved: reviewsResult.data.filter((r: any) => r.state === 'APPROVED').length,
        changes_requested: reviewsResult.data.filter((r: any) => r.state === 'CHANGES_REQUESTED')
          .length
      },
      file_types: filesResult.data.reduce((types: Record<string, number>, file: any) => {
        const ext = file.filename.split('.').pop() || 'no-extension'
        types[ext] = (types[ext] || 0) + 1
        return types
      }, {}),
      complexity_score: Math.min(
        100,
        Math.max(
          0,
          filesResult.data.length * 2 +
            filesResult.data.reduce((sum: number, file: any) => sum + file.changes, 0) / 10
        )
      )
    }

    return {
      success: true,
      data: analysis,
      rateLimit: prResult.rateLimit
    }
  } catch (error) {
    throw new Error(`GitHub PR analysis failed: ${(error as Error).message}`)
  }
}

async function executeValidateToken(): Promise<GitHubResponse> {
  return GitHubUtils.executeWithErrorHandling(async () => {
    const token = getGitHubToken()
    if (!token) throw new Error('GitHub token not found')
    const validation = await validateGitHubToken(token)
    if (!validation.valid) throw new Error(validation.error || 'Token validation failed')
    return GitHubUtils.buildResponse(
      Object.freeze({
        valid: validation.valid,
        scopes: validation.scopes,
        rate_limit: lastRateLimit
      })
    )
  }, 'validate-token')
}

async function executeProtectBranch(
  owner?: string,
  repo?: string,
  options: any = {}
): Promise<GitHubResponse> {
  return GitHubUtils.executeWithErrorHandling(async () => {
    const { owner: targetOwner, repo: targetRepo } = await GitHubUtils.resolveRepository(
      owner,
      repo
    )
    const accessCheck = validateRepositoryAccess(targetOwner, targetRepo)
    if (!accessCheck.allowed) throw new Error(accessCheck.error)
    if (!options.branch) throw new Error('Branch name is required')

    const protectionData = Object.freeze({
      required_status_checks: options.required_status_checks || null,
      enforce_admins: options.enforce_admins || false,
      required_pull_request_reviews: options.required_pull_request_reviews || {
        required_approving_review_count: 1,
        dismiss_stale_reviews: true,
        require_code_owner_reviews: false
      },
      restrictions: options.restrictions || null
    })

    const result = await makeGitHubRequest(
      `/repos/${targetOwner}/${targetRepo}/branches/${options.branch}/protection`,
      {
        method: 'PUT',
        body: JSON.stringify(protectionData)
      }
    )

    return GitHubUtils.buildResponse(
      Object.freeze({
        branch: options.branch,
        protection: result.data
      }),
      result.rateLimit
    )
  }, 'protect-branch')
}

async function executeAdvancedSearch(
  owner?: string,
  repo?: string,
  options: any = {}
): Promise<GitHubResponse> {
  return GitHubUtils.executeWithErrorHandling(async () => {
    if (!options.q) throw new Error('Search query (q) is required')
    const searchType = options.type || 'repositories'
    let query = options.q
    if (options.filters) {
      Object.entries(options.filters).forEach(([key, value]) => {
        query += ` ${key}:${value}`
      })
    }
    if (owner && repo) query += ` repo:${owner}/${repo}`

    const params = GitHubUtils.buildParams(options)
    params.append('q', query)
    if (options.sort_by) params.append('sort', options.sort_by)
    if (options.order) params.append('order', options.order)

    const result = await makeGitHubRequest(`/search/${searchType}?${params.toString()}`)
    return GitHubUtils.buildResponse(
      Object.freeze({
        query,
        search_type: searchType,
        total_count: result.data.total_count,
        incomplete_results: result.data.incomplete_results,
        items: result.data.items
      }),
      result.rateLimit
    )
  }, 'advanced-search')
}

async function executeCreateIssue(
  owner?: string,
  repo?: string,
  options: any = {}
): Promise<GitHubResponse> {
  return GitHubUtils.executeWithErrorHandling(async () => {
    const { owner: targetOwner, repo: targetRepo } = await GitHubUtils.resolveRepository(
      owner,
      repo
    )

    if (!options.title) {
      throw new Error('Issue title is required')
    }

    const issueData: any = {
      title: options.title,
      body: options.body || ''
    }

    if (options.labels?.length > 0) issueData.labels = options.labels
    if (options.assignees?.length > 0) issueData.assignees = options.assignees
    if (options.milestone) issueData.milestone = options.milestone

    const result = await makeGitHubRequest(`/repos/${targetOwner}/${targetRepo}/issues`, {
      method: 'POST',
      body: JSON.stringify(issueData),
      headers: { 'Content-Type': 'application/json' }
    })

    const issueResponseData = Object.freeze({
      number: result.data.number,
      title: result.data.title,
      body: result.data.body,
      state: result.data.state,
      html_url: result.data.html_url,
      created_at: result.data.created_at,
      updated_at: result.data.updated_at,
      user: Object.freeze({
        login: result.data.user.login,
        avatar_url: result.data.user.avatar_url
      }),
      labels: Object.freeze(
        result.data.labels.map((label: any) =>
          Object.freeze({
            name: label.name,
            color: label.color
          })
        )
      ),
      assignees: Object.freeze(result.data.assignees.map((assignee: any) => assignee.login))
    })

    return GitHubUtils.buildResponse(issueResponseData, result.rateLimit)
  }, 'create-issue')
}

async function executeUpdateIssue(
  owner?: string,
  repo?: string,
  options: any = {}
): Promise<GitHubResponse> {
  return GitHubUtils.executeWithErrorHandling(async () => {
    const { owner: targetOwner, repo: targetRepo } = await GitHubUtils.resolveRepository(
      owner,
      repo
    )

    if (!options.issue_number) {
      throw new Error('Issue number is required')
    }

    const updateData: any = {}
    if (options.title) updateData.title = options.title
    if (options.body !== undefined) updateData.body = options.body
    if (options.state) updateData.state = options.state
    if (options.state_reason) updateData.state_reason = options.state_reason
    if (options.labels) updateData.labels = options.labels
    if (options.assignees) updateData.assignees = options.assignees
    if (options.milestone !== undefined) updateData.milestone = options.milestone

    const result = await makeGitHubRequest(
      `/repos/${targetOwner}/${targetRepo}/issues/${options.issue_number}`,
      {
        method: 'PATCH',
        body: JSON.stringify(updateData),
        headers: { 'Content-Type': 'application/json' }
      }
    )

    const updateResponseData = Object.freeze({
      number: result.data.number,
      title: result.data.title,
      body: result.data.body,
      state: result.data.state,
      html_url: result.data.html_url,
      updated_at: result.data.updated_at,
      closed_at: result.data.closed_at
    })

    return GitHubUtils.buildResponse(updateResponseData, result.rateLimit)
  }, 'update-issue')
}

async function executeCloseIssue(
  owner?: string,
  repo?: string,
  options: any = {}
): Promise<GitHubResponse> {
  return GitHubUtils.executeWithErrorHandling(async () => {
    const { owner: targetOwner, repo: targetRepo } = await GitHubUtils.resolveRepository(
      owner,
      repo
    )

    if (!options.issue_number) {
      throw new Error('Issue number is required')
    }

    const closeData = Object.freeze({
      state: 'closed',
      state_reason: options.state_reason || 'completed'
    })

    const result = await makeGitHubRequest(
      `/repos/${targetOwner}/${targetRepo}/issues/${options.issue_number}`,
      {
        method: 'PATCH',
        body: JSON.stringify(closeData),
        headers: { 'Content-Type': 'application/json' }
      }
    )

    const closeResponseData = Object.freeze({
      number: result.data.number,
      title: result.data.title,
      state: result.data.state,
      state_reason: result.data.state_reason,
      html_url: result.data.html_url,
      closed_at: result.data.closed_at
    })

    return GitHubUtils.buildResponse(closeResponseData, result.rateLimit)
  }, 'close-issue')
}

async function executeStageFiles(
  owner?: string,
  repo?: string,
  options: any = {}
): Promise<GitHubResponse> {
  return GitHubUtils.executeWithErrorHandling(async () => {
    const { owner: targetOwner, repo: targetRepo } = await GitHubUtils.resolveRepository(
      owner,
      repo
    )
    const accessCheck = validateRepositoryAccess(targetOwner, targetRepo)
    if (!accessCheck.allowed) throw new Error(accessCheck.error)

    const filePatterns =
      options.files ||
      (options.pattern
        ? [options.pattern]
        : options.templateVars?.pattern
          ? [options.templateVars.pattern]
          : null)
    if (!filePatterns)
      throw new Error(
        'File patterns required (options.files, options.pattern, or templateVars.pattern)'
      )

    const templateVars = options.templateVars || {}
    const processedPatterns = filePatterns.map((pattern: string) =>
      pattern.includes('P:')
        ? pattern.replace(/P:([^\s,\]]+)/g, (_match, path) => {
            if (!templateVars._aiNotationParams) templateVars._aiNotationParams = {}
            if (!templateVars._aiNotationParams.path) templateVars._aiNotationParams.path = []
            templateVars._aiNotationParams.path.push(path)
            return path
          })
        : pattern
    )

    const stagedFiles: any[] = []
    const errors: string[] = []

    for (const pattern of processedPatterns) {
      try {
        const filesResult = await makeGitHubRequest(
          `/repos/${targetOwner}/${targetRepo}/contents/${pattern.replace(/\*/g, '')}`
        )
        if (filesResult.data) {
          Array.isArray(filesResult.data)
            ? stagedFiles.push(...filesResult.data)
            : stagedFiles.push(filesResult.data)
        }
      } catch (error) {
        errors.push(`Failed to stage pattern ${pattern}: ${(error as Error).message}`)
      }
    }

    return GitHubUtils.buildResponse(
      Object.freeze({
        stagedFiles: stagedFiles.length,
        patterns: processedPatterns,
        files: Object.freeze(
          stagedFiles.map(f =>
            Object.freeze({
              name: f.name,
              path: f.path,
              type: f.type,
              sha: f.sha
            })
          )
        ),
        errors: errors.length > 0 ? Object.freeze(errors) : undefined,
        aiNotationMetadata: templateVars._aiNotationParams
          ? Object.freeze({
              parametersDetected: templateVars._aiNotationParams,
              toolsDetected: {},
              workflowCapable: true,
              validationRequired: true
            })
          : undefined,
        timestamp: Date.now(),
        processingTime: 0
      })
    )
  }, 'stage-files')
}

async function executeCreateCommit(
  owner?: string,
  repo?: string,
  options: any = {}
): Promise<GitHubResponse> {
  return GitHubUtils.executeWithErrorHandling(async () => {
    const { owner: targetOwner, repo: targetRepo } = await GitHubUtils.resolveRepository(
      owner,
      repo
    )
    const accessCheck = validateRepositoryAccess(targetOwner, targetRepo)
    if (!accessCheck.allowed) throw new Error(accessCheck.error)

    let commitMessage = options.message || options.options?.message
    if (!commitMessage)
      throw new Error(`Commit message required. Received: ${JSON.stringify(options)}`)

    const templateVars = options.templateVars || options.options?.templateVars || {}

    // @removed:processAINotation - Phase 11A.P.6 - function not available in tools module

    if (templateVars.timestamp)
      commitMessage = commitMessage.replace(/\{\{timestamp\}\}/g, templateVars.timestamp)
    if (templateVars.operation)
      commitMessage = commitMessage.replace(/\{\{operation\}\}/g, templateVars.operation)

    const filesToCommit = options.files || options.options?.files
    if (!filesToCommit?.length) throw new Error('Files array required for create-commit operation')

    const branchResult = await makeGitHubRequest(
      `/repos/${targetOwner}/${targetRepo}/branches/main`
    )
    const latestCommitSha = branchResult.data.commit.sha

    const treeEntries: any[] = []
    const fileContents: any[] = []

    for (const filePath of filesToCommit) {
      try {
        const fileResult = await makeGitHubRequest(
          `/repos/${targetOwner}/${targetRepo}/contents/${filePath}`
        )
        if (fileResult.data?.content) {
          treeEntries.push(
            Object.freeze({
              path: filePath,
              mode: '100644',
              type: 'blob',
              sha: fileResult.data.sha
            })
          )
          fileContents.push(
            Object.freeze({ path: filePath, sha: fileResult.data.sha, size: fileResult.data.size })
          )
        }
      } catch (error) {
        console.warn(`Could not get file ${filePath}: ${(error as Error).message}`)
      }
    }

    if (treeEntries.length === 0) throw new Error('No valid files found to commit')

    const newTreeResult = await makeGitHubRequest(`/repos/${targetOwner}/${targetRepo}/git/trees`, {
      method: 'POST',
      body: JSON.stringify({ base_tree: latestCommitSha, tree: treeEntries }),
      headers: { 'Content-Type': 'application/json' }
    })

    const commitData: any = {
      message: commitMessage,
      tree: newTreeResult.data.sha,
      parents: [latestCommitSha]
    }
    const authorInfo = options.author || options.options?.author
    const committerInfo = options.committer || options.options?.committer

    if (authorInfo)
      commitData.author = {
        name: authorInfo.name || 'Augster MCP',
        email: authorInfo.email || '<EMAIL>',
        date: new Date().toISOString()
      }
    if (committerInfo)
      commitData.committer = {
        name: committerInfo.name || 'Augster MCP',
        email: committerInfo.email || '<EMAIL>',
        date: new Date().toISOString()
      }

    const commitResult = await makeGitHubRequest(
      `/repos/${targetOwner}/${targetRepo}/git/commits`,
      {
        method: 'POST',
        body: JSON.stringify(commitData),
        headers: { 'Content-Type': 'application/json' }
      }
    )

    return GitHubUtils.buildResponse(
      Object.freeze({
        commitSha: commitResult.data.sha,
        commitMessage,
        filesCommitted: filesToCommit.length,
        files: Object.freeze(fileContents),
        tree: newTreeResult.data.sha,
        author: commitResult.data.author,
        committer: commitResult.data.committer,
        aiNotationMetadata: templateVars._aiNotationParams
          ? Object.freeze({
              parametersDetected: templateVars._aiNotationParams,
              toolsDetected: templateVars._aiNotationTools || {},
              workflowCapable: true,
              validationRequired: true
            })
          : undefined,
        timestamp: Date.now(),
        processingTime: 0
      })
    )
  }, 'create-commit')
}

// === MAIN HANDLER ===

/**
 * F:executeGitHubCommand - Execute GitHub command with resilience
 * @notation P:command F:executeGitHubCommand CB:executeGitHubCommand I:OperationResult DB:github
 */
export const executeGitHubCommand = async (
  command: GitHubCommand
): Promise<OperationResult<GitHubResult>> => {
  const config = createGitHubConfig()

  return await executeWithResilience(command, config, async cmd => {
    const { action, owner, repo, options = {} } = cmd

    switch (action) {
      case 'repo':
        return await executeGitHubRepo(owner, repo)

      case 'issues':
        return await executeGitHubIssues(owner, repo, options)

      case 'commits':
        return await executeGitHubCommits(owner, repo, options)

      case 'branches':
        return await executeGitHubBranches(owner, repo, options)

      case 'pulls':
        return await executeGitHubPulls(owner, repo, options)

      case 'files':
        return await executeGitHubFiles(owner, repo, options)

      case 'search':
        return await executeGitHubSearch(owner, repo, options)

      case 'create-issue':
        return await executeCreateIssue(owner, repo, options)

      case 'template':
        const templateSource = cmd.content || cmd.path
        if (!templateSource)
          throw new Error('Either path or content must be provided for template action')
        return await executeGitHubTemplate(
          templateSource,
          (options?.templateVars as Record<string, unknown>) || {},
          (options?.templateEngine as 'simple' | 'mustache') || 'simple'
        )

      default:
        throw new Error(`Unknown GitHub action: ${action}`)
    }
  })
}

/**
 * F:executeGitHubTemplate - Execute GitHub template operation
 * @notation P:templateSource,vars,engine F:executeGitHubTemplate CB:executeGitHubTemplate I:GitHubResult DB:none
 */
export const executeGitHubTemplate = async (
  templateSource: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple'
): Promise<GitHubResult> => {
  try {
    const result = await processMultiHandlerTemplate(templateSource, vars, engine)

    return Object.freeze({
      success: true,
      data: result.content,
      processingTime: 0,
      timestamp: Date.now()
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message
    })
  }
}

/**
 * F:githubHandler - Create GitHub handler function
 * @notation P:db F:githubHandler CB:none I:function DB:github
 */
export const githubHandler = () => {
  return {
    execute: async (input: unknown): Promise<GitHubResult> => {
      if (!validateGitHubCommand(input)) {
        return Object.freeze({
          success: false,
          error: 'Invalid GitHub command structure'
        })
      }

      const result = await executeGitHubCommand(input)
      return result.success
        ? result.data
        : Object.freeze({
            success: false,
            error: result.error
          })
    }
  }
}
