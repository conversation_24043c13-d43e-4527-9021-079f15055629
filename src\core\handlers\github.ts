/**
 * GitHub Handler - Unified Export Module
 * Refactored from single 1436-line file to 3 constraint-compliant files (≤150 lines each)
 *
 * @notation P:core/handlers/github F:githubHandler,executeGitHubCommand CB:executeGitHubCommand I:GitHubCommand,GitHubResult DB:github
 */

// Re-export all functionality from split modules
export * from './github-core'
export * from './github-api'
export * from './github-utils'
