/**
 * Security State - AI-Optimized Security State Management
 * @notation P:core/state/securityState F:createSecurityState,addSecurityEvent CB:addSecurityEvent I:SecurityState,SecurityEventLog DB:security
 */

import {
  SecurityEvent,
  SecurityValidationResult,
  SecurityConfig,
  DEFAULT_SECURITY_CONFIG,
  createSecurityEvent,
  createSecurityValidationResult,
  ALLOWED_GIT_COMMANDS,
  ALLOWED_TERMINAL_COMMANDS
} from '../schema/securitySchema'

export type SecurityState = {
  readonly config: SecurityConfig
  readonly events: readonly SecurityEvent[]
  readonly maxEvents: number
  readonly lastValidation: SecurityValidationResult | null
  readonly validationCount: number
  readonly blockedCount: number
}

export type SecurityEventLog = {
  readonly events: readonly SecurityEvent[]
  readonly totalEvents: number
  readonly blockedEvents: number
  readonly criticalEvents: number
  readonly recentEvents: readonly SecurityEvent[]
}

export type SecurityMetrics = {
  readonly totalValidations: number
  readonly successfulValidations: number
  readonly blockedAttempts: number
  readonly criticalEvents: number
  readonly averageValidationTime: number
  readonly lastEventTime: number | null
}

/**
 * F:createSecurityState - Create initial security state
 * @notation P:config,maxEvents F:createSecurityState CB:none I:SecurityState DB:security
 */
export const createSecurityState = (
  config: Partial<SecurityConfig> = {},
  maxEvents: number = 1000
): SecurityState => {
  return Object.freeze({
    config: Object.freeze({ ...DEFAULT_SECURITY_CONFIG, ...config }),
    events: Object.freeze([]),
    maxEvents,
    lastValidation: null,
    validationCount: 0,
    blockedCount: 0
  })
}

/**
 * F:addSecurityEvent - Add security event to state
 * @notation P:state,event F:addSecurityEvent CB:addSecurityEvent I:SecurityState DB:security
 */
export const addSecurityEvent = (state: SecurityState, event: SecurityEvent): SecurityState => {
  const newEvents = [...state.events, event]
  const trimmedEvents =
    newEvents.length > state.maxEvents ? newEvents.slice(-state.maxEvents) : newEvents

  return Object.freeze({
    ...state,
    events: Object.freeze(trimmedEvents),
    blockedCount: event.blocked ? state.blockedCount + 1 : state.blockedCount
  })
}

/**
 * F:updateLastValidation - Update last validation result
 * @notation P:state,result F:updateLastValidation CB:none I:SecurityState DB:none
 */
export const updateLastValidation = (
  state: SecurityState,
  result: SecurityValidationResult
): SecurityState => {
  return Object.freeze({
    ...state,
    lastValidation: result,
    validationCount: state.validationCount + 1
  })
}

/**
 * F:clearSecurityEvents - Clear all security events
 * @notation P:state F:clearSecurityEvents CB:none I:SecurityState DB:security
 */
export const clearSecurityEvents = (state: SecurityState): SecurityState => {
  return Object.freeze({
    ...state,
    events: Object.freeze([]),
    blockedCount: 0
  })
}

/**
 * F:getRecentSecurityEvents - Get recent security events within time window
 * @notation P:state,timeWindowMs F:getRecentSecurityEvents CB:none I:array DB:none
 */
export const getRecentSecurityEvents = (
  state: SecurityState,
  timeWindowMs: number = 300000 // 5 minutes
): readonly SecurityEvent[] => {
  const cutoffTime = Date.now() - timeWindowMs
  return Object.freeze(
    state.events.filter(event => new Date(event.timestamp).getTime() >= cutoffTime)
  )
}

/**
 * F:getSecurityEventsByType - Get security events by type
 * @notation P:state,type F:getSecurityEventsByType CB:none I:array DB:none
 */
export const getSecurityEventsByType = (
  state: SecurityState,
  type: SecurityEvent['type']
): readonly SecurityEvent[] => {
  return Object.freeze(state.events.filter(event => event.type === type))
}

/**
 * F:getSecurityEventsBySeverity - Get security events by severity
 * @notation P:state,severity F:getSecurityEventsBySeverity CB:none I:array DB:none
 */
export const getSecurityEventsBySeverity = (
  state: SecurityState,
  severity: SecurityEvent['severity']
): readonly SecurityEvent[] => {
  return Object.freeze(state.events.filter(event => event.severity === severity))
}

/**
 * F:getSecurityMetrics - Get security metrics from state
 * @notation P:state F:getSecurityMetrics CB:none I:SecurityMetrics DB:none
 */
export const getSecurityMetrics = (state: SecurityState): SecurityMetrics => {
  const criticalEvents = state.events.filter(e => e.severity === 'critical').length
  const lastEvent = state.events[state.events.length - 1]

  return Object.freeze({
    totalValidations: state.validationCount,
    successfulValidations: state.validationCount - state.blockedCount,
    blockedAttempts: state.blockedCount,
    criticalEvents,
    averageValidationTime: 0, // Would need timing data
    lastEventTime: lastEvent ? new Date(lastEvent.timestamp).getTime() : null
  })
}

/**
 * F:validateCommandWithState - Validate command and update state
 * @notation P:state,command,args,allowedCommands F:validateCommandWithState CB:validateCommandWithState I:object DB:security
 */
export const validateCommandWithState = (
  state: SecurityState,
  command: string,
  args: readonly string[] = [],
  allowedCommands: ReadonlySet<string>
): { result: SecurityValidationResult; updatedState: SecurityState } => {
  const config = state.config

  if (!command || typeof command !== 'string') {
    const result = createSecurityValidationResult(false, 'Invalid command format')
    const event = createSecurityEvent(
      'validation_failure',
      'medium',
      'command-validator',
      { command, args },
      true
    )
    return {
      result,
      updatedState: addSecurityEvent(updateLastValidation(state, result), event)
    }
  }

  if (command.length > config.maxCommandLength) {
    const result = createSecurityValidationResult(false, 'Command too long')
    const event = createSecurityEvent(
      'command_injection',
      'high',
      'command-validator',
      { command, length: command.length },
      true
    )
    return {
      result,
      updatedState: addSecurityEvent(updateLastValidation(state, result), event)
    }
  }

  if (config.dangerousPatterns.test(command)) {
    const result = createSecurityValidationResult(false, 'Dangerous pattern detected')
    const event = createSecurityEvent(
      'command_injection',
      'critical',
      'command-validator',
      { command, args },
      true
    )
    return {
      result,
      updatedState: addSecurityEvent(updateLastValidation(state, result), event)
    }
  }

  const normalizedCommand = command.toLowerCase().trim()
  if (!allowedCommands.has(normalizedCommand)) {
    const result = createSecurityValidationResult(
      false,
      `Command not allowed: ${normalizedCommand}`
    )
    const event = createSecurityEvent(
      'validation_failure',
      'medium',
      'command-validator',
      { command: normalizedCommand },
      true
    )
    return {
      result,
      updatedState: addSecurityEvent(updateLastValidation(state, result), event)
    }
  }

  const dangerousArg = args.find(arg => config.dangerousPatterns.test(arg))
  if (dangerousArg) {
    const result = createSecurityValidationResult(
      false,
      `Dangerous pattern in argument: ${dangerousArg}`
    )
    const event = createSecurityEvent(
      'command_injection',
      'high',
      'command-validator',
      { command, dangerousArg },
      true
    )
    return {
      result,
      updatedState: addSecurityEvent(updateLastValidation(state, result), event)
    }
  }

  const result = createSecurityValidationResult(true, undefined, command)
  const event = createSecurityEvent(
    'validation_failure',
    'low',
    'command-validator',
    { command, args },
    false
  )
  return {
    result,
    updatedState: addSecurityEvent(updateLastValidation(state, result), event)
  }
}

/**
 * F:validateGitCommandWithState - Validate git command with state
 * @notation P:state,command,args F:validateGitCommandWithState CB:validateGitCommandWithState I:object DB:security
 */
export const validateGitCommandWithState = (
  state: SecurityState,
  command: string,
  args: readonly string[] = []
): { result: SecurityValidationResult; updatedState: SecurityState } => {
  return validateCommandWithState(state, command, args, ALLOWED_GIT_COMMANDS)
}

/**
 * F:validateTerminalCommandWithState - Validate terminal command with state
 * @notation P:state,command,args F:validateTerminalCommandWithState CB:validateTerminalCommandWithState I:object DB:security
 */
export const validateTerminalCommandWithState = (
  state: SecurityState,
  command: string,
  args: readonly string[] = []
): { result: SecurityValidationResult; updatedState: SecurityState } => {
  return validateCommandWithState(state, command, args, ALLOWED_TERMINAL_COMMANDS)
}

/**
 * F:logSecurityEventToState - Log security event and update state
 * @notation P:state,type,severity,source,details,blocked F:logSecurityEventToState CB:logSecurityEventToState I:SecurityState DB:security
 */
export const logSecurityEventToState = (
  state: SecurityState,
  type: SecurityEvent['type'],
  severity: SecurityEvent['severity'],
  source: string,
  details: unknown,
  blocked: boolean
): SecurityState => {
  const event = createSecurityEvent(type, severity, source, details, blocked)
  return addSecurityEvent(state, event)
}

/**
 * F:createSecurityEventLog - Create security event log from state
 * @notation P:state,timeWindowMs F:createSecurityEventLog CB:none I:SecurityEventLog DB:none
 */
export const createSecurityEventLog = (
  state: SecurityState,
  timeWindowMs: number = 3600000 // 1 hour
): SecurityEventLog => {
  const recentEvents = getRecentSecurityEvents(state, timeWindowMs)
  const blockedEvents = state.events.filter(e => e.blocked).length
  const criticalEvents = state.events.filter(e => e.severity === 'critical').length

  return Object.freeze({
    events: state.events,
    totalEvents: state.events.length,
    blockedEvents,
    criticalEvents,
    recentEvents
  })
}

let globalSecurityState: SecurityState = createSecurityState()

/**
 * F:getGlobalSecurityState - Get global security state
 * @notation P:none F:getGlobalSecurityState CB:none I:SecurityState DB:none
 */
export const getGlobalSecurityState = (): SecurityState => globalSecurityState

/**
 * F:updateGlobalSecurityState - Update global security state
 * @notation P:newState F:updateGlobalSecurityState CB:none I:void DB:security
 */
export const updateGlobalSecurityState = (newState: SecurityState): void => {
  globalSecurityState = newState
}

/**
 * F:resetGlobalSecurityState - Reset global security state
 * @notation P:config,maxEvents F:resetGlobalSecurityState CB:none I:void DB:security
 */
export const resetGlobalSecurityState = (
  config?: Partial<SecurityConfig>,
  maxEvents?: number
): void => {
  globalSecurityState = createSecurityState(config, maxEvents)
}
