const fs = require('fs');
const path = require('path');

console.log('🔧 FIXING SYMBOLIC TRACE SYSTEM - FINAL FIX');
console.log('='.repeat(60));

const symbolIndexDir = '.augment/symbol-index';
if (!fs.existsSync(symbolIndexDir)) {
  fs.mkdirSync(symbolIndexDir, { recursive: true });
}

function scanForSymbols(dir, symbols = {}) {
  const items = fs.readdirSync(dir);
  
  items.forEach(item => {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      scanForSymbols(fullPath, symbols);
    } else if (item.endsWith('.ts')) {
      const content = fs.readFileSync(fullPath, 'utf-8');
      const relativePath = path.relative(process.cwd(), fullPath);
      
      const commentBlocks = content.match(/\/\*[\s\S]*?\*\//g) || [];
      
      commentBlocks.forEach(block => {
        if (block.includes('@notation')) {
          const notationMatch = block.match(/@notation\s+([^\n\r*]+)/);
          if (notationMatch) {
            const notation = notationMatch[1].trim();
            
            const symbolTypes = ['P:', 'F:', 'CB:', 'I:', 'DB:'];
            symbolTypes.forEach(type => {
              const regex = new RegExp(type + '([^\\s,]+)', 'g');
              let match;
              while ((match = regex.exec(notation)) !== null) {
                if (!symbols[type]) symbols[type] = [];
                symbols[type].push({
                  name: match[1],
                  file: relativePath,
                  notation: notation,
                  block: block.substring(0, 100) + '...'
                });
              }
            });
          }
        }
      });
    }
  });
  
  return symbols;
}

const symbols = scanForSymbols('src');

console.log('📊 SYMBOLS FOUND:');
let totalSymbols = 0;
Object.keys(symbols).forEach(type => {
  console.log('- ' + type + ' ' + symbols[type].length + ' symbols');
  totalSymbols += symbols[type].length;
});

console.log('📊 TOTAL SYMBOLS: ' + totalSymbols);

Object.keys(symbols).forEach(type => {
  const filename = type.replace(':', '') + '.symbol.json';
  const filepath = path.join(symbolIndexDir, filename);
  fs.writeFileSync(filepath, JSON.stringify(symbols[type], null, 2));
  console.log('✅ Created: ' + filename + ' (' + symbols[type].length + ' symbols)');
});

const masterIndex = {
  totalSymbols: totalSymbols,
  symbolTypes: Object.keys(symbols),
  generatedAt: new Date().toISOString(),
  files: Object.keys(symbols).map(type => type.replace(':', '') + '.symbol.json')
};

fs.writeFileSync(path.join(symbolIndexDir, 'index.json'), JSON.stringify(masterIndex, null, 2));

console.log('');
console.log('✅ SYMBOLIC TRACE SYSTEM FIXED');
console.log('📦 Symbol files created: ' + Object.keys(symbols).length);
console.log('📊 Total symbols indexed: ' + totalSymbols);
