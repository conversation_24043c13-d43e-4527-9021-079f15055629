/**
 * Manifest Generator - AI-Optimized Transformation Tracking
 * Pure functions for tracking and reporting code transformations
 *
 * @notation P:core/report/generateManifest F:createManifest,recordTransformation,generateReport CB:none I:RefactorManifest,TransformationRecord DB:none
 */

import * as fs from 'fs'
import * as path from 'path'
import { RefactorManifest, TransformationRecord, SymbolMove, AINotationSymbol } from '../types'
import { TRANSFORMATION_VERSION, MANIFEST_SCHEMA_VERSION } from '../constants'

/**
 * F:createManifest - Create a new refactor manifest
 * @notation P:none F:createManifest CB:none I:RefactorManifest DB:none
 */
export const createManifest = (): RefactorManifest => {
  return Object.freeze({
    version: MANIFEST_SCHEMA_VERSION,
    timestamp: Date.now(),
    transformations: Object.freeze([]),
    symbolMoves: Object.freeze([]),
    totalChanges: 0
  })
}

/**
 * F:recordTransformation - Record a transformation in the manifest
 * @notation P:manifest,transformation F:recordTransformation CB:none I:RefactorManifest,TransformationRecord DB:none
 */
export const recordTransformation = (
  manifest: RefactorManifest,
  transformation: Omit<TransformationRecord, 'timestamp'>
): RefactorManifest => {
  const fullTransformation = Object.freeze({
    ...transformation,
    timestamp: Date.now()
  })

  const newTransformations = [...manifest.transformations, fullTransformation]

  return Object.freeze({
    ...manifest,
    transformations: Object.freeze(newTransformations),
    totalChanges: manifest.totalChanges + 1
  })
}

/**
 * F:recordSymbolMove - Record a symbol move in the manifest
 * @notation P:manifest,symbolMove F:recordSymbolMove CB:none I:RefactorManifest,SymbolMove DB:none
 */
export const recordSymbolMove = (
  manifest: RefactorManifest,
  symbolMove: SymbolMove
): RefactorManifest => {
  const newSymbolMoves = [...manifest.symbolMoves, Object.freeze(symbolMove)]

  return Object.freeze({
    ...manifest,
    symbolMoves: Object.freeze(newSymbolMoves),
    totalChanges: manifest.totalChanges + 1
  })
}

/**
 * F:saveManifest - Save manifest to file system
 * @notation P:manifest,filePath F:saveManifest CB:none I:void DB:none
 */
export const saveManifest = async (
  manifest: RefactorManifest,
  filePath: string = '.augment/refactor-manifest.json'
): Promise<void> => {
  const manifestJson = JSON.stringify(manifest, null, 2)
  await fs.promises.writeFile(filePath, manifestJson, 'utf-8')
}

/**
 * F:loadManifest - Load manifest from file system
 * @notation P:filePath F:loadManifest CB:none I:RefactorManifest DB:none
 */
export const loadManifest = async (
  filePath: string = '.augment/refactor-manifest.json'
): Promise<RefactorManifest> => {
  try {
    const manifestJson = await fs.promises.readFile(filePath, 'utf-8')
    const manifest = JSON.parse(manifestJson) as RefactorManifest
    return Object.freeze(manifest)
  } catch (error) {
    return createManifest()
  }
}

/**
 * F:generateReport - Generate a human-readable transformation report
 * @notation P:manifest F:generateReport CB:none I:string DB:none
 */
export const generateReport = (manifest: RefactorManifest): string => {
  const { transformations, symbolMoves, totalChanges } = manifest

  const transformationsByType = transformations.reduce(
    (acc, t) => {
      acc[t.operation] = (acc[t.operation] || 0) + 1
      return acc
    },
    {} as Record<string, number>
  )

  const symbolMovesByType = symbolMoves.reduce(
    (acc, s) => {
      acc[s.type] = (acc[s.type] || 0) + 1
      return acc
    },
    {} as Record<string, number>
  )

  const successfulTransformations = transformations.filter(t => t.success).length
  const failedTransformations = transformations.length - successfulTransformations
  const successRate =
    transformations.length > 0
      ? ((successfulTransformations / transformations.length) * 100).toFixed(2)
      : '0'

  return `# Augster Transformation Report

## Summary
- **Total Changes**: ${totalChanges}
- **Transformations**: ${transformations.length}
- **Symbol Moves**: ${symbolMoves.length}
- **Success Rate**: ${successRate}%
- **Failed Operations**: ${failedTransformations}

## Transformations by Type
${Object.entries(transformationsByType)
  .map(([type, count]) => `- **${type}**: ${count}`)
  .join('\n')}

## Symbol Moves by Type
${Object.entries(symbolMovesByType)
  .map(([type, count]) => `- **${type}**: ${count}`)
  .join('\n')}

## Recent Transformations
${transformations
  .slice(-10)
  .map(t => `- ${t.operation} on ${t.sourceFile} ${t.success ? '✅' : '❌'}`)
  .join('\n')}

## Recent Symbol Moves
${symbolMoves
  .slice(-10)
  .map(s => `- ${s.symbol} (${s.type}) moved from ${s.fromFile} to ${s.toFile}`)
  .join('\n')}

---
Generated at: ${new Date().toISOString()}
Manifest Version: ${manifest.version}
`
}

/**
 * F:createSymbolIndex - Create symbol index for a module
 * @notation P:filePath,symbols F:createSymbolIndex CB:none I:SymbolIndex DB:none
 */
export const createSymbolIndex = (
  filePath: string,
  symbols: readonly {
    readonly name: string
    readonly type: string
    readonly notation: AINotationSymbol
    readonly line: number
  }[]
): SymbolIndex => {
  return Object.freeze({
    filePath,
    timestamp: Date.now(),
    symbols: Object.freeze(symbols.map(s => Object.freeze(s)))
  })
}

/**
 * F:saveSymbolIndex - Save symbol index to file
 * @notation P:symbolIndex,outputDir F:saveSymbolIndex CB:none I:void DB:none
 */
export const saveSymbolIndex = async (
  symbolIndex: SymbolIndex,
  outputDir: string = '.augment/symbol-index'
): Promise<void> => {
  const fileName = path.basename(symbolIndex.filePath, '.ts') + '.symbol.json'
  const outputPath = path.join(outputDir, fileName)

  await fs.promises.mkdir(outputDir, { recursive: true })

  const indexJson = JSON.stringify(symbolIndex, null, 2)
  await fs.promises.writeFile(outputPath, indexJson, 'utf-8')
}

/**
 * F:generateUndoScript - Generate undo script for transformations
 * @notation P:manifest F:generateUndoScript CB:none I:string DB:none
 */
export const generateUndoScript = (manifest: RefactorManifest): string => {
  const undoOperations = manifest.transformations
    .filter(t => t.success && t.rollbackData)
    .reverse()
    .map(t => {
      return `// Undo: ${t.operation} on ${t.sourceFile}
// Original changes: ${t.changes.join(', ')}
// Rollback data: ${JSON.stringify(t.rollbackData, null, 2)}
`
    })
    .join('\n')

  return `#!/usr/bin/env node
/**
 * Auto-generated undo script for Augster transformations
 * Generated at: ${new Date().toISOString()}
 * Manifest version: ${manifest.version}
 */

${undoOperations}

console.log('Undo script generated. Manual review required before execution.')
`
}

export type SymbolIndex = {
  readonly filePath: string
  readonly timestamp: number
  readonly symbols: readonly {
    readonly name: string
    readonly type: string
    readonly notation: AINotationSymbol
    readonly line: number
  }[]
}

/**
 * F:logTransformation - Log transformation to file
 * @notation P:transformation,logPath F:logTransformation CB:none I:void DB:none
 */
export const logTransformation = async (
  transformation: TransformationRecord,
  logPath: string = '.augment/logs/refactor.log'
): Promise<void> => {
  const logEntry = `[${new Date().toISOString()}] ${transformation.operation} on ${transformation.sourceFile} - ${transformation.success ? 'SUCCESS' : 'FAILED'}\n`

  await fs.promises.mkdir(path.dirname(logPath), { recursive: true })

  await fs.promises.appendFile(logPath, logEntry, 'utf-8')
}
