{"systemErrors": [{"type": "systemErrors", "error": "Cannot find module './src/core/handlers/memory'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js", "context": {"handler": "memory"}, "timestamp": "2025-06-18T23:06:22.856Z", "stack": "Error: Cannot find module './src/core/handlers/memory'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at testHandler (C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js:43:27)\n    at runAllTests (C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js:105:11)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js:122:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Module.load (node:internal/modules/cjs/loader:1207:32)"}, {"type": "systemErrors", "error": "Cannot find module './src/core/handlers/file'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js", "context": {"handler": "file"}, "timestamp": "2025-06-18T23:06:22.858Z", "stack": "Error: Cannot find module './src/core/handlers/file'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at testHandler (C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js:43:27)\n    at runAllTests (C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js:105:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}, {"type": "systemErrors", "error": "Cannot find module './src/core/handlers/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js", "context": {"handler": "database"}, "timestamp": "2025-06-18T23:06:22.859Z", "stack": "Error: Cannot find module './src/core/handlers/database'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at testHandler (C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js:43:27)\n    at runAllTests (C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js:105:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}, {"type": "systemErrors", "error": "Cannot find module './src/core/handlers/github'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js", "context": {"handler": "github"}, "timestamp": "2025-06-18T23:06:22.860Z", "stack": "Error: Cannot find module './src/core/handlers/github'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at testHandler (C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js:43:27)\n    at runAllTests (C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js:105:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}, {"type": "systemErrors", "error": "Cannot find module './src/core/handlers/monitoring'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js", "context": {"handler": "monitoring"}, "timestamp": "2025-06-18T23:06:22.860Z", "stack": "Error: Cannot find module './src/core/handlers/monitoring'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at testHandler (C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js:43:27)\n    at runAllTests (C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js:105:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}, {"type": "systemErrors", "error": "Cannot find module './src/core/handlers/coordination'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js", "context": {"handler": "coordination"}, "timestamp": "2025-06-18T23:06:22.860Z", "stack": "Error: Cannot find module './src/core/handlers/coordination'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at testHandler (C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js:43:27)\n    at runAllTests (C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js:105:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}, {"type": "systemErrors", "error": "Cannot find module './src/core/handlers/fetch'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js", "context": {"handler": "fetch"}, "timestamp": "2025-06-18T23:06:22.861Z", "stack": "Error: Cannot find module './src/core/handlers/fetch'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at testHandler (C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js:43:27)\n    at runAllTests (C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js:105:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}, {"type": "systemErrors", "error": "Cannot find module './src/core/handlers/time'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js", "context": {"handler": "time"}, "timestamp": "2025-06-18T23:06:22.861Z", "stack": "Error: Cannot find module './src/core/handlers/time'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at testHandler (C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js:43:27)\n    at runAllTests (C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js:105:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}, {"type": "systemErrors", "error": "Cannot find module './src/core/handlers/git'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js", "context": {"handler": "git"}, "timestamp": "2025-06-18T23:06:22.862Z", "stack": "Error: Cannot find module './src/core/handlers/git'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at testHandler (C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js:43:27)\n    at runAllTests (C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js:105:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}, {"type": "systemErrors", "error": "Cannot find module './src/core/handlers/terminal'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js", "context": {"handler": "terminal"}, "timestamp": "2025-06-18T23:06:22.862Z", "stack": "Error: Cannot find module './src/core/handlers/terminal'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at testHandler (C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js:43:27)\n    at runAllTests (C:\\Users\\<USER>\\Desktop\\augster_mvp_scaffold\\test-all-handlers.js:105:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"}], "toolFailures": [], "augmentToolFailures": [], "builtInToolFailures": [], "lessonsLearned": [{"lesson": "Symbolic trace system was broken - 0 symbols found due to incorrect regex for @notation extraction from comment blocks", "fix": "Fixed regex to properly extract @notation from /* */ comment blocks, now 2305 symbols indexed", "timestamp": "2025-06-18T23:08:04.454Z"}, {"lesson": "Handler integration working but handlers returning errors due to invalid command structure", "fix": "Need to investigate proper payload format for each handler type", "timestamp": "2025-06-18T23:08:04.454Z"}, {"lesson": "Preemptive validation pipeline working but architectural compliance failing", "fix": "Architectural compliance check needs refinement for real execution scenarios", "timestamp": "2025-06-18T23:08:04.454Z"}, {"lesson": "File handler working perfectly - file.read successfully returned package.json content", "fix": "File handler properly integrated into agent workflow with 6ms execution time", "timestamp": "2025-06-18T23:09:02.975Z"}], "userCorrections": [], "handlerTestResults": [{"handler": "memory", "action": "query", "success": false, "result": null, "error": "Invalid memory command structure", "timestamp": "2025-06-18T23:08:04.451Z", "executionPath": "COMPLETE_AGENT_WORKFLOW_WITH_HANDLER", "handlerExecuted": true}, {"handler": "file", "action": "read", "success": true, "result": "Successfully read package.json (2036 bytes)", "error": null, "timestamp": "2025-06-18T23:09:02.974Z", "executionPath": "COMPLETE_AGENT_WORKFLOW_WITH_HANDLER", "handlerExecuted": true, "executionTime": "6ms"}], "createdAt": "2025-01-18T00:00:00.000Z", "lastUpdated": "2025-06-18T23:09:02.975Z", "version": "1.0.0"}