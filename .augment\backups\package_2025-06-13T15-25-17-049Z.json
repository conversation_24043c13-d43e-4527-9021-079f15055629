{"name": "augster_mvp_scaffold", "version": "1.0.0", "description": "", "main": "dist/index.js", "type": "commonjs", "engines": {"node": ">=18.0.0"}, "scripts": {"build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "ts-node src/index.ts", "predev": "node -e \"const fs=require('fs'); if(!fs.existsSync('.env.dev')){console.error('❌ .env.dev not found'); process.exit(1)}\"", "preprod": "node -e \"const fs=require('fs'); if(!fs.existsSync('.env.memory')){console.error('❌ .env.memory not found'); process.exit(1)}\"", "validate:env": "node -e \"const fs=require('fs'); const files=['.env.dev','.env.memory']; files.forEach(f=>{if(!fs.existsSync(f))console.warn('⚠️ Missing:',f)})\"", "mcp:dev": "npm run predev && node -r ts-node/register src/mcp/mcp-launcher.ts --dev", "mcp:prod": "npm run preprod && node -r ts-node/register src/mcp/mcp-launcher.ts --prod", "mcp:compiled": "npm run build && npm run preprod && node -r ts-node/register src/mcp/mcp-launcher.ts --prod --compiled", "setup": "npm install && npm run validate:env && npm run build", "start:dev": "concurrently \"npm run mcp:dev\" \"npm run dev\"", "start:prod": "npm run mcp:compiled && node dist/index.js", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "format": "prettier --write .", "check-format": "prettier --check .", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@types/node": "^24.0.0", "ajv": "^8.17.1", "chalk": "^5.4.1", "concurrently": "^8.2.2", "dotenv": "^16.5.0", "fs-extra": "^11.3.0", "sqlite3": "^5.1.7", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "devDependencies": {"@types/dotenv": "^8.2.3", "@types/fs-extra": "^11.0.4", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "cross-env": "^7.0.3", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "prettier": "^3.5.3", "rimraf": "^6.0.1"}}