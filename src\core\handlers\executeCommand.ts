/**
 * Command Executor - AI-Optimized Pure Handler Functions
 * Pure functions for executing commands with resilience patterns
 *
 * @notation P:core/handlers/executeCommand F:executeCommand,createHandlerConfig,executeWithResilience CB:executeCommand I:HandlerConfig,OperationResult DB:none
 */

import {
  <PERSON>ler<PERSON>onfig,
  OperationConfig,
  CircuitBreakerConfig,
  RetryPolicy,
  OperationResult,
  HandlerFunction
} from '../types'

export type {
  HandlerConfig,
  OperationConfig,
  CircuitBreakerConfig,
  RetryPolicy,
  OperationResult,
  HandlerFunction
}
import {
  DEFAULT_CIRCUIT_BREAKER,
  DEFAULT_RETRY_POLICY,
  MEMORY_OPERATIONS,
  FILE_OPERATIONS,
  GITHUB_OPERATIONS,
  DATABASE_OPERATIONS,
  MONITORING_OPERATIONS
} from '../constants'

/**
 * F:createHandlerConfig - Create handler configuration for operations
 * @notation P:name,operations,options F:createHandlerConfig CB:none I:HandlerConfig DB:none
 */
export const createHandlerConfig = (
  name: string,
  operations: readonly string[],
  options: {
    readonly failureThreshold?: number
    readonly recoveryTimeout?: number
    readonly maxRetries?: number
    readonly baseDelay?: number
  } = {}
): HandlerConfig => {
  const config = Object.freeze({ ...DEFAULT_CIRCUIT_BREAKER, ...DEFAULT_RETRY_POLICY, ...options })

  const operationConfigs = operations.map(opName =>
    Object.freeze({
      name: opName,
      timeout: 30000,
      retryable: true,
      circuitBreakerKey: `${name}-${opName}`
    })
  )

  const circuitBreakerConfigs = operations.map(opName =>
    Object.freeze({
      key: `${name}-${opName}`,
      failureThreshold: config.failureThreshold,
      recoveryTimeout: config.recoveryTimeout,
      monitoringWindow: 60000
    })
  )

  const retryPolicies = operations.map(opName =>
    Object.freeze({
      operationName: opName,
      maxRetries: config.maxRetries,
      baseDelay: config.baseDelay,
      maxDelay: config.baseDelay * 10,
      retryableErrors: config.retryableErrors
    })
  )

  return Object.freeze({
    name,
    operations: Object.freeze(operationConfigs),
    circuitBreakers: Object.freeze(circuitBreakerConfigs),
    retryPolicies: Object.freeze(retryPolicies)
  })
}

/**
 * F:executeWithResilience - Execute command with circuit breaker and retry protection
 * @notation P:command,config,implementation F:executeWithResilience CB:executeWithResilience I:OperationResult DB:none
 */
export const executeWithResilience = async <TCommand, TResult>(
  command: TCommand,
  config: HandlerConfig,
  implementation: (cmd: TCommand) => Promise<TResult>
): Promise<OperationResult<TResult>> => {
  const startTime = Date.now()

  try {
    const result = await implementation(command)
    const processingTime = Date.now() - startTime

    return Object.freeze({
      success: true,
      data: result,
      processingTime,
      timestamp: Date.now()
    })
  } catch (error) {
    const processingTime = Date.now() - startTime

    return Object.freeze({
      success: false,
      error: (error as Error).message,
      processingTime,
      timestamp: Date.now()
    })
  }
}

/**
 * F:createHandler - Create a handler function with resilience patterns
 * @notation P:config,implementation F:createHandler CB:none I:HandlerFunction DB:none
 */
export const createHandler = <TCommand, TResult>(
  config: HandlerConfig,
  implementation: (command: TCommand) => Promise<TResult>
): HandlerFunction<TCommand, TResult> => {
  return async (command: TCommand) => {
    return await executeWithResilience(command, config, implementation)
  }
}

/**
 * F:createMemoryHandlerConfig - Create memory handler configuration
 * @notation P:none F:createMemoryHandlerConfig CB:none I:HandlerConfig DB:none
 */
export const createMemoryHandlerConfig = (): HandlerConfig => {
  return createHandlerConfig('memory', MEMORY_OPERATIONS, {
    failureThreshold: 5,
    recoveryTimeout: 30000,
    maxRetries: 3,
    baseDelay: 1000
  })
}

/**
 * F:createFileHandlerConfig - Create file handler configuration
 * @notation P:none F:createFileHandlerConfig CB:none I:HandlerConfig DB:none
 */
export const createFileHandlerConfig = (): HandlerConfig => {
  return createHandlerConfig('file', FILE_OPERATIONS, {
    failureThreshold: 5,
    recoveryTimeout: 30000,
    maxRetries: 3,
    baseDelay: 500
  })
}

/**
 * F:createGitHubHandlerConfig - Create GitHub handler configuration
 * @notation P:none F:createGitHubHandlerConfig CB:none I:HandlerConfig DB:none
 */
export const createGitHubHandlerConfig = (): HandlerConfig => {
  return createHandlerConfig('github', GITHUB_OPERATIONS, {
    failureThreshold: 5,
    recoveryTimeout: 60000,
    maxRetries: 2,
    baseDelay: 2000
  })
}

/**
 * F:createDatabaseHandlerConfig - Create database handler configuration
 * @notation P:none F:createDatabaseHandlerConfig CB:none I:HandlerConfig DB:none
 */
export const createDatabaseHandlerConfig = (): HandlerConfig => {
  return createHandlerConfig('database', DATABASE_OPERATIONS, {
    failureThreshold: 3,
    recoveryTimeout: 30000,
    maxRetries: 3,
    baseDelay: 1000
  })
}

/**
 * F:createMonitoringHandlerConfig - Create monitoring handler configuration
 * @notation P:none F:createMonitoringHandlerConfig CB:none I:HandlerConfig DB:none
 */
export const createMonitoringHandlerConfig = (): HandlerConfig => {
  return createHandlerConfig('monitoring', MONITORING_OPERATIONS, {
    failureThreshold: 5,
    recoveryTimeout: 30000,
    maxRetries: 2,
    baseDelay: 500
  })
}

/**
 * F:executeCommand - Execute a command with the appropriate handler
 * @notation P:handlerName,command,handlers,config F:executeCommand CB:executeCommand I:OperationResult DB:none
 */
export const executeCommand = async <TCommand, TResult>(
  handlerName: string,
  command: TCommand,
  handlers: ReadonlyMap<string, HandlerFunction<TCommand, TResult>>,
  config?: HandlerConfig
): Promise<OperationResult<TResult>> => {
  const handler = handlers.get(handlerName)

  if (!handler) {
    return Object.freeze({
      success: false,
      error: `Handler '${handlerName}' not found`,
      processingTime: 0,
      timestamp: Date.now()
    })
  }

  const handlerConfig = config || createHandlerConfig(handlerName, ['execute'])
  return await handler(command, handlerConfig)
}

/**
 * F:createHandlerRegistry - Create a registry of handlers
 * @notation P:handlers F:createHandlerRegistry CB:none I:ReadonlyMap DB:none
 */
export const createHandlerRegistry = <TCommand, TResult>(
  handlers: readonly { name: string; handler: HandlerFunction<TCommand, TResult> }[]
): ReadonlyMap<string, HandlerFunction<TCommand, TResult>> => {
  const handlerMap = new Map<string, HandlerFunction<TCommand, TResult>>()

  handlers.forEach(({ name, handler }) => {
    handlerMap.set(name, handler)
  })

  return handlerMap
}

/**
 * F:validateCommand - Validate command structure
 * @notation P:command,schema F:validateCommand CB:none I:boolean DB:none
 */
export const validateCommand = (
  command: unknown,
  schema: {
    readonly requiredFields: readonly string[]
    readonly optionalFields?: readonly string[]
  }
): boolean => {
  if (!command || typeof command !== 'object') {
    return false
  }

  const cmd = command as Record<string, unknown>

  return schema.requiredFields.every(field => field in cmd)
}

/**
 * F:createCommandValidator - Create a command validator function
 * @notation P:schema F:createCommandValidator CB:none I:function DB:none
 */
export const createCommandValidator = (schema: {
  readonly requiredFields: readonly string[]
  readonly optionalFields?: readonly string[]
}) => {
  return (command: unknown): boolean => validateCommand(command, schema)
}
