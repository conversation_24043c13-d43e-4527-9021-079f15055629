{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Fetch Operations Schema", "description": "Validation schema for fetch operations handler commands", "type": "object", "properties": {"action": {"type": "string", "enum": ["fetch", "fetch_text", "fetch_json", "template"]}, "url": {"type": "string", "description": "URL to fetch"}, "path": {"type": "string", "description": "Template file path for template action"}, "content": {"type": "string", "description": "Template content for template action"}, "options": {"type": "object", "properties": {"method": {"type": "string", "enum": ["GET", "POST", "PUT", "DELETE", "PATCH"], "default": "GET"}, "headers": {"type": "object", "description": "HTTP headers"}, "body": {"type": "string", "description": "Request body"}, "timeout": {"type": "number", "minimum": 1000, "maximum": 30000, "default": 10000}, "templateEngine": {"type": "string", "enum": ["simple", "mustache"], "default": "simple"}, "templateVars": {"type": "object", "description": "Template variables for substitution"}, "templateOutputPath": {"type": "string", "description": "Output path for processed template"}}}}, "required": ["action"], "additionalProperties": false}