/**
 * Agent Engine Core - Types, State Management, and Configuration
 * Split from engine.ts for constraint compliance (≤150 lines)
 *
 * @notation P:core/tools/agent/engine-core F:createAgentState,createDefaultAgentConfig CB:stateManagement I:AgentState,AgentConfig DB:agent
 */

import { Database } from 'sqlite3'
import * as fs from 'fs'
import * as path from 'path'

export type ContextualState = {
  readonly manifest: any
  readonly feedback: any
  readonly executionPlan: any
  readonly metrics: any
  readonly symbolicTrace: Record<string, string>
  readonly timestamp: number
}

export type DirectiveEnforcementResult = {
  readonly success: boolean
  readonly contextConsumed: boolean
  readonly symbolicTraceActive: boolean
  readonly feedbackIntegrated: boolean
  readonly error?: string
}

export type AgentState = {
  readonly isRunning: boolean
  readonly startTime: number
  readonly heartbeatInterval: number
  readonly lastHeartbeat: number
  readonly database: Database | null
}

export type AgentConfig = {
  readonly heartbeatInterval: number
  readonly enableLogging: boolean
  readonly shutdownTimeout: number
}

export type AgentResult = {
  readonly success: boolean
  readonly state: AgentState
  readonly error?: string
  readonly timestamp: number
}

/**
 * F:createDefaultAgentConfig - Create default agent configuration
 * @notation P:none F:createDefaultAgentConfig CB:none I:AgentConfig DB:none
 */
export const createDefaultAgentConfig = (): AgentConfig => {
  return Object.freeze({
    heartbeatInterval: 30000, // 30 seconds
    enableLogging: true,
    shutdownTimeout: 5000 // 5 seconds
  })
}

/**
 * F:createAgentState - Create initial agent state
 * @notation P:database,config F:createAgentState CB:none I:AgentState DB:agent
 */
export const createAgentState = (
  database: Database | null = null,
  config: AgentConfig = createDefaultAgentConfig()
): AgentState => {
  const now = Date.now()
  return Object.freeze({
    isRunning: false,
    startTime: now,
    heartbeatInterval: config.heartbeatInterval,
    lastHeartbeat: now,
    database
  })
}

/**
 * F:loadAgentDirectives - Load agent directives from file
 * @notation P:directivePath F:loadAgentDirectives CB:fileSystem I:string DB:none
 */
export const loadAgentDirectives = (directivePath: string): string => {
  try {
    if (fs.existsSync(directivePath)) {
      const content = fs.readFileSync(directivePath, 'utf-8')
      console.log(`📋 DIRECTIVES: Loaded from ${directivePath}`)
      return content
    } else {
      console.log(`⚠️ DIRECTIVES: File not found at ${directivePath}, using defaults`)
      return `
# Default Agent Directives
- Execute with constraint awareness
- Respect token/tool/memory limits
- Use MCP-wrapped tools only
- Format: TRACE → STATUS → SUMMARY
- Log traits with evidence
- Capture all failures and reflect
- Enforce schema compliance
- Template required
      `.trim()
    }
  } catch (error) {
    console.error(`❌ DIRECTIVES: Failed to load from ${directivePath}:`, (error as Error).message)
    return '# Fallback directives - basic execution mode'
  }
}

/**
 * F:consumeContextualState - Load contextual state from workspace
 * @notation P:workspaceRoot F:consumeContextualState CB:fileSystem I:ContextualState DB:none
 */
export const consumeContextualState = (workspaceRoot: string): ContextualState => {
  const manifestPath = path.join(workspaceRoot, '.augment', 'refactor-manifest.json')

  try {
    let manifest = {}
    if (fs.existsSync(manifestPath)) {
      const manifestContent = fs.readFileSync(manifestPath, 'utf-8')
      manifest = JSON.parse(manifestContent)
      console.log('📄 CONTEXT: Loaded refactor manifest')
    }

    const feedbackPath = path.join(workspaceRoot, '.augment', 'feedback.json')
    let feedback = {}
    if (fs.existsSync(feedbackPath)) {
      const feedbackContent = fs.readFileSync(feedbackPath, 'utf-8')
      feedback = JSON.parse(feedbackContent)
      console.log('💬 CONTEXT: Loaded feedback data')
    }

    const executionPlanPath = path.join(workspaceRoot, '.augment', 'execution-plan.json')
    let executionPlan = {}
    if (fs.existsSync(executionPlanPath)) {
      const planContent = fs.readFileSync(executionPlanPath, 'utf-8')
      executionPlan = JSON.parse(planContent)
      console.log('📋 CONTEXT: Loaded execution plan')
    }

    const metricsPath = path.join(workspaceRoot, '.augment', 'metrics.json')
    let metrics = {}
    if (fs.existsSync(metricsPath)) {
      const metricsContent = fs.readFileSync(metricsPath, 'utf-8')
      metrics = JSON.parse(metricsContent)
      console.log('📊 CONTEXT: Loaded metrics data')
    }

    return Object.freeze({
      manifest,
      feedback,
      executionPlan,
      metrics,
      symbolicTrace: {},
      timestamp: Date.now()
    })
  } catch (error) {
    console.error('❌ CONTEXT: Failed to load contextual state:', (error as Error).message)
    return Object.freeze({
      manifest: {},
      feedback: {},
      executionPlan: {},
      metrics: {},
      symbolicTrace: {},
      timestamp: Date.now()
    })
  }
}

/**
 * F:enforceDirectiveCompliance - Enforce directive compliance on response
 * @notation P:context,response F:enforceDirectiveCompliance CB:validation I:DirectiveEnforcementResult DB:none
 */
export const enforceDirectiveCompliance = (
  context: ContextualState,
  response: string
): DirectiveEnforcementResult => {
  try {
    const hasTraceFormat = response.includes('--- AUGSTER DISTRIBUTED TRACE ---')
    const hasStatusSection = response.includes('STATUS')
    const hasSummarySection = response.includes('SUMMARY')
    const hasSchemaCompliance = response.includes('schema')
    const hasContextReference = Object.keys(context.manifest).length > 0

    return Object.freeze({
      success: hasTraceFormat && hasStatusSection && hasSummarySection,
      contextConsumed: hasContextReference,
      symbolicTraceActive: hasTraceFormat,
      feedbackIntegrated: Object.keys(context.feedback).length > 0,
      error: !hasTraceFormat ? 'Missing TRACE format' : undefined
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      contextConsumed: false,
      symbolicTraceActive: false,
      feedbackIntegrated: false,
      error: (error as Error).message
    })
  }
}
