{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Time Operations Schema", "description": "Validation schema for time operations handler commands", "type": "object", "properties": {"command": {"type": "string", "enum": ["time.current", "time.convert", "time.format", "time.parse", "time.add", "time.subtract", "time.diff"]}, "action": {"type": "string", "enum": ["current", "convert", "format", "parse", "add", "subtract", "diff", "template"]}, "path": {"type": "string", "description": "Template file path for template action"}, "content": {"type": "string", "description": "Template content for template action"}, "options": {"type": "object", "properties": {"templateEngine": {"type": "string", "enum": ["simple", "mustache"], "default": "simple"}, "templateVars": {"type": "object", "description": "Template variables for substitution"}, "templateOutputPath": {"type": "string", "description": "Output path for processed template"}}}, "timezone": {"type": "string", "description": "IANA timezone identifier (e.g., UTC, America/New_York, Europe/London)", "pattern": "^[A-Za-z_]+(/[A-Za-z_]+)*$"}, "timestamp": {"oneOf": [{"type": "number", "minimum": 0}, {"type": "string", "format": "date-time"}, {"type": "string", "pattern": "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}"}], "description": "Unix timestamp (number) or ISO 8601 date string"}, "format": {"type": "string", "description": "Custom format string (YYYY, MM, DD, HH, mm, ss)", "maxLength": 100}, "fromTimezone": {"type": "string", "description": "Source timezone for conversion", "pattern": "^[A-Za-z_]+(/[A-Za-z_]+)*$"}, "toTimezone": {"type": "string", "description": "Target timezone for conversion", "pattern": "^[A-Za-z_]+(/[A-Za-z_]+)*$"}, "amount": {"type": "number", "description": "Amount to add/subtract", "minimum": -1000000, "maximum": 1000000}, "unit": {"type": "string", "enum": ["seconds", "minutes", "hours", "days", "weeks", "months", "years"], "description": "Time unit for arithmetic operations"}, "timestamp1": {"oneOf": [{"type": "number", "minimum": 0}, {"type": "string", "format": "date-time"}, {"type": "string", "pattern": "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}"}], "description": "First timestamp for difference calculation"}, "timestamp2": {"oneOf": [{"type": "number", "minimum": 0}, {"type": "string", "format": "date-time"}, {"type": "string", "pattern": "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}"}], "description": "Second timestamp for difference calculation"}}, "required": ["action"], "allOf": [{"if": {"properties": {"action": {"const": "convert"}}}, "then": {"required": ["timestamp"], "properties": {"fromTimezone": {"type": "string"}, "toTimezone": {"type": "string"}}}}, {"if": {"properties": {"action": {"const": "format"}}}, "then": {"required": ["timestamp"]}}, {"if": {"properties": {"action": {"enum": ["add", "subtract"]}}}, "then": {"required": ["timestamp", "amount", "unit"]}}, {"if": {"properties": {"action": {"const": "diff"}}}, "then": {"required": ["timestamp1", "timestamp2"]}}], "additionalProperties": false}