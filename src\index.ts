import { Database } from 'sqlite3'
import { executeAgentLoopLegacy as executeAgent<PERSON>oop } from './core/tools/agent/engine'

/**
 * F:initDB - Initialize SQLite database for agent system
 * @notation P:none F:initDB CB:none I:Database DB:agent
 */
const initDB = (): Database => {
  const dbPath = process.env.MCP_DB_PATH || ':memory:'
  return new Database(dbPath)
}

console.log('🚀 Starting Augster Agent...')
const db = initDB()

executeAgentLoop(db)
