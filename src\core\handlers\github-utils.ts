/**
 * GitHub Utilities - Extended Operations and Handler
 * @notation P:core/handlers/github-utils F:executeGitHubCommits,executeGitHubCommand,githubHandler CB:executeGitHubCommand I:GitHubCommand,GitHubResult DB:github
 */

import { processMultiHandlerTemplate } from '../tools'
import { GitHubCommand, GitHubResponse } from '../types'
import { GitHubResult, createGitHubConfig, validateGitHubCommand } from './github-core'
import { 
  makeGitHubRequest, 
  executeGitHubRepo, 
  executeGitHubIssues, 
  getRepositoryInfo,
  GitHubUtils 
} from './github-api'
import { OperationResult, executeWithResilience } from './executeCommand'

// === COMMITS OPERATIONS ===

export async function executeGitHubCommits(
  owner?: string,
  repo?: string,
  options: any = {}
): Promise<GitHubResponse> {
  const repoInfo = await GitHubUtils.resolveRepository(owner, repo)
  if (!repoInfo) {
    const fallback = await getRepositoryInfo()
    if (!fallback) throw new Error('Repository information not available')
    owner = fallback.owner
    repo = fallback.repo
  } else {
    owner = repoInfo.owner
    repo = repoInfo.repo
  }

  const queryParams = new URLSearchParams()
  if (options.sha) queryParams.append('sha', options.sha)
  if (options.path) queryParams.append('path', options.path)
  if (options.author) queryParams.append('author', options.author)
  if (options.since) queryParams.append('since', options.since)
  if (options.until) queryParams.append('until', options.until)
  if (options.per_page) queryParams.append('per_page', options.per_page.toString())
  if (options.page) queryParams.append('page', options.page.toString())

  const endpoint = `/repos/${owner}/${repo}/commits${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
  const { data, rateLimit } = await makeGitHubRequest(endpoint)

  return Object.freeze({
    success: true,
    data: Object.freeze(
      data.map((commit: any) =>
        Object.freeze({
          sha: commit.sha,
          message: commit.commit.message,
          author: Object.freeze({
            name: commit.commit.author.name,
            email: commit.commit.author.email,
            date: commit.commit.author.date
          }),
          committer: Object.freeze({
            name: commit.commit.committer.name,
            email: commit.commit.committer.email,
            date: commit.commit.committer.date
          }),
          html_url: commit.html_url,
          stats: commit.stats
            ? Object.freeze({
                additions: commit.stats.additions,
                deletions: commit.stats.deletions,
                total: commit.stats.total
              })
            : null
        })
      )
    ),
    rateLimit
  })
}

// === TEMPLATE OPERATIONS ===

export async function executeGitHubTemplate(
  templateSource: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple'
): Promise<GitHubResult> {
  try {
    const result = await processMultiHandlerTemplate(templateSource, vars, engine)

    return Object.freeze({
      success: true,
      data: result.content,
      processingTime: 0,
      timestamp: Date.now()
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message
    })
  }
}

// === MAIN COMMAND HANDLER ===

export async function executeGitHubCommand(
  command: GitHubCommand
): Promise<OperationResult<GitHubResult>> {
  const config = createGitHubConfig()

  return await executeWithResilience(command, config, async cmd => {
    const { action, owner, repo, options = {} } = cmd

    switch (action) {
      case 'repo':
        const repoResult = await executeGitHubRepo(owner, repo)
        return Object.freeze({
          success: repoResult.success,
          data: repoResult.data,
          rateLimit: repoResult.rateLimit,
          processingTime: 0,
          timestamp: Date.now()
        })

      case 'issues':
        const issuesResult = await executeGitHubIssues(owner, repo, options)
        return Object.freeze({
          success: issuesResult.success,
          data: issuesResult.data,
          rateLimit: issuesResult.rateLimit,
          processingTime: 0,
          timestamp: Date.now()
        })

      case 'commits':
        const commitsResult = await executeGitHubCommits(owner, repo, options)
        return Object.freeze({
          success: commitsResult.success,
          data: commitsResult.data,
          rateLimit: commitsResult.rateLimit,
          processingTime: 0,
          timestamp: Date.now()
        })

      case 'template':
        if (!cmd.content) throw new Error('Template content is required for template action')
        return await executeGitHubTemplate(
          cmd.content,
          cmd.templateVars || {},
          cmd.templateEngine || 'simple'
        )

      default:
        throw new Error(`Unsupported GitHub action: ${action}`)
    }
  })
}

// === HANDLER FACTORY ===

export function githubHandler() {
  return {
    execute: async (input: unknown): Promise<GitHubResult> => {
      if (!validateGitHubCommand(input)) {
        return Object.freeze({
          success: false,
          error: 'Invalid GitHub command structure'
        })
      }

      const result = await executeGitHubCommand(input)
      return result.success
        ? result.data
        : Object.freeze({
            success: false,
            error: result.error
          })
    }
  }
}
