/**
 * Core Module - AI-Optimized Public API
 * Machine-only exports for AI consumption and mutation
 *
 * @notation P:core/index F:all CB:none I:all DB:none
 */

export type {
  AINotationSymbol,
  AINotationMetadata,
  AINotationResponse,
  HandlerConfig,
  OperationConfig,
  CircuitBreakerConfig,
  RetryPolicy,
  OperationResult,
  HandlerFunction,
  MetricsState,
  ActionState,
  PerformanceMetrics,
  ActionHandler,
  EnvironmentConfig,
  EnvironmentTemplate,
  BatchAnalysisOptions,
  FileAnalysisResult,
  BatchAnalysisResult,
  MethodInfo,
  CircuitBreakerInfo,
  InterfaceInfo,
  PropertyInfo,
  ImportInfo,
  ExportInfo,
  TypeScriptAnalysis,
  ChunkedFileOptions,
  ChunkedFileResult,
  EnhancedToolCommand,
  EnhancedToolResult,
  TransformationRecord,
  RefactorManifest,
  SymbolMove
} from './types'

export {
  ENV_DEVELOPMENT,
  ENV_PRODUCTION,
  AI_SYMBOLS,
  NOTATION_PREFIXES,
  METHOD_PATTERNS,
  CIRCUIT_BREAKER_PATTERNS,
  EXPORT_PATTERNS,
  INTERFACE_PATTERN,
  IMPORT_PATTERN,
  PARAMETER_PATTERN,
  PROPERTY_PATTERN,
  EXCLUDED_PATTERNS,
  KNOWN_HANDLERS,
  TOOL_REGISTRY,
  DEFAULT_CHUNK_SIZE,
  MAX_METRICS,
  PARAM_REGEX,
  DEFAULT_CIRCUIT_BREAKER,
  DEFAULT_RETRY_POLICY,
  MEMORY_OPERATIONS,
  FILE_OPERATIONS,
  GITHUB_OPERATIONS,
  DATABASE_OPERATIONS,
  MONITORING_OPERATIONS,
  SCHEMA_PATHS,
  ANTI_PATTERNS,
  TRANSFORMATION_VERSION,
  MANIFEST_SCHEMA_VERSION,
  SYMBOL_TYPES,
  NOTATION_SYMBOLS
} from './constants'

export * from './handlers'
export * from './tools'
export * from './notation'
export * from './antiPatterns'
export * from './schema'
export * from './report'
export * from './state'
