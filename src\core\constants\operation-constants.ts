/**
 * Operation Constants - Task Configurations and Operation Maps
 * @notation P:core/constants/operation-constants F:all CB:none I:all DB:operations
 */

import { TransformationType, RefinementType, TaskType, TaskConfig } from '../types'

export const PERFORMANCE_THRESHOLD = 0.8
export const ERROR_CONFIDENCE_DIVISOR = 5
export const SUCCESS_CONFIDENCE_DIVISOR = 10
export const SLOW_EXECUTION_MULTIPLIER = 2
export const MEMORY_THRESHOLD_MB = 100
export const HIGH_CONFIDENCE_THRESHOLD = 0.67

export const TASK_CONFIGS = new Map<TaskType, TaskConfig>([
  [
    TaskType.EXTRACT,
    {
      priority: 1,
      timeout: 30000,
      retryCount: 3,
      dependencies: [],
      resources: { memory: 50, cpu: 0.3 },
      validation: { required: true, schema: 'extract.schema.json' }
    }
  ],
  [
    TaskType.IMPLEMENT,
    {
      priority: 2,
      timeout: 60000,
      retryCount: 2,
      dependencies: [TaskType.EXTRACT],
      resources: { memory: 100, cpu: 0.6 },
      validation: { required: true, schema: 'implement.schema.json' }
    }
  ],
  [
    TaskType.TEST,
    {
      priority: 3,
      timeout: 45000,
      retryCount: 3,
      dependencies: [TaskType.IMPLEMENT],
      resources: { memory: 75, cpu: 0.4 },
      validation: { required: true, schema: 'test.schema.json' }
    }
  ],
  [
    TaskType.VALIDATE,
    {
      priority: 4,
      timeout: 30000,
      retryCount: 2,
      dependencies: [TaskType.TEST],
      resources: { memory: 50, cpu: 0.2 },
      validation: { required: true, schema: 'validate.schema.json' }
    }
  ]
])

export const REFINEMENT_CONFIGS = new Map([
  [
    TransformationType.ERROR_PATTERN,
    {
      confidence: 0.8,
      applicability: ['typescript', 'javascript'],
      transformations: [RefinementType.SYNTAX_FIX, RefinementType.TYPE_ANNOTATION]
    }
  ],
  [
    TransformationType.PERFORMANCE_OPTIMIZATION,
    {
      confidence: 0.7,
      applicability: ['typescript', 'javascript', 'sql'],
      transformations: [RefinementType.ALGORITHM_IMPROVEMENT, RefinementType.CACHING]
    }
  ]
])


export const TOOL_RELIABILITY_IMPROVEMENTS = Object.freeze({
  BA: 0.79, // 79% reduction in analysis steps
  TS: 0.9, // 90% reduction in compilation failures
  DB: 0.85, // 85% reduction in query errors
  ANTI: 0.92 // 92% reduction in false positives
} as const)

export const METRICS_CAPS = Object.freeze({
  EXECUTION_EFFICIENCY_GAIN: 0.6,
  ERROR_REDUCTION_RATE: 0.9,
  MEMORY_OPTIMIZATION: 0.4,
  CPU_UTILIZATION_IMPROVEMENT: 0.3
} as const)

export const TOOL_REGISTRY = {
  BA: 'src/core/tools/batchAnalyzer.ts',
  ANTI: 'src/core/antiPatterns/detector.ts',
  REPORT: 'src/core/report/generateManifest.ts',
  EXECUTE: 'src/core/handlers/executeCommand.ts',
  LAUNCH: 'src/runtime/launch.ts',
  VALIDATE_SCHEMA: 'src/core/schema/validateSchema.ts',
  BACKUP: 'src/core/state/dbBackup.ts',
  TEMPLATE: 'src/core/tools/templateProcessor.ts'
} as const

export const GITHUB_OPERATION_MAP = new Map([
  ['repo', 'executeGitHubRepo'],
  ['issues', 'executeGitHubIssues'],
  ['commits', 'executeGitHubCommits'],
  ['search', 'executeGitHubSearch'],
  ['user', 'executeGitHubUser'],
  ['organization', 'executeGitHubOrganization'],
  ['pull-requests', 'executeGitHubPullRequests'],
  ['branches', 'executeGitHubBranches'],
  ['tags', 'executeGitHubTags'],
  ['releases', 'executeGitHubReleases'],
  ['contents', 'executeGitHubContents'],
  ['collaborators', 'executeGitHubCollaborators'],
  ['webhooks', 'executeGitHubWebhooks'],
  ['actions', 'executeGitHubActions'],
  ['packages', 'executeGitHubPackages'],
  ['projects', 'executeGitHubProjects'],
  ['discussions', 'executeGitHubDiscussions'],
  ['gists', 'executeGitHubGists'],
  ['notifications', 'executeGitHubNotifications'],
  ['advanced-search', 'executeAdvancedSearch'],
  ['validate-token', 'executeValidateToken'],
  ['create-issue', 'executeCreateIssue'],
  ['update-issue', 'executeUpdateIssue'],
  ['close-issue', 'executeCloseIssue'],
  ['stage-files', 'executeStageFiles'],
  ['create-commit', 'executeCreateCommit']
])

export const SQL_TEMPLATES = Object.freeze({
  PERFORMANCE_BASELINES_TABLE: `
    CREATE TABLE IF NOT EXISTS performance_baselines (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      operation_type TEXT NOT NULL,
      avg_execution_time REAL NOT NULL,
      success_rate REAL NOT NULL,
      memory_usage_mb REAL NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`,
  
  CIRCUIT_BREAKER_EVENTS_TABLE: `
    CREATE TABLE IF NOT EXISTS circuit_breaker_events (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      handler_name TEXT NOT NULL,
      event_type TEXT NOT NULL,
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
      failure_count INTEGER DEFAULT 0,
      success_count INTEGER DEFAULT 0,
      state TEXT NOT NULL,
      metadata TEXT
    )`,
  
  SYSTEM_TRAITS_TABLE: `
    CREATE TABLE IF NOT EXISTS system_traits (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      trait_name TEXT NOT NULL UNIQUE,
      quality_score REAL NOT NULL,
      last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
      metadata TEXT
    )`
} as const)

export const HISTORICAL_DATA_QUERY = `SELECT
  AVG(CASE WHEN success = 1 THEN execution_time END) as avg_success_time,
  COUNT(CASE WHEN success = 1 THEN 1 END) as success_count,
  COUNT(CASE WHEN success = 0 THEN 1 END) as failure_count,
  AVG(memory_usage) as avg_memory_usage
FROM mcp_calls 
WHERE operation_type = ? AND timestamp > datetime('now', '-1 hour')`
