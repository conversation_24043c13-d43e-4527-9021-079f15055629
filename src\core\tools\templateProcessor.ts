/**
 * Template Processor - AI-Optimized Pure Functions
 * @notation P:core/tools/templateProcessor F:processMultiHandlerTemplate,processTemplate CB:processTemplate I:TemplateResult,TemplateOptions DB:templates
 */

/**
 * @I:TemplateOptions - Template processing options
 * @notation P:none F:none CB:none I:TemplateOptions DB:templates
 */
export type TemplateOptions = {
  readonly engine?: 'simple' | 'mustache'
  readonly vars?: Record<string, unknown>
  readonly preserveWhitespace?: boolean
  readonly strictMode?: boolean
}

/**
 * @I:TemplateResult - Template processing result
 * @notation P:none F:none CB:none I:TemplateResult DB:templates
 */
export type TemplateResult = {
  readonly content: string
  readonly data?: unknown
  readonly aiNotationMetadata?: {
    readonly parametersDetected: Record<string, readonly string[]>
    readonly toolsDetected: Record<string, readonly string[]>
    readonly workflowCapable: boolean
    readonly validationRequired: boolean
  }
  readonly metadata?: {
    readonly engine: string
    readonly vars: Record<string, unknown>
    readonly processingTime: number
  }
}

/**
 * F:processSimpleTemplate - Process template with simple variable substitution
 * @notation P:template,vars F:processSimpleTemplate CB:none I:string DB:none
 */
export const processSimpleTemplate = (
  template: string,
  vars: Record<string, unknown> = {}
): string => {
  let result = template

  for (const [key, value] of Object.entries(vars)) {
    const pattern = new RegExp(`{{\\s*${key}\\s*}}`, 'g')
    result = result.replace(pattern, String(value))
  }

  return result
}

/**
 * F:processMustacheTemplate - Process template with Mustache-like syntax
 * @notation P:template,vars F:processMustacheTemplate CB:none I:string DB:none
 */
export const processMustacheTemplate = (
  template: string,
  vars: Record<string, unknown> = {}
): string => {
  let result = template

  for (const [key, value] of Object.entries(vars)) {
    const pattern = new RegExp(`{{\\s*${key}\\s*}}`, 'g')
    result = result.replace(pattern, String(value))
  }

  result = result.replace(/{{#if\s+(\w+)}}(.*?){{\/if}}/gs, (match, varName, content) => {
    const varValue = vars[varName]
    return varValue ? content : ''
  })

  result = result.replace(/{{#each\s+(\w+)}}(.*?){{\/each}}/gs, (match, varName, content) => {
    const array = vars[varName]
    if (Array.isArray(array)) {
      return array
        .map(item => {
          let itemContent = content
          if (typeof item === 'object' && item !== null) {
            for (const [itemKey, itemValue] of Object.entries(item)) {
              const itemPattern = new RegExp(`{{\\s*${itemKey}\\s*}}`, 'g')
              itemContent = itemContent.replace(itemPattern, String(itemValue))
            }
          } else {
            itemContent = itemContent.replace(/{{\.}}/g, String(item))
          }
          return itemContent
        })
        .join('')
    }
    return ''
  })

  return result
}

/**
 * F:detectAINotation - Detect AI notation patterns in template
 * @notation P:template F:detectAINotation CB:none I:object DB:none
 */
export const detectAINotation = (template: string) => {
  const parametersDetected: Record<string, string[]> = {}
  const toolsDetected: Record<string, string[]> = {}

  const notationMatches = template.match(/@notation\s+([^@\n]+)/g) || []
  for (const match of notationMatches) {
    const parts = match.replace('@notation', '').trim().split(/\s+/)
    for (const part of parts) {
      if (part.includes(':')) {
        const [prefix, value] = part.split(':')
        if (!parametersDetected[prefix]) {
          parametersDetected[prefix] = []
        }
        parametersDetected[prefix].push(value)
      }
    }
  }

  const toolMatches = template.match(/\b(BA|TS|DB|ANTI|REPORT):[^\s]+/g) || []
  for (const match of toolMatches) {
    const [tool, target] = match.split(':')
    if (!toolsDetected[tool]) {
      toolsDetected[tool] = []
    }
    toolsDetected[tool].push(target)
  }

  return Object.freeze({
    parametersDetected: Object.freeze(
      Object.fromEntries(Object.entries(parametersDetected).map(([k, v]) => [k, Object.freeze(v)]))
    ),
    toolsDetected: Object.freeze(
      Object.fromEntries(Object.entries(toolsDetected).map(([k, v]) => [k, Object.freeze(v)]))
    ),
    workflowCapable: toolMatches.length > 0,
    validationRequired: template.includes('@validate') || template.includes('schema')
  })
}

/**
 * F:processTemplate - Process template with specified engine
 * @notation P:template,vars,engine F:processTemplate CB:processTemplate I:TemplateResult DB:templates
 */
export const processTemplate = (
  template: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple'
): TemplateResult => {
  const startTime = Date.now()

  let content: string
  switch (engine) {
    case 'mustache':
      content = processMustacheTemplate(template, vars)
      break
    case 'simple':
    default:
      content = processSimpleTemplate(template, vars)
      break
  }

  const aiNotationMetadata = detectAINotation(template)
  const processingTime = Date.now() - startTime

  return Object.freeze({
    content,
    data: content,
    aiNotationMetadata,
    metadata: Object.freeze({
      engine,
      vars: Object.freeze({ ...vars }),
      processingTime
    })
  })
}

/**
 * F:processMultiHandlerTemplate - Main template processing function for handlers
 * @notation P:template,vars,engine F:processMultiHandlerTemplate CB:processMultiHandlerTemplate I:TemplateResult DB:templates
 */
export const processMultiHandlerTemplate = async (
  template: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple'
): Promise<TemplateResult> => {
  try {
    return processTemplate(template, vars, engine)
  } catch {
    return Object.freeze({
      content: template,
      data: template,
      aiNotationMetadata: Object.freeze({
        parametersDetected: Object.freeze({}),
        toolsDetected: Object.freeze({}),
        workflowCapable: false,
        validationRequired: false
      }),
      metadata: Object.freeze({
        engine,
        vars: Object.freeze({ ...vars }),
        processingTime: 0
      })
    })
  }
}

/**
 * F:createTemplateProcessor - Create template processor with default options
 * @notation P:options F:createTemplateProcessor CB:none I:function DB:templates
 */
export const createTemplateProcessor = (options: TemplateOptions = {}) => {
  const defaultOptions = Object.freeze({
    engine: 'simple' as const,
    vars: Object.freeze({}),
    preserveWhitespace: false,
    strictMode: false,
    ...options
  })

  return {
    process: (template: string, vars?: Record<string, unknown>) => {
      return processTemplate(
        template,
        Object.freeze({ ...defaultOptions.vars, ...vars }),
        defaultOptions.engine
      )
    },
    processAsync: async (template: string, vars?: Record<string, unknown>) => {
      return processMultiHandlerTemplate(
        template,
        Object.freeze({ ...defaultOptions.vars, ...vars }),
        defaultOptions.engine
      )
    }
  }
}

/**
 * F:validateTemplate - Validate template syntax
 * @notation P:template,engine F:validateTemplate CB:none I:object DB:templates
 */
export const validateTemplate = (template: string, engine: 'simple' | 'mustache' = 'simple') => {
  const errors: string[] = []

  const openBraces = (template.match(/{{/g) || []).length
  const closeBraces = (template.match(/}}/g) || []).length
  if (openBraces !== closeBraces) {
    errors.push(`Unmatched braces: ${openBraces} opening, ${closeBraces} closing`)
  }

  if (engine === 'mustache') {
    const invalidPatterns = template.match(/{{#\w+}}.*?(?!{{\/\w+}})/gs)
    if (invalidPatterns) {
      errors.push('Unclosed conditional or loop blocks detected')
    }
  }

  return Object.freeze({
    valid: errors.length === 0,
    errors: Object.freeze(errors)
  })
}

/**
 * F:escapeTemplateString - Escape special characters in template string
 * @notation P:str F:escapeTemplateString CB:none I:string DB:none
 */
export const escapeTemplateString = (str: string): string => {
  return str.replace(/\\/g, '\\\\').replace(/{{/g, '\\{\\{').replace(/}}/g, '\\}\\}')
}

/**
 * F:unescapeTemplateString - Unescape special characters in template string
 * @notation P:str F:unescapeTemplateString CB:none I:string DB:none
 */
export const unescapeTemplateString = (str: string): string => {
  return str
    .replace(/\\{\\{/g, '{{')
    .replace(/\\}\\}/g, '}}')
    .replace(/\\\\/g, '\\')
}
