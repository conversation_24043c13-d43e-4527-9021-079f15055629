/**
 * Feedback Processor - Unified Export Module
 * Refactored from single 579-line file to 3 constraint-compliant files (≤150 lines each)
 *
 * @notation P:core/tools/agent/feedbackProcessor F:processFeedback,analyzeExecutionResults,createFeedbackSummary CB:processFeedback I:FeedbackResult,ContextTransformation DB:feedback
 */

// Re-export all functionality from split modules
export * from './feedback-core'
export * from './feedback-analysis'
export * from './feedback-processing'
 */
export const detectSuccessPatterns = (
  results: readonly ToolExecutionResult[]
): readonly PatternAnalysis[] => {
  const successfulResults = results.filter(r => r.success)
  const patterns: Map<string, PatternAnalysis> = new Map()

  for (const result of successfulResults) {
    const handlerAction = `${result.handler}:${result.action}`

    if (patterns.has(handlerAction)) {
      const existing = patterns.get(handlerAction)!
      patterns.set(
        handlerAction,
        Object.freeze({
          ...existing,
          frequency: existing.frequency + 1,
          examples: Object.freeze([...existing.examples, result.taskId].slice(0, 5))
        })
      )
    } else {
      patterns.set(
        handlerAction,
        Object.freeze({
          pattern: handlerAction,
          frequency: 1,
          confidence: 0.8,
          examples: Object.freeze([result.taskId]),
          applicability: Object.freeze([result.handler])
        })
      )
    }
  }

  return Object.freeze(Array.from(patterns.values()))
}

/**
 * F:detectErrorPatterns - Detect error patterns from execution results
 * @notation P:results F:detectErrorPatterns CB:none I:array DB:feedback
 */
export const detectErrorPatterns = (
  results: readonly ToolExecutionResult[]
): readonly PatternAnalysis[] => {
  const failedResults = results.filter(r => !r.success)
  const patterns: Map<string, PatternAnalysis> = new Map()

  for (const result of failedResults) {
    if (!result.error) continue

    const errorType = result.error.name || 'UnknownError'

    if (patterns.has(errorType)) {
      const existing = patterns.get(errorType)!
      patterns.set(
        errorType,
        Object.freeze({
          ...existing,
          frequency: existing.frequency + 1,
          examples: Object.freeze([...existing.examples, result.taskId].slice(0, 5))
        })
      )
    } else {
      patterns.set(
        errorType,
        Object.freeze({
          pattern: errorType,
          frequency: 1,
          confidence: 0.9,
          examples: Object.freeze([result.taskId]),
          applicability: Object.freeze([result.handler])
        })
      )
    }
  }

  return Object.freeze(Array.from(patterns.values()))
}

/**
 * F:analyzePerformanceMetrics - Analyze performance metrics from execution results
 * @notation P:results F:analyzePerformanceMetrics CB:none I:array DB:feedback
 */
export const analyzePerformanceMetrics = (
  results: readonly ToolExecutionResult[]
): readonly PerformanceInsight[] => {
  const insights: PerformanceInsight[] = []

  if (results.length === 0) {
    return Object.freeze([])
  }

  const thresholds = getPerformanceThresholds(results)
  const executionTimes = results.map(r => r.executionTime)
  const avgExecutionTime =
    executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length
  const slowTasks = results.filter(r => r.executionTime > thresholds.executionTime.slow)

  if (avgExecutionTime > thresholds.executionTime.slow) {
    insights.push(
      Object.freeze({
        metric: 'execution_time',
        trend: 'degrading',
        impact: 'high',
        recommendation: `Performance degraded: avg ${avgExecutionTime.toFixed(0)}ms vs baseline ${thresholds.executionTime.average.toFixed(0)}ms`
      })
    )
  } else if (avgExecutionTime < thresholds.executionTime.fast) {
    insights.push(
      Object.freeze({
        metric: 'execution_time',
        trend: 'improving',
        impact: 'medium',
        recommendation: `Performance improved: avg ${avgExecutionTime.toFixed(0)}ms vs baseline ${thresholds.executionTime.average.toFixed(0)}ms`
      })
    )
  } else {
    insights.push(
      Object.freeze({
        metric: 'execution_time',
        trend: 'stable',
        impact: 'low',
        recommendation: `Performance stable: avg ${avgExecutionTime.toFixed(0)}ms within baseline range`
      })
    )
  }

  const retryResults = results.filter(
    r => r.metadata?.retryAttempts && r.metadata.retryAttempts > 0
  )
  const retryRate = retryResults.length / results.length

  if (retryRate > 0.3) {
    insights.push(
      Object.freeze({
        metric: 'retry_rate',
        trend: 'degrading',
        impact: 'medium',
        recommendation: `High retry rate: ${(retryRate * 100).toFixed(1)}% of tasks required retries`
      })
    )
  }

  const successRate = results.filter(r => r.success).length / results.length
  if (successRate < thresholds.successRate.poor) {
    insights.push(
      Object.freeze({
        metric: 'success_rate',
        trend: 'degrading',
        impact: 'high',
        recommendation: `Low success rate: ${(successRate * 100).toFixed(1)}% vs baseline ${(thresholds.successRate.good * 100).toFixed(1)}%`
      })
    )
  } else if (successRate > thresholds.successRate.good) {
    insights.push(
      Object.freeze({
        metric: 'success_rate',
        trend: 'stable',
        impact: 'low',
        recommendation: `Excellent success rate: ${(successRate * 100).toFixed(1)}%, maintain current practices`
      })
    )
  }

  return Object.freeze(insights)
}

/**
 * F:generateContextTransformations - Generate context transformations from patterns
 * @notation P:successPatterns,errorPatterns,options F:generateContextTransformations CB:none I:array DB:feedback
 */
export const generateContextTransformations = (
  successPatterns: readonly PatternAnalysis[],
  errorPatterns: readonly PatternAnalysis[],
  options: FeedbackProcessingOptions = DEFAULT_FEEDBACK_OPTIONS
): readonly ContextTransformation[] => {
  const transformations: ContextTransformation[] = []

  for (const pattern of successPatterns) {
    if (pattern.confidence >= options.minConfidenceThreshold && pattern.frequency >= 2) {
      transformations.push(
        Object.freeze({
          id: `success-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          sourceTaskId: pattern.examples[0],
          transformationType: TransformationType.SUCCESS_PATTERN,
          insights: Object.freeze({
            pattern: pattern.pattern,
            confidence: pattern.confidence,
            applicability: pattern.applicability,
            recommendations: Object.freeze([
              `Apply ${pattern.pattern} pattern to similar tasks`,
              `Consider standardizing this approach for ${pattern.applicability.join(', ')}`
            ])
          }),
          createdAt: Date.now()
        })
      )
    }
  }

  for (const pattern of errorPatterns) {
    if (pattern.confidence >= options.minConfidenceThreshold && pattern.frequency >= 2) {
      transformations.push(
        Object.freeze({
          id: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          sourceTaskId: pattern.examples[0],
          transformationType: TransformationType.ERROR_PATTERN,
          insights: Object.freeze({
            pattern: pattern.pattern,
            confidence: pattern.confidence,
            applicability: pattern.applicability,
            recommendations: Object.freeze([
              `Implement error handling for ${pattern.pattern}`,
              `Add retry logic for ${pattern.applicability.join(', ')} operations`
            ])
          }),
          createdAt: Date.now()
        })
      )
    }
  }

  return Object.freeze(transformations.slice(0, options.maxTransformations))
}

/**
 * F:generateExecutionRefinements - Generate execution refinements from insights
 * @notation P:insights,planId F:generateExecutionRefinements CB:none I:array DB:feedback
 */
export const generateExecutionRefinements = (
  insights: readonly PerformanceInsight[],
  planId: string
): readonly ExecutionRefinement[] => {
  const refinements: ExecutionRefinement[] = []

  for (const insight of insights) {
    if (insight.impact === 'high' || insight.impact === 'medium') {
      let refinementType: RefinementType
      let expectedImprovement = {
        performanceGain: 0,
        reliabilityIncrease: 0,
        resourceEfficiency: 0
      }

      switch (insight.metric) {
        case 'execution_time':
          refinementType = RefinementType.PARALLELIZATION_OPTIMIZATION
          expectedImprovement = {
            performanceGain: 0.3,
            reliabilityIncrease: 0.1,
            resourceEfficiency: 0.2
          }
          break
        case 'retry_rate':
          refinementType = RefinementType.RETRY_POLICY_ADJUSTMENT
          expectedImprovement = {
            performanceGain: 0.1,
            reliabilityIncrease: 0.4,
            resourceEfficiency: 0.2
          }
          break
        case 'success_rate':
          refinementType = RefinementType.DEPENDENCY_MODIFICATION
          expectedImprovement = {
            performanceGain: 0.2,
            reliabilityIncrease: 0.5,
            resourceEfficiency: 0.1
          }
          break
        default:
          refinementType = RefinementType.RESOURCE_REALLOCATION
          expectedImprovement = {
            performanceGain: 0.1,
            reliabilityIncrease: 0.1,
            resourceEfficiency: 0.3
          }
      }

      refinements.push(
        Object.freeze({
          planId,
          refinementType,
          changes: Object.freeze({
            before: insight.metric,
            after: insight.recommendation,
            reasoning: `Based on ${insight.trend} trend in ${insight.metric}`
          }),
          expectedImprovement: Object.freeze(expectedImprovement),
          appliedAt: Date.now()
        })
      )
    }
  }

  return Object.freeze(refinements)
}

/**
 * F:processFeedback - Process execution feedback and generate insights
 * @notation P:results,planId,options F:processFeedback CB:processFeedback I:FeedbackResult DB:feedback
 */
export const processFeedback = (
  results: readonly ToolExecutionResult[],
  planId: string,
  options: FeedbackProcessingOptions = DEFAULT_FEEDBACK_OPTIONS
): FeedbackResult => {
  const startTime = Date.now()

  try {
    const transformations: ContextTransformation[] = []
    const refinements: ExecutionRefinement[] = []
    const insights: string[] = []
    const recommendations: string[] = []

    if (options.enablePatternDetection) {
      const successPatterns = detectSuccessPatterns(results)
      const errorPatterns = detectErrorPatterns(results)

      insights.push(
        `Detected ${successPatterns.length} success patterns and ${errorPatterns.length} error patterns`
      )

      if (options.enableContextTransformation) {
        const contextTransformations = generateContextTransformations(
          successPatterns,
          errorPatterns,
          options
        )
        transformations.push(...contextTransformations)
      }
    }

    if (options.enablePerformanceAnalysis) {
      const performanceInsights = analyzePerformanceMetrics(results)

      for (const insight of performanceInsights) {
        insights.push(`${insight.metric}: ${insight.trend} trend with ${insight.impact} impact`)
        recommendations.push(insight.recommendation)
      }

      const executionRefinements = generateExecutionRefinements(performanceInsights, planId)
      refinements.push(...executionRefinements)
    }

    if (results.length > 0) {
      const successRate = results.filter(r => r.success).length / results.length
      recommendations.push(`Overall success rate: ${(successRate * 100).toFixed(1)}%`)

      if (successRate < 0.9) {
        recommendations.push('Consider implementing additional error handling and retry mechanisms')
      }
    }

    return Object.freeze({
      success: true,
      transformations: Object.freeze(transformations),
      refinements: Object.freeze(refinements),
      insights: Object.freeze(insights),
      recommendations: Object.freeze(recommendations),
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      transformations: Object.freeze([]),
      refinements: Object.freeze([]),
      insights: Object.freeze([]),
      recommendations: Object.freeze([`Error processing feedback: ${(error as Error).message}`]),
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  }
}

/**
 * F:analyzeExecutionResults - Analyze execution results for patterns
 * @notation P:results F:analyzeExecutionResults CB:analyzeExecutionResults I:object DB:feedback
 */
export const analyzeExecutionResults = (results: readonly ToolExecutionResult[]) => {
  const successPatterns = detectSuccessPatterns(results)
  const errorPatterns = detectErrorPatterns(results)
  const performanceInsights = analyzePerformanceMetrics(results)

  return Object.freeze({
    successPatterns: Object.freeze(successPatterns),
    errorPatterns: Object.freeze(errorPatterns),
    performanceInsights: Object.freeze(performanceInsights),
    summary: Object.freeze({
      totalResults: results.length,
      successCount: results.filter(r => r.success).length,
      errorCount: results.filter(r => !r.success).length,
      avgExecutionTime: results.reduce((sum, r) => sum + r.executionTime, 0) / results.length
    })
  })
}

/**
 * F:createFeedbackSummary - Create feedback summary for logging
 * @notation P:feedbackResult F:createFeedbackSummary CB:none I:string DB:none
 */
export const createFeedbackSummary = (feedbackResult: FeedbackResult): string => {
  let summary = '\n📊 Feedback Processing Summary\n'
  summary += `${'='.repeat(50)}\n`
  summary += `✅ Success: ${feedbackResult.success}\n`
  summary += `🔄 Transformations: ${feedbackResult.transformations.length}\n`
  summary += `⚡ Refinements: ${feedbackResult.refinements.length}\n`
  summary += `💡 Insights: ${feedbackResult.insights.length}\n`
  summary += `📋 Recommendations: ${feedbackResult.recommendations.length}\n`
  summary += `⏱️  Processing Time: ${feedbackResult.processingTime}ms\n\n`

  if (feedbackResult.insights.length > 0) {
    summary += '🔍 Key Insights:\n'
    feedbackResult.insights.forEach(insight => {
      summary += `   • ${insight}\n`
    })
    summary += '\n'
  }

  if (feedbackResult.recommendations.length > 0) {
    summary += '💡 Recommendations:\n'
    feedbackResult.recommendations.forEach(rec => {
      summary += `   • ${rec}\n`
    })
  }

  return summary
}
