/**
 * GitHub Handler Core - Types and Configuration
 * Split from github.ts for constraint compliance (≤150 lines)
 *
 * @notation P:core/handlers/github-core F:createGitHubConfig,validateGitHubCommand CB:none I:GitHubCommand,GitHubResult DB:github
 */

import { GitHubCommand, GitHubResponse, GitHubRateLimit } from '../types'
import {
  GITHUB_API_CONFIG,
  GITHUB_RATE_LIMITS,
  GITHUB_ERROR_CODES,
  GITHUB_ERROR_MESSAGES
} from '../constants'
import {
  HandlerConfig,
  OperationResult,
  createHandlerConfig,
  executeWithResilience
} from './executeCommand'

// === TYPES ===

export type GitHubResult = {
  readonly success: boolean
  readonly data?: unknown
  readonly error?: string
  readonly rateLimit?: GitHubRateLimit
  readonly processingTime?: number
  readonly timestamp?: number
}

// === CONFIGURATION ===

/**
 * F:createGitHubConfig - Create GitHub handler configuration
 * @notation P:none F:createGitHubConfig CB:none I:HandlerConfig DB:none
 */
export const createGitHubConfig = (): HandlerConfig => {
  return createHandlerConfig(
    'github',
    [
      'repo',
      'issues',
      'commits',
      'branches',
      'pulls',
      'files',
      'search',
      'releases',
      'template'
    ],
    {
      failureThreshold: 5,
      recoveryTimeout: 60000,
      maxRetries: 3,
      baseDelay: 2000
    }
  )
}

// === VALIDATION ===

/**
 * F:validateGitHubCommand - Validate GitHub command structure
 * @notation P:command F:validateGitHubCommand CB:none I:boolean DB:none
 */
export const validateGitHubCommand = (command: unknown): command is GitHubCommand => {
  if (!command || typeof command !== 'object') return false

  const cmd = command as Record<string, unknown>

  return (
    typeof cmd.action === 'string' &&
    [
      'repo',
      'issues',
      'commits',
      'branches',
      'pulls',
      'files',
      'search',
      'releases',
      'template'
    ].includes(cmd.action) &&
    (cmd.owner === undefined || typeof cmd.owner === 'string') &&
    (cmd.repo === undefined || typeof cmd.repo === 'string')
  )
}

// === SECURITY AND RATE LIMITING ===

const ALLOWED_REPOSITORIES = process.env.GITHUB_ALLOWED_REPOS?.split(',') || []
const WORKSPACE_SECURITY_ENABLED = process.env.GITHUB_WORKSPACE_SECURITY === 'true'
let lastRateLimit: GitHubRateLimit | null = null

export function getGitHubToken(): string | null {
  const token = process.env.GITHUB_TOKEN || process.env.GH_TOKEN || null
  if (!token) throw new Error(GITHUB_ERROR_MESSAGES.TOKEN_NOT_FOUND)
  return token
}

export function checkRateLimit(): Readonly<{ allowed: boolean; waitTime?: number; message?: string }> {
  if (!lastRateLimit) return Object.freeze({ allowed: true })
  const now = Date.now() / 1000

  if (lastRateLimit.remaining <= 0 && now < lastRateLimit.resetTime) {
    const waitTime = Math.ceil(lastRateLimit.resetTime - now)
    return Object.freeze({
      allowed: false,
      waitTime,
      message: `Rate limit exceeded. Reset in ${waitTime} seconds.`
    })
  }

  return Object.freeze({ allowed: true })
}

export function validateRepositoryAccess(
  owner: string,
  repo: string
): Readonly<{ allowed: boolean; message?: string }> {
  if (!WORKSPACE_SECURITY_ENABLED) {
    return Object.freeze({ allowed: true })
  }

  const repoFullName = `${owner}/${repo}`
  if (ALLOWED_REPOSITORIES.length > 0 && !ALLOWED_REPOSITORIES.includes(repoFullName)) {
    return Object.freeze({
      allowed: false,
      message: `Repository ${repoFullName} is not in the allowed list`
    })
  }

  return Object.freeze({ allowed: true })
}

export function updateRateLimit(rateLimit: GitHubRateLimit): void {
  lastRateLimit = Object.freeze(rateLimit)
}

// === UTILITIES ===

export const GitHubUtils = Object.freeze({
  async resolveRepository(
    owner?: string,
    repo?: string
  ): Promise<{ owner: string; repo: string } | null> {
    if (owner && repo) {
      return { owner, repo }
    }

    // Try to resolve from git remote if in a git repository
    try {
      const { execSync } = await import('child_process')
      const remoteUrl = execSync('git remote get-url origin', { encoding: 'utf-8' }).trim()
      
      const match = remoteUrl.match(/github\.com[:/]([^/]+)\/([^/.]+)/)
      if (match) {
        return { owner: match[1], repo: match[2] }
      }
    } catch {
      // Ignore git errors
    }

    return null
  }
})
