/**
 * <PERSON>tch Handler - AI-Optimized Pure Functions
 * Migrated from BaseHandler class to pure function architecture
 *
 * @notation P:core/handlers/fetch F:fetchHand<PERSON>,executeFetchCommand CB:executeFetchCommand I:FetchCommand,FetchResult DB:fetch
 */

import * as https from 'https'
import * as http from 'http'
import { URL } from 'url'
import { processMultiHandlerTemplate } from '../tools'
import {
  HandlerConfig,
  OperationResult,
  createHandlerConfig,
  executeWithResilience
} from './executeCommand'

export type FetchCommand = {
  readonly action: 'fetch' | 'fetch_text' | 'fetch_json' | 'template'
  readonly url?: string
  readonly path?: string
  readonly content?: string
  readonly options?: {
    readonly method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
    readonly headers?: Record<string, string>
    readonly body?: string
    readonly timeout?: number
    readonly maxSize?: number
    readonly followRedirects?: boolean
    readonly templateEngine?: 'simple' | 'mustache'
    readonly templateVars?: Record<string, unknown>
    readonly templateOutputPath?: string
  }
}

export type FetchResult = {
  readonly success: boolean
  readonly data?: {
    readonly url: string
    readonly status: number
    readonly statusText: string
    readonly headers: Record<string, string>
    readonly content: string
    readonly contentType: string
    readonly size: number
  }
  readonly error?: string
  readonly timestamp: number
  readonly processingTime: number
}

/**
 * F:createFetchConfig - Create fetch handler configuration
 * @notation P:none F:createFetchConfig CB:none I:HandlerConfig DB:none
 */
export const createFetchConfig = (): HandlerConfig => {
  return createHandlerConfig('fetch', ['fetch', 'fetch_text', 'fetch_json', 'template'], {
    failureThreshold: 5,
    recoveryTimeout: 30000,
    maxRetries: 3,
    baseDelay: 1000
  })
}

/**
 * F:validateFetchCommand - Validate fetch command structure
 * @notation P:command F:validateFetchCommand CB:none I:boolean DB:none
 */
export const validateFetchCommand = (command: unknown): command is FetchCommand => {
  if (!command || typeof command !== 'object') return false

  const cmd = command as Record<string, unknown>

  return (
    typeof cmd.action === 'string' &&
    ['fetch', 'fetch_text', 'fetch_json', 'template'].includes(cmd.action)
  )
}

/**
 * F:executeFetchOperation - Execute HTTP fetch operation
 * @notation P:url,options F:executeFetchOperation CB:executeFetchOperation I:FetchResult DB:fetch
 */
export const executeFetchOperation = async (
  url: string,
  options: FetchCommand['options'] = {}
): Promise<FetchResult> => {
  const startTime = Date.now()

  try {
    if (!url || typeof url !== 'string') {
      throw new Error('Invalid URL: URL must be a non-empty string')
    }

    // @removed:URLValidator,SecurityLogger - Phase 11A.P.7 - security validation removed for core functionality

    const parsedUrl = new URL(url)
    if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
      throw new Error('Only HTTP and HTTPS URLs are supported')
    }

    const method = options?.method || 'GET'
    const timeout = options?.timeout || 10000
    const maxSize = options?.maxSize || 1024 * 1024 // 1MB default
    const followRedirects = options?.followRedirects !== false

    return new Promise((resolve, reject) => {
      const requestOptions = {
        method,
        headers: {
          'User-Agent': 'MCP-Fetch-Tool/1.0',
          ...options?.headers
        },
        timeout
      }

      const client = parsedUrl.protocol === 'https:' ? https : http

      const req = client.request(parsedUrl, requestOptions, res => {
        if (
          followRedirects &&
          res.statusCode &&
          res.statusCode >= 300 &&
          res.statusCode < 400 &&
          res.headers.location
        ) {
          const redirectUrl = new URL(res.headers.location, url).toString()
          return executeFetchOperation(redirectUrl, { ...options, followRedirects: false })
            .then(resolve)
            .catch(reject)
        }

        let data = ''
        let size = 0

        res.on('data', chunk => {
          size += chunk.length
          if (size > maxSize) {
            req.destroy()
            reject(new Error(`Response too large: ${size} bytes (max: ${maxSize})`))
            return
          }
          data += chunk
        })

        res.on('end', () => {
          const processingTime = Date.now() - startTime

          resolve(
            Object.freeze({
              success: true,
              data: Object.freeze({
                url,
                status: res.statusCode || 0,
                statusText: res.statusMessage || '',
                headers: res.headers as Record<string, string>,
                content: data,
                contentType: res.headers['content-type'] || 'text/plain',
                size
              }),
              timestamp: Date.now(),
              processingTime
            })
          )
        })
      })

      req.on('error', error => {
        const processingTime = Date.now() - startTime
        reject(
          Object.freeze({
            success: false,
            error: error.message,
            timestamp: Date.now(),
            processingTime
          })
        )
      })

      req.on('timeout', () => {
        req.destroy()
        const processingTime = Date.now() - startTime
        reject(
          Object.freeze({
            success: false,
            error: `Request timeout after ${timeout}ms`,
            timestamp: Date.now(),
            processingTime
          })
        )
      })

      if (options?.body && ['POST', 'PUT'].includes(method)) {
        req.write(options.body)
      }

      req.end()
    })
  } catch (error) {
    const processingTime = Date.now() - startTime
    return Object.freeze({
      success: false,
      error: (error as Error).message,
      timestamp: Date.now(),
      processingTime
    })
  }
}

/**
 * F:executeFetchText - Execute fetch with text content parsing
 * @notation P:url,options F:executeFetchText CB:executeFetchText I:FetchResult DB:fetch
 */
export const executeFetchText = async (
  url: string,
  options: FetchCommand['options'] = {}
): Promise<FetchResult> => {
  const result = await executeFetchOperation(url, options)

  if (result.success && result.data) {
    const updatedData = Object.freeze({
      ...result.data,
      content: result.data.content.toString()
    })

    return Object.freeze({
      ...result,
      data: updatedData
    })
  }

  return result
}

/**
 * F:executeFetchJson - Execute fetch with JSON parsing
 * @notation P:url,options F:executeFetchJson CB:executeFetchJson I:FetchResult DB:fetch
 */
export const executeFetchJson = async (
  url: string,
  options: FetchCommand['options'] = {}
): Promise<FetchResult> => {
  const result = await executeFetchOperation(url, options)

  if (result.success && result.data) {
    try {
      const jsonData = JSON.parse(result.data.content)
      const updatedData = Object.freeze({
        ...result.data,
        content: JSON.stringify(jsonData, null, 2),
        contentType: 'application/json'
      })

      return Object.freeze({
        ...result,
        data: updatedData
      })
    } catch (error) {
      return Object.freeze({
        success: false,
        error: `Invalid JSON response: ${(error as Error).message}`,
        timestamp: Date.now(),
        processingTime: result.processingTime
      })
    }
  }

  return result
}

/**
 * F:executeFetchTemplate - Execute fetch template operation
 * @notation P:templateSource,vars,engine F:executeFetchTemplate CB:executeFetchTemplate I:FetchResult DB:none
 */
export const executeFetchTemplate = async (
  templateSource: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple'
): Promise<FetchResult> => {
  try {
    const result = await processMultiHandlerTemplate(templateSource, vars, engine)

    return Object.freeze({
      success: true,
      data: Object.freeze({
        url: '',
        status: 200,
        statusText: 'OK',
        headers: {},
        content: result.content,
        contentType: 'text/plain',
        size: result.content.length
      }),
      timestamp: Date.now(),
      processingTime: 0
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message,
      timestamp: Date.now(),
      processingTime: 0
    })
  }
}

/**
 * F:executeFetchCommand - Execute fetch command with resilience
 * @notation P:command F:executeFetchCommand CB:executeFetchCommand I:OperationResult DB:fetch
 */
export const executeFetchCommand = async (
  command: FetchCommand
): Promise<OperationResult<FetchResult>> => {
  const config = createFetchConfig()

  return await executeWithResilience(command, config, async cmd => {
    const { action, url, options = {} } = cmd

    switch (action) {
      case 'fetch':
        if (!url) throw new Error('URL is required for fetch action')
        return await executeFetchOperation(url, options)

      case 'fetch_text':
        if (!url) throw new Error('URL is required for fetch_text action')
        return await executeFetchText(url, options)

      case 'fetch_json':
        if (!url) throw new Error('URL is required for fetch_json action')
        return await executeFetchJson(url, options)

      case 'template':
        const templateSource = cmd.content || cmd.path
        if (!templateSource)
          throw new Error('Either path or content must be provided for template action')
        return await executeFetchTemplate(
          templateSource,
          options?.templateVars || {},
          options?.templateEngine || 'simple'
        )

      default:
        throw new Error(`Unknown fetch action: ${action}`)
    }
  })
}

/**
 * F:fetchHandler - Create fetch handler function
 * @notation P:db F:fetchHandler CB:none I:function DB:fetch
 */
export const fetchHandler = () => {
  return {
    execute: async (input: unknown): Promise<FetchResult> => {
      if (!validateFetchCommand(input)) {
        return Object.freeze({
          success: false,
          error: 'Invalid fetch command structure',
          timestamp: Date.now(),
          processingTime: 0
        })
      }

      const result = await executeFetchCommand(input)
      return result.success
        ? result.data
        : Object.freeze({
            success: false,
            error: result.error,
            timestamp: Date.now(),
            processingTime: 0
          })
    }
  }
}
