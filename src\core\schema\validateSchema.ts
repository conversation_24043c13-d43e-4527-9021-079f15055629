/**
 * Universal Schema Validator - AI-Optimized Pure Functions
 * Centralized schema validation for all DB operations and MCP handlers
 * Eliminates schema validation gaps across 16 uncovered files
 *
 * @notation P:core/schema/validateSchema F:validateSchema,createValidator CB:validateSchema I:ValidationResult,SchemaConfig DB:validation
 */

import Ajv, { JSONSchemaType, ValidateFunction } from 'ajv'
import addFormats from 'ajv-formats'
import { readFileSync } from 'fs'
import { join } from 'path'

/**
 * @I:ValidationResult - Schema validation result
 * @notation P:none F:none CB:none I:ValidationResult DB:validation
 */
export type ValidationResult = {
  readonly valid: boolean
  readonly errors?: readonly string[]
  readonly data?: unknown
  readonly schema?: string
}

/**
 * @I:SchemaConfig - Schema configuration
 * @notation P:none F:none CB:none I:SchemaConfig DB:validation
 */
export type SchemaConfig = {
  readonly schemaPath: string
  readonly strict?: boolean
  readonly allErrors?: boolean
  readonly removeAdditional?: boolean | 'all' | 'failing'
}

/**
 * @I:ValidatorCache - Validator cache entry
 * @notation P:none F:none CB:none I:ValidatorCache DB:validation
 */
export type ValidatorCache = {
  readonly validator: ValidateFunction
  readonly schema: unknown
  readonly lastModified: number
}

const validatorCache = new Map<string, ValidatorCache>()
const ajvInstance = new Ajv({
  allErrors: true,
  strict: false,
  removeAdditional: true
})
addFormats(ajvInstance)

/**
 * F:loadSchema - Load schema from file system
 * @notation P:schemaPath F:loadSchema CB:loadSchema I:object DB:schemas
 */
export const loadSchema = (schemaPath: string): unknown => {
  try {
    const fullPath = schemaPath.startsWith('/')
      ? schemaPath
      : join(process.cwd(), 'src/core/schema', schemaPath)

    const schemaContent = readFileSync(fullPath, 'utf-8')
    return JSON.parse(schemaContent)
  } catch (error) {
    throw new Error(`Failed to load schema from ${schemaPath}: ${(error as Error).message}`)
  }
}

/**
 * F:createValidator - Create validator function for schema
 * @notation P:schema,config F:createValidator CB:createValidator I:ValidateFunction DB:validation
 */
export const createValidator = (
  schema: unknown,
  config: Partial<SchemaConfig> = {}
): ValidateFunction => {
  try {
    const ajv = new Ajv({
      allErrors: config.allErrors ?? true,
      strict: config.strict ?? false,
      removeAdditional: config.removeAdditional ?? true
    })
    addFormats(ajv)

    return ajv.compile(schema as JSONSchemaType<unknown>)
  } catch (error) {
    throw new Error(`Failed to create validator: ${(error as Error).message}`)
  }
}

/**
 * F:getCachedValidator - Get cached validator or create new one
 * @notation P:schemaPath,config F:getCachedValidator CB:getCachedValidator I:ValidateFunction DB:validation
 */
export const getCachedValidator = (
  schemaPath: string,
  config: Partial<SchemaConfig> = {}
): ValidateFunction => {
  const cacheKey = `${schemaPath}-${JSON.stringify(config)}`
  const cached = validatorCache.get(cacheKey)

  if (cached) {
    return cached.validator
  }

  const schema = loadSchema(schemaPath)
  const validator = createValidator(schema, config)

  validatorCache.set(
    cacheKey,
    Object.freeze({
      validator,
      schema,
      lastModified: Date.now()
    })
  )

  return validator
}

/**
 * F:validateSchema - Universal schema validation function
 * @notation P:data,schemaPath,config F:validateSchema CB:validateSchema I:ValidationResult DB:validation
 */
export const validateSchema = (
  data: unknown,
  schemaPath: string,
  config: Partial<SchemaConfig> = {}
): ValidationResult => {
  try {
    const validator = getCachedValidator(schemaPath, config)
    const valid = validator(data)

    if (valid) {
      return Object.freeze({
        valid: true,
        data,
        schema: schemaPath
      })
    }

    const errors = validator.errors?.map(
      err => `${err.instancePath || 'root'}: ${err.message}`
    ) || ['Unknown validation error']

    return Object.freeze({
      valid: false,
      errors: Object.freeze(errors),
      data,
      schema: schemaPath
    })
  } catch (error) {
    return Object.freeze({
      valid: false,
      errors: Object.freeze([(error as Error).message]),
      schema: schemaPath
    })
  }
}

/**
 * F:validateWithSchema - Validate data with inline schema
 * @notation P:data,schema,config F:validateWithSchema CB:validateWithSchema I:ValidationResult DB:validation
 */
export const validateWithSchema = (
  data: unknown,
  schema: unknown,
  config: Partial<SchemaConfig> = {}
): ValidationResult => {
  try {
    const validator = createValidator(schema, config)
    const valid = validator(data)

    if (valid) {
      return Object.freeze({
        valid: true,
        data,
        schema: 'inline'
      })
    }

    const errors = validator.errors?.map(
      err => `${err.instancePath || 'root'}: ${err.message}`
    ) || ['Unknown validation error']

    return Object.freeze({
      valid: false,
      errors: Object.freeze(errors),
      data,
      schema: 'inline'
    })
  } catch (error) {
    return Object.freeze({
      valid: false,
      errors: Object.freeze([(error as Error).message]),
      schema: 'inline'
    })
  }
}

/**
 * F:validateMemoryCommand - Validate memory command structure
 * @notation P:data F:validateMemoryCommand CB:validateMemoryCommand I:ValidationResult DB:memory
 */
export const validateMemoryCommand = (data: unknown): ValidationResult => {
  return validateSchema(data, 'memory.schema.json')
}

/**
 * F:validateFileCommand - Validate file command structure
 * @notation P:data F:validateFileCommand CB:validateFileCommand I:ValidationResult DB:files
 */
export const validateFileCommand = (data: unknown): ValidationResult => {
  return validateSchema(data, 'file.schema.json')
}

/**
 * F:validateGitHubCommand - Validate GitHub command structure
 * @notation P:data F:validateGitHubCommand CB:validateGitHubCommand I:ValidationResult DB:github
 */
export const validateGitHubCommand = (data: unknown): ValidationResult => {
  return validateSchema(data, 'github.schema.json')
}

/**
 * F:validateDatabaseCommand - Validate database command structure
 * @notation P:data F:validateDatabaseCommand CB:validateDatabaseCommand I:ValidationResult DB:database
 */
export const validateDatabaseCommand = (data: unknown): ValidationResult => {
  return validateSchema(data, 'database.schema.json')
}

/**
 * F:validateEnhancedToolCommand - Validate enhanced tool command structure
 * @notation P:data F:validateEnhancedToolCommand CB:validateEnhancedToolCommand I:ValidationResult DB:enhanced_tools
 */
export const validateEnhancedToolCommand = (data: unknown): ValidationResult => {
  return validateSchema(data, 'enhanced-tool.schema.json')
}

/**
 * F:validateCoordinationCommand - Validate coordination command structure
 * @notation P:data F:validateCoordinationCommand CB:validateCoordinationCommand I:ValidationResult DB:coordination
 */
export const validateCoordinationCommand = (data: unknown): ValidationResult => {
  return validateSchema(data, 'coordination.schema.json')
}

/**
 * F:validateMonitoringCommand - Validate monitoring command structure
 * @notation P:data F:validateMonitoringCommand CB:validateMonitoringCommand I:ValidationResult DB:monitoring
 */
export const validateMonitoringCommand = (data: unknown): ValidationResult => {
  return validateSchema(data, 'monitoring.schema.json')
}

/**
 * F:validateTerminalCommand - Validate terminal command structure
 * @notation P:data F:validateTerminalCommand CB:validateTerminalCommand I:ValidationResult DB:terminal
 */
export const validateTerminalCommand = (data: unknown): ValidationResult => {
  return validateSchema(data, 'terminal.schema.json')
}

/**
 * F:clearValidatorCache - Clear validator cache
 * @notation P:none F:clearValidatorCache CB:none I:void DB:validation
 */
export const clearValidatorCache = (): void => {
  validatorCache.clear()
}

/**
 * F:getValidatorCacheStats - Get validator cache statistics
 * @notation P:none F:getValidatorCacheStats CB:none I:object DB:validation
 */
export const getValidatorCacheStats = () => {
  return Object.freeze({
    size: validatorCache.size,
    keys: Object.freeze(Array.from(validatorCache.keys()))
  })
}

/**
 * F:validateAndThrow - Validate data and throw on error
 * @notation P:data,schemaPath,config F:validateAndThrow CB:validateAndThrow I:void DB:validation
 */
export const validateAndThrow = (
  data: unknown,
  schemaPath: string,
  config: Partial<SchemaConfig> = {}
): void => {
  const result = validateSchema(data, schemaPath, config)
  if (!result.valid) {
    throw new Error(`Validation failed for ${schemaPath}: ${result.errors?.join(', ')}`)
  }
}

/**
 * F:createSchemaValidator - Create schema validator factory
 * @notation P:schemaPath,config F:createSchemaValidator CB:createSchemaValidator I:function DB:validation
 */
export const createSchemaValidator = (schemaPath: string, config: Partial<SchemaConfig> = {}) => {
  return (data: unknown): ValidationResult => {
    return validateSchema(data, schemaPath, config)
  }
}
