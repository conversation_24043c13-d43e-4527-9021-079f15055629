{"schemaFiles": ["src/core/schema/coordination.schema.json", "src/core/schema/database.schema.json", "src/core/schema/enhanced-tool.schema.json", "src/core/schema/fetch.schema.json", "src/core/schema/file.schema.json", "src/core/schema/git.schema.json", "src/core/schema/github.schema.json", "src/core/schema/memory.schema.json", "src/core/schema/monitoring.schema.json", "src/core/schema/terminal.schema.json", "src/core/schema/time.schema.json"], "validateWith": "ajv", "onMigration": {"backupDbPath": "db/backup/augster.pre-migration.db", "postCheck": "src/core/tools/dbValidator.ts", "logTo": ".augment/logs/db-migration.log", "failOnInvalid": true, "symbolTagPrefix": "DB:"}}