/**
 * Handler Constants - Handler Configuration and Operations
 * @notation P:core/constants/handler-constants F:all CB:none I:all DB:handlers
 */

export const MEMORY_OPERATIONS = ['insert', 'query', 'update', 'delete', 'template'] as const

export const FILE_OPERATIONS = [
  'read',
  'write',
  'exists',
  'list',
  'delete',
  'copy',
  'move',
  'template'
] as const

export const GITHUB_OPERATIONS = [
  'repo',
  'issues',
  'commits',
  'search',
  'user',
  'organization',
  'pull-requests',
  'branches',
  'tags',
  'releases',
  'contents',
  'collaborators',
  'webhooks',
  'actions',
  'packages',
  'projects',
  'discussions',
  'gists',
  'notifications',
  'advanced-search',
  'validate-token',
  'create-issue',
  'update-issue',
  'close-issue',
  'stage-files',
  'create-commit',
  'template'
] as const

export const DATABASE_OPERATIONS = [
  'connect',
  'execute',
  'query',
  'schema',
  'backup',
  'migrate',
  'template'
] as const

export const MONITORING_OPERATIONS = ['health', 'metrics', 'dashboard'] as const

export const KNOWN_HANDLERS = Object.freeze([
  'memory',
  'file',
  'database',
  'github',
  'monitoring',
  'coordination',
  'fetch',
  'time',
  'git',
  'terminal',
  'enhancedTool'
])

export const DEFAULT_CIRCUIT_BREAKER = {
  failureThreshold: 5,
  recoveryTimeout: 30000,
  monitoringWindow: 60000
} as const

export const DEFAULT_RETRY_POLICY = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 30000,
  jitterPercent: 0.1
} as const

export const DEFAULT_HANDLER_OPTIONS = Object.freeze({
  failureThreshold: 5,
  recoveryTimeout: 30000,
  maxRetries: 3,
  baseDelay: 1000,
  monitoringWindow: 60000
} as const)

export const DEFAULT_RETRYABLE_ERRORS = Object.freeze([
  'SQLITE_BUSY',
  'SQLITE_LOCKED',
  'ECONNRESET',
  'ETIMEDOUT',
  'ENOTFOUND',
  'ECONNREFUSED'
] as const)

export const GITHUB_API_CONFIG = Object.freeze({
  BASE_URL: 'https://api.github.com',
  DEFAULT_PER_PAGE: 30,
  MAX_PER_PAGE: 100,
  DEFAULT_TIMEOUT: 30000,
  USER_AGENT: 'Augster-MCP-Client/1.0.0'
} as const)

export const GITHUB_RATE_LIMITS = Object.freeze({
  THRESHOLD: 100,
  RESET_BUFFER: 60000,
  CHECK_INTERVAL: 5000,
  BACKOFF_MULTIPLIER: 2,
  MAX_BACKOFF: 300000,
  BURST_LIMIT: 5000,
  HOURLY_LIMIT: 5000
} as const)

export const GITHUB_ERROR_CODES = Object.freeze({
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  RATE_LIMITED: 429,
  SERVER_ERROR: 500
} as const)

export const GITHUB_ERROR_MESSAGES = Object.freeze({
  TOKEN_NOT_FOUND: 'GitHub token not found. Set GITHUB_TOKEN or GH_TOKEN environment variable.',
  AUTH_FAILED: 'GitHub authentication failed. Check token validity.',
  RATE_LIMITED: 'GitHub API rate limit exceeded. Please wait before making more requests.',
  REPO_NOT_FOUND: 'Repository not found or access denied.',
  INVALID_REQUEST: 'Invalid GitHub API request format.',
  NETWORK_ERROR: 'Network error occurred while accessing GitHub API.',
  UNKNOWN_ERROR: 'An unknown error occurred with the GitHub API.'
} as const)

export const GITHUB_ACTIONS = Object.freeze({
  REPO: 'repo',
  ISSUES: 'issues',
  COMMITS: 'commits',
  SEARCH: 'search',
  USER: 'user',
  ORGANIZATION: 'organization',
  PULL_REQUESTS: 'pull-requests',
  BRANCHES: 'branches',
  TAGS: 'tags',
  RELEASES: 'releases',
  CONTENTS: 'contents',
  COLLABORATORS: 'collaborators',
  WEBHOOKS: 'webhooks',
  ACTIONS: 'actions',
  PACKAGES: 'packages',
  PROJECTS: 'projects',
  DISCUSSIONS: 'discussions',
  GISTS: 'gists',
  NOTIFICATIONS: 'notifications',
  ADVANCED_SEARCH: 'advanced-search',
  VALIDATE_TOKEN: 'validate-token',
  CREATE_ISSUE: 'create-issue',
  UPDATE_ISSUE: 'update-issue',
  CLOSE_ISSUE: 'close-issue',
  STAGE_FILES: 'stage-files',
  CREATE_COMMIT: 'create-commit'
} as const)

export const DATABASE_PATHS = Object.freeze({
  DB_PATH: '.augment/db/augster.db',
  BACKUP_DIR: '.augment/db/backups'
} as const)

export const MIGRATION_ERROR_CODES = Object.freeze({
  MIGRATION_FAILED: 'MIGRATION_FAILED',
  SQL_EXECUTION_ERROR: 'SQL_EXECUTION_ERROR',
  BACKUP_FAILED: 'BACKUP_FAILED',
  RESTORE_FAILED: 'RESTORE_FAILED',
  INTEGRITY_CHECK_FAILED: 'INTEGRITY_CHECK_FAILED'
} as const)

export const MIGRATION_WARNING_CODES = Object.freeze({
  DRY_RUN: 'DRY_RUN',
  BACKUP_SKIPPED: 'BACKUP_SKIPPED',
  ROLLBACK_AVAILABLE: 'ROLLBACK_AVAILABLE'
} as const)

export const SCHEMA_CHANGE_TYPES = Object.freeze({
  CREATE_TABLE: 'CREATE_TABLE' as const,
  ALTER_TABLE: 'ALTER_TABLE' as const,
  DROP_TABLE: 'DROP_TABLE' as const,
  CREATE_INDEX: 'CREATE_INDEX' as const,
  DROP_INDEX: 'DROP_INDEX' as const
} as const)

export const REQUIRED_TABLES = Object.freeze([
  'mcp_calls',
  'circuit_breaker_events',
  'system_traits'
] as const)

export const MINIMUM_RECORD_COUNT = 240
