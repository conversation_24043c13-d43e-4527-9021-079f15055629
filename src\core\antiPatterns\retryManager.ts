/**
 * Retry Manager - AI-Optimized Pure Functions
 * @notation P:core/antiPatterns/retryManager F:createRetryManager,executeWithRetry CB:executeWithRetry I:RetryConfig,RetryMetrics DB:retry
 */

export type RetryConfig = {
  readonly maxRetries: number
  readonly baseDelay: number
  readonly maxDelay: number
  readonly exponentialBase: number
  readonly jitterPercent: number
  readonly retryableErrors: readonly string[]
}

export type RetryMetrics = {
  readonly totalAttempts: number
  readonly successfulRetries: number
  readonly failedRetries: number
  readonly averageDelay: number
  readonly lastAttemptTime: number
}

export type RetryInstance = {
  readonly config: RetryConfig
  readonly metrics: RetryMetrics
}

/**
 * F:createDefaultRetryConfig - Create default retry configuration
 * @notation P:none F:createDefaultRetryConfig CB:none I:RetryConfig DB:none
 */
export const createDefaultRetryConfig = (): RetryConfig => {
  return Object.freeze({
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 30000,
    exponentialBase: 2,
    jitterPercent: 25,
    retryableErrors: Object.freeze([
      'ECONNRESET',
      'ECONNREFUSED',
      'ETIMEDOUT',
      'ENOTFOUND',
      'SQLITE_BUSY',
      'SQLITE_LOCKED',
      'CircuitBreakerError'
    ])
  })
}

/**
 * F:createRetryManager - Create new retry manager instance
 * @notation P:config F:createRetryManager CB:none I:RetryInstance DB:retry
 */
export const createRetryManager = (config: Partial<RetryConfig> = {}): RetryInstance => {
  const fullConfig = Object.freeze({
    ...createDefaultRetryConfig(),
    ...config
  })

  return Object.freeze({
    config: fullConfig,
    metrics: Object.freeze({
      totalAttempts: 0,
      successfulRetries: 0,
      failedRetries: 0,
      averageDelay: 0,
      lastAttemptTime: 0
    })
  })
}

/**
 * F:isRetryableError - Check if error is retryable
 * @notation P:error,config F:isRetryableError CB:none I:boolean DB:none
 */
export const isRetryableError = (error: Error, config: RetryConfig): boolean => {
  const errorType = error.name || error.constructor.name
  const errorMessage = error.message.toLowerCase()

  if (config.retryableErrors.includes(errorType)) {
    return true
  }

  if (
    errorMessage.includes('sqlite_busy') ||
    errorMessage.includes('database is locked') ||
    errorMessage.includes('database table is locked')
  ) {
    return true
  }

  if (
    errorMessage.includes('econnreset') ||
    errorMessage.includes('econnrefused') ||
    errorMessage.includes('etimedout') ||
    errorMessage.includes('enotfound') ||
    errorMessage.includes('network') ||
    errorMessage.includes('connection') ||
    errorMessage.includes('timeout')
  ) {
    return true
  }

  for (const retryableError of config.retryableErrors) {
    if (errorMessage.includes(retryableError.toLowerCase())) {
      return true
    }
  }

  return false
}

/**
 * F:calculateDelay - Calculate delay with exponential backoff and jitter
 * @notation P:attempt,config F:calculateDelay CB:none I:number DB:none
 */
export const calculateDelay = (attempt: number, config: RetryConfig): number => {
  const exponentialDelay = config.baseDelay * Math.pow(config.exponentialBase, attempt)

  const cappedDelay = Math.min(exponentialDelay, config.maxDelay)

  const jitter = calculateJitter(cappedDelay, config.jitterPercent)

  return Math.max(0, cappedDelay + jitter)
}

/**
 * F:calculateJitter - Calculate jitter for delay
 * @notation P:delay,jitterPercent F:calculateJitter CB:none I:number DB:none
 */
export const calculateJitter = (delay: number, jitterPercent: number): number => {
  const jitterRange = (delay * jitterPercent) / 100
  return (Math.random() - 0.5) * 2 * jitterRange
}

/**
 * F:sleep - Sleep for specified milliseconds
 * @notation P:ms F:sleep CB:none I:Promise DB:none
 */
export const sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * F:updateRetryMetrics - Update retry metrics
 * @notation P:metrics,attempt,success F:updateRetryMetrics CB:none I:RetryMetrics DB:retry
 */
export const updateRetryMetrics = (
  metrics: RetryMetrics,
  attempt: number,
  success: boolean
): RetryMetrics => {
  return Object.freeze({
    totalAttempts: metrics.totalAttempts + 1,
    successfulRetries:
      success && attempt > 0 ? metrics.successfulRetries + 1 : metrics.successfulRetries,
    failedRetries: !success ? metrics.failedRetries + 1 : metrics.failedRetries,
    averageDelay: metrics.averageDelay, // Could be calculated if needed
    lastAttemptTime: Date.now()
  })
}

/**
 * F:executeWithRetry - Execute operation with retry logic
 * @notation P:instance,operation,operationName F:executeWithRetry CB:executeWithRetry I:object DB:retry
 */
export const executeWithRetry = async <T>(
  instance: RetryInstance,
  operation: () => Promise<T>,
  operationName: string = 'unknown'
): Promise<{ result: T; updatedInstance: RetryInstance }> => {
  let lastError: Error
  let currentInstance = instance

  for (let attempt = 0; attempt <= instance.config.maxRetries; attempt++) {
    currentInstance = Object.freeze({
      ...currentInstance,
      metrics: updateRetryMetrics(currentInstance.metrics, attempt, false)
    })

    try {
      const result = await operation()

      if (attempt > 0) {
        currentInstance = Object.freeze({
          ...currentInstance,
          metrics: updateRetryMetrics(currentInstance.metrics, attempt, true)
        })
      }

      return { result, updatedInstance: currentInstance }
    } catch (error) {
      lastError = error as Error

      if (attempt === instance.config.maxRetries) {
        currentInstance = Object.freeze({
          ...currentInstance,
          metrics: updateRetryMetrics(currentInstance.metrics, attempt, false)
        })
        throw lastError
      }

      if (!isRetryableError(lastError, instance.config)) {
        throw lastError
      }

      const delay = calculateDelay(attempt, instance.config)
      await sleep(delay)
    }
  }

  // This should never be reached, but TypeScript requires it
  throw lastError!
}

/**
 * F:retryWithBackoff - Simple retry operation utility
 * @notation P:operation,config,operationName F:retryWithBackoff CB:retryWithBackoff I:Promise DB:retry
 */
export const retryWithBackoff = async <T>(
  operation: () => Promise<T>,
  config: Partial<RetryConfig> = {},
  operationName: string = 'operation'
): Promise<T> => {
  const retryManager = createRetryManager(config)
  const { result } = await executeWithRetry(retryManager, operation, operationName)
  return result
}

/**
 * F:createRetryManagerRegistry - Create retry manager registry
 * @notation P:none F:createRetryManagerRegistry CB:none I:Map DB:retry
 */
export const createRetryManagerRegistry = (): Map<string, RetryInstance> => {
  return new Map()
}

/**
 * F:getOrCreateRetryManager - Get or create retry manager in registry
 * @notation P:registry,name,config F:getOrCreateRetryManager CB:none I:RetryInstance DB:retry
 */
export const getOrCreateRetryManager = (
  registry: Map<string, RetryInstance>,
  name: string,
  config?: Partial<RetryConfig>
): RetryInstance => {
  if (!registry.has(name)) {
    registry.set(name, createRetryManager(config))
  }
  return registry.get(name)!
}

/**
 * F:getAllRetryMetrics - Get all retry metrics from registry
 * @notation P:registry F:getAllRetryMetrics CB:none I:object DB:none
 */
export const getAllRetryMetrics = (
  registry: Map<string, RetryInstance>
): Record<string, RetryMetrics> => {
  const metrics: Record<string, RetryMetrics> = {}
  for (const [name, instance] of registry) {
    metrics[name] = instance.metrics
  }
  return Object.freeze(metrics)
}

/**
 * F:resetAllRetryMetrics - Reset all retry metrics in registry
 * @notation P:registry F:resetAllRetryMetrics CB:none I:void DB:retry
 */
export const resetAllRetryMetrics = (registry: Map<string, RetryInstance>): void => {
  for (const [name, instance] of registry) {
    const resetInstance = Object.freeze({
      ...instance,
      metrics: Object.freeze({
        totalAttempts: 0,
        successfulRetries: 0,
        failedRetries: 0,
        averageDelay: 0,
        lastAttemptTime: 0
      })
    })
    registry.set(name, resetInstance)
  }
}
