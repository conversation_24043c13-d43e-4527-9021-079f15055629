/**
 * Feedback Processing - Main Processing Functions and Summary Generation
 * Split from feedbackProcessor.ts for constraint compliance (≤150 lines)
 *
 * @notation P:core/tools/agent/feedback-processing F:processFeedback,analyzeExecutionResults,createFeedbackSummary CB:processFeedback I:FeedbackResult DB:feedback
 */

import { ToolExecutionResult, ContextTransformation, ExecutionRefinement } from '../../types'
import { FeedbackResult, FeedbackProcessingOptions, DEFAULT_FEEDBACK_OPTIONS } from './feedback-core'
import { detectSuccessPatterns, detectErrorPatterns, analyzePerformanceMetrics, generateContextTransformations, generateExecutionRefinements } from './feedback-analysis'

/**
 * F:processFeedback - Process execution feedback and generate insights
 * @notation P:results,planId,options F:processFeedback CB:processFeedback I:FeedbackResult DB:feedback
 */
export const processFeedback = (
  results: readonly ToolExecutionResult[],
  planId: string,
  options: FeedbackProcessingOptions = DEFAULT_FEEDBACK_OPTIONS
): FeedbackResult => {
  const startTime = Date.now()

  try {
    const transformations: ContextTransformation[] = []
    const refinements: ExecutionRefinement[] = []
    const insights: string[] = []
    const recommendations: string[] = []

    if (options.enablePatternDetection) {
      const successPatterns = detectSuccessPatterns(results)
      const errorPatterns = detectErrorPatterns(results)

      insights.push(
        `Detected ${successPatterns.length} success patterns and ${errorPatterns.length} error patterns`
      )

      if (options.enableContextTransformation) {
        const contextTransformations = generateContextTransformations(
          successPatterns,
          errorPatterns,
          options
        )
        transformations.push(...contextTransformations)
      }
    }

    if (options.enablePerformanceAnalysis) {
      const performanceInsights = analyzePerformanceMetrics(results)

      for (const insight of performanceInsights) {
        insights.push(`${insight.metric}: ${insight.trend} trend with ${insight.impact} impact`)
        recommendations.push(insight.recommendation)
      }

      const executionRefinements = generateExecutionRefinements(performanceInsights, planId)
      refinements.push(...executionRefinements)
    }

    if (results.length > 0) {
      const successRate = results.filter(r => r.success).length / results.length
      recommendations.push(`Overall success rate: ${(successRate * 100).toFixed(1)}%`)

      if (successRate < 0.9) {
        recommendations.push('Consider implementing additional error handling and retry mechanisms')
      }
    }

    return Object.freeze({
      success: true,
      transformations: Object.freeze(transformations),
      refinements: Object.freeze(refinements),
      insights: Object.freeze(insights),
      recommendations: Object.freeze(recommendations),
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      transformations: Object.freeze([]),
      refinements: Object.freeze([]),
      insights: Object.freeze([]),
      recommendations: Object.freeze([`Error processing feedback: ${(error as Error).message}`]),
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  }
}

/**
 * F:analyzeExecutionResults - Analyze execution results for patterns
 * @notation P:results F:analyzeExecutionResults CB:analyzeExecutionResults I:object DB:feedback
 */
export const analyzeExecutionResults = (results: readonly ToolExecutionResult[]) => {
  const successPatterns = detectSuccessPatterns(results)
  const errorPatterns = detectErrorPatterns(results)
  const performanceInsights = analyzePerformanceMetrics(results)

  return Object.freeze({
    successPatterns: Object.freeze(successPatterns),
    errorPatterns: Object.freeze(errorPatterns),
    performanceInsights: Object.freeze(performanceInsights),
    summary: Object.freeze({
      totalResults: results.length,
      successCount: results.filter(r => r.success).length,
      errorCount: results.filter(r => !r.success).length,
      avgExecutionTime: results.reduce((sum, r) => sum + r.executionTime, 0) / results.length
    })
  })
}

/**
 * F:createFeedbackSummary - Create feedback summary for logging
 * @notation P:feedbackResult F:createFeedbackSummary CB:none I:string DB:none
 */
export const createFeedbackSummary = (feedbackResult: FeedbackResult): string => {
  let summary = '\n📊 Feedback Processing Summary\n'
  summary += `${'='.repeat(50)}\n`
  summary += `✅ Success: ${feedbackResult.success}\n`
  summary += `🔄 Transformations: ${feedbackResult.transformations.length}\n`
  summary += `⚡ Refinements: ${feedbackResult.refinements.length}\n`
  summary += `💡 Insights: ${feedbackResult.insights.length}\n`
  summary += `📋 Recommendations: ${feedbackResult.recommendations.length}\n`
  summary += `⏱️  Processing Time: ${feedbackResult.processingTime}ms\n\n`

  if (feedbackResult.insights.length > 0) {
    summary += '🔍 Key Insights:\n'
    feedbackResult.insights.forEach(insight => {
      summary += `   • ${insight}\n`
    })
    summary += '\n'
  }

  if (feedbackResult.recommendations.length > 0) {
    summary += '💡 Recommendations:\n'
    feedbackResult.recommendations.forEach(rec => {
      summary += `   • ${rec}\n`
    })
  }

  return summary
}
