/**
 * Runtime Server - Agent-Centric MCP Server
 * Fully orchestrated agent runtime, planner, and feedback loop with HTTP interface
 *
 * @notation P:runtime/server F:processInput,initializeSystem,handleError CB:processInput I:MCPCommand,AgentExecutionResult DB:agent
 */

import { createServer, IncomingMessage, ServerResponse } from 'http'
import { Database } from 'sqlite3'
import { buildRouter, type AgentExecutionResult } from './router'
import {
  createLoggingState,
  logMCPCall,
  flushLogs,
  type LoggingState
} from '../core/handlers/CentralLoggingDispatcher'

import {
  validateAgentModule,
  initializeAgentRuntime,
  registerRuntimeSymbols,
  getAgentModuleInfo,
  consumeContextualState,
  enforceDirectiveCompliance,
  createDefaultAgentConfig,
  createAgentState,
  updateHeartbeat,
  getAgentStatus
} from '../core/tools/agent'

import {
  createAgentTraceOutput,
  implementTraceFormat,
  validateTraceFormat
} from './traceFormatter'

const PORT = Number(process.env.PORT || 8082)

let db: Database
let router: (input: any) => Promise<AgentExecutionResult>
let loggingState: LoggingState
let agentInfo: ReturnType<typeof getAgentModuleInfo>
let contextualState: ReturnType<typeof consumeContextualState>
let directiveStatus: ReturnType<typeof enforceDirectiveCompliance>
let agentConfig = createDefaultAgentConfig()
let agentState = createAgentState(null, agentConfig)

/**
 * F:initializeSystem - Complete agent runtime bootstrapping
 */
const initializeSystem = async () => {
  console.log('🤖 Agent-Enhanced MCP Server Starting...')

  // Initialize trace format system
  const traceFormatImplemented = implementTraceFormat({ trace_format: 'TRACE_STATUS_SUMMARY' })
  if (!traceFormatImplemented) {
    throw new Error('Failed to implement trace format system')
  }

  const runtimeState = await initializeAgentRuntime(process.cwd())
  const symbols = await registerRuntimeSymbols(process.cwd())

  const validation = await validateAgentModule()
  if (!validation.valid) {
    throw new Error(
      `Module validation failed: ${validation.validations
        .filter(v => !v.valid)
        .map(v => v.error)
        .join(', ')}`
    )
  }

  agentInfo = getAgentModuleInfo()
  console.log(`📦 Agent Tools: ${agentInfo.name} v${agentInfo.version}`)
  console.log(`🏗️  Architecture: ${agentInfo.architecture}`)
  console.log(`🔧 Features: ${agentInfo.features.join(', ')}`)
  console.log(`✅ Modules Validated: ${validation.validations.length}`)

  db = new Database(process.env.MCP_DB_PATH || ':memory:')
  agentState = createAgentState(db, agentConfig)
  loggingState = createLoggingState(db)
  router = buildRouter(db)

  contextualState = consumeContextualState(process.cwd())
  directiveStatus = enforceDirectiveCompliance(contextualState, 'Agent-MCP Server Startup')

  if (!directiveStatus.success) {
    console.warn('⚠️ Directive Compliance Failed:', directiveStatus.error)
  } else {
    console.log('✅ Directive Compliance: All enforced')
  }

  agentState = updateHeartbeat(agentState)
}

/**
 * F:handleRequest - Main HTTP request handler
 */
const handleRequest = async (req: IncomingMessage, res: ServerResponse) => {
  try {
    if (req.method !== 'POST' && req.url !== '/status') {
      res.writeHead(404, { 'Content-Type': 'application/json' })
      res.end(JSON.stringify({ error: 'Not Found' }))
      return
    }

    if (req.method === 'GET' && req.url === '/status') {
      // Return agent status info
      const status = getAgentStatus(agentState)
      const result: AgentExecutionResult = {
        success: true,
        data: {
          status,
          info: agentInfo,
          directives: directiveStatus,
          context: {
            symbolicTrace: contextualState.symbolicTrace,
            manifestOps: contextualState.manifest?.transformations?.length || 0
          }
        },
        timestamp: Date.now(),
        processingTime: 0,
        agentMetadata: {
          sessionId: loggingState.sessionId,
          optimizations: ['runtime-integrated']
        }
      }
      res.writeHead(200, { 'Content-Type': 'application/json' })
      res.end(JSON.stringify(result))
      return
    }

    if (req.method === 'POST' && req.url === '/mcp') {
      // Read JSON body
      let body = ''
      for await (const chunk of req) {
        body += chunk
      }
      let input: any
      try {
        input = JSON.parse(body)
      } catch (jsonErr) {
        res.writeHead(400, { 'Content-Type': 'application/json' })
        res.end(JSON.stringify({ error: 'Invalid JSON' }))
        return
      }

      // Validate input
      if (!input || typeof input.command !== 'string') {
        res.writeHead(400, { 'Content-Type': 'application/json' })
        res.end(JSON.stringify({ error: 'Missing or invalid command property' }))
        return
      }

      // Special case: 'agent.status' command
      if (input.command === 'agent.status') {
        const status = getAgentStatus(agentState)
        const result: AgentExecutionResult = {
          success: true,
          data: {
            status,
            info: agentInfo,
            directives: directiveStatus,
            context: {
              symbolicTrace: contextualState.symbolicTrace,
              manifestOps: contextualState.manifest?.transformations?.length || 0
            }
          },
          timestamp: Date.now(),
          processingTime: 0,
          agentMetadata: {
            sessionId: loggingState.sessionId,
            optimizations: ['runtime-integrated']
          }
        }
        res.writeHead(200, { 'Content-Type': 'application/json' })
        res.end(JSON.stringify(result))
        return
      }

      // Route normal commands to router
      const start = Date.now()
      const result = await router(input)
      const processingTime = Date.now() - start

      // Log agent executions occasionally
      if (Math.random() < 0.1) {
        loggingState = await flushLogs(loggingState)
      }

      // Send response
      res.writeHead(200, { 'Content-Type': 'application/json' })
      res.end(JSON.stringify({ ...result, processingTime }))
      return
    }

    // If we reach here, method/url is unsupported
    res.writeHead(405, { 'Content-Type': 'application/json' })
    res.end(JSON.stringify({ error: 'Method Not Allowed' }))
  } catch (error) {
    const err = error as Error
    await logMCPCall(
      loggingState,
      {
        operation: 'mcp-server-error',
        result: { success: false, error: err.message },
        context: {}
      },
      {
        sessionId: loggingState.sessionId,
        toolName: 'mcp-server',
        operation: 'request-processing',
        performanceMetrics: {
          responseTime: 0,
          circuitBreakerState: 'closed',
          retryCount: 0
        },
        errorContext: {
          errorType: 'server-exception',
          errorMessage: err.message,
          recoveryAttempts: 0,
          recoverySuccess: false
        }
      }
    )
    res.writeHead(500, { 'Content-Type': 'application/json' })
    res.end(JSON.stringify({ error: err.message }))
  }
}

/**
 * Bootstraps the server
 */
async function main() {
  await initializeSystem()

  createServer(handleRequest).listen(PORT, () => {
    console.log(`🚀 Agent MCP Server HTTP API listening on http://localhost:${PORT}`)
  })
}

main().catch(err => {
  console.error('❌ Fatal error during server startup:', err)
  process.exit(1)
})
