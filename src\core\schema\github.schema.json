{"$schema": "http://json-schema.org/draft-07/schema#", "title": "GitHub Operations Schema", "description": "Validation schema for GitHub operations handler commands", "type": "object", "properties": {"action": {"type": "string", "enum": ["repo", "issues", "commits", "branches", "pulls", "files", "search", "create-pull", "update-pull", "merge-pull", "review-pull", "analyze-pr", "validate-token", "protect-branch", "advanced-search", "create-issue", "update-issue", "close-issue", "template", "stage-files", "create-commit", "push-commit"]}, "owner": {"type": "string", "description": "Repository owner"}, "repo": {"type": "string", "description": "Repository name"}, "path": {"type": "string", "description": "File path or template path"}, "content": {"type": "string", "description": "Template content for template action"}, "options": {"type": "object", "properties": {"state": {"type": "string", "enum": ["open", "closed", "all"], "default": "open"}, "labels": {"type": "string"}, "per_page": {"type": "integer", "minimum": 1, "maximum": 100, "default": 30}, "sha": {"type": "string"}, "protected": {"type": "boolean"}, "base": {"type": "string"}, "head": {"type": "string"}, "ref": {"type": "string"}, "q": {"type": "string"}, "type": {"type": "string", "enum": ["repositories", "code", "commits", "issues", "users"], "default": "repositories"}, "sort": {"type": "string"}, "order": {"type": "string", "enum": ["asc", "desc"], "default": "desc"}, "title": {"type": "string"}, "body": {"type": "string"}, "draft": {"type": "boolean", "default": false}, "issue_number": {"type": "integer"}, "state_reason": {"type": "string"}, "assignees": {"type": "array", "items": {"type": "string"}}, "milestone": {"type": "integer"}, "templateEngine": {"type": "string", "enum": ["simple", "mustache"], "default": "simple"}, "templateVars": {"type": "object", "description": "Template variables for substitution"}, "templateOutputPath": {"type": "string", "description": "Output path for processed template"}}}}, "required": ["action"], "additionalProperties": false}