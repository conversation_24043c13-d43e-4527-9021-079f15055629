/**
 * Unified Type Definitions - Consolidated Handler Types
 * @notation P:core/shared/unifiedTypes F:createHandlerConfig,createOperationResult CB:type-consolidation I:UnifiedHandlerConfig,UnifiedOperationResult,UnifiedOperationConfig DB:none
 */

/**
 * @I:UnifiedOperationConfig - Unified operation configuration
 * @notation P:none F:none CB:none I:UnifiedOperationConfig DB:handlers
 */
export type UnifiedOperationConfig = {
  readonly name: string
  readonly timeout?: number
  readonly retryable?: boolean
  readonly circuitBreakerKey?: string
  readonly circuitBreaker?: {
    readonly failureThreshold?: number
    readonly recoveryTimeout?: number
    readonly monitoringWindow?: number
  }
  readonly retryManager?: {
    readonly maxRetries?: number
    readonly baseDelay?: number
    readonly maxDelay?: number
    readonly jitterPercent?: number
    readonly retryableErrors?: readonly string[]
  }
}

/**
 * @I:UnifiedCircuitBreakerConfig - Unified circuit breaker configuration
 * @notation P:none F:none CB:none I:UnifiedCircuitBreakerConfig DB:handlers
 */
export type UnifiedCircuitBreakerConfig = {
  readonly key: string
  readonly failureThreshold: number
  readonly recoveryTimeout: number
  readonly monitoringWindow: number
}

/**
 * @I:UnifiedRetryPolicy - Unified retry policy configuration
 * @notation P:none F:none CB:none I:UnifiedRetryPolicy DB:handlers
 */
export type UnifiedRetryPolicy = {
  readonly operationName: string
  readonly maxRetries: number
  readonly baseDelay: number
  readonly maxDelay: number
  readonly jitterPercent?: number
  readonly retryableErrors: readonly string[]
}

/**
 * @I:UnifiedHandlerConfig - Unified handler configuration
 * @notation P:none F:none CB:none I:UnifiedHandlerConfig DB:handlers
 */
export type UnifiedHandlerConfig = {
  readonly name: string
  readonly operations: readonly string[] | readonly UnifiedOperationConfig[]
  readonly circuitBreakers?: readonly UnifiedCircuitBreakerConfig[]
  readonly retryPolicies?: readonly UnifiedRetryPolicy[]
  readonly resilience?: {
    readonly failureThreshold?: number
    readonly recoveryTimeout?: number
    readonly maxRetries?: number
    readonly baseDelay?: number
  }
}

/**
 * @I:UnifiedOperationResult - Unified operation result
 * @notation P:none F:none CB:none I:UnifiedOperationResult DB:handlers
 */
export type UnifiedOperationResult<TData = unknown> = {
  readonly success: boolean
  readonly data?: TData
  readonly error?: string
  readonly processingTime: number
  readonly timestamp: number
  readonly metadata?: Record<string, unknown>
}

/**
 * @I:UnifiedHandlerState - Unified handler state management
 * @notation P:none F:none CB:none I:UnifiedHandlerState DB:handlers
 */
export type UnifiedHandlerState = {
  readonly handlerName: string
  readonly operations: ReadonlyMap<string, UnifiedOperationConfig>
  readonly initialized: boolean
  readonly lastActivity?: number
}

/**
 * F:createUnifiedHandlerConfig - Create unified handler configuration
 * @notation P:name,operations,resilience F:createUnifiedHandlerConfig CB:none I:UnifiedHandlerConfig DB:none
 */
export const createUnifiedHandlerConfig = (
  name: string,
  operations: readonly string[],
  resilience?: {
    readonly failureThreshold?: number
    readonly recoveryTimeout?: number
    readonly maxRetries?: number
    readonly baseDelay?: number
  }
): UnifiedHandlerConfig => {
  return Object.freeze({
    name,
    operations,
    resilience: resilience ? Object.freeze(resilience) : undefined
  })
}

/**
 * F:createUnifiedOperationResult - Create unified operation result
 * @notation P:success,data,error,startTime F:createUnifiedOperationResult CB:none I:UnifiedOperationResult DB:none
 */
export const createUnifiedOperationResult = <TData = unknown>(
  success: boolean,
  data?: TData,
  error?: string,
  startTime?: number,
  metadata?: Record<string, unknown>
): UnifiedOperationResult<TData> => {
  const processingTime = startTime ? Date.now() - startTime : 0
  
  return Object.freeze({
    success,
    data,
    error,
    processingTime,
    timestamp: Date.now(),
    metadata: metadata ? Object.freeze(metadata) : undefined
  })
}

/**
 * F:createSuccessResult - Create unified success result
 * @notation P:data,startTime,metadata F:createSuccessResult CB:none I:UnifiedOperationResult DB:none
 */
export const createSuccessResult = <TData>(
  data: TData,
  startTime?: number,
  metadata?: Record<string, unknown>
): UnifiedOperationResult<TData> => {
  return createUnifiedOperationResult(true, data, undefined, startTime, metadata)
}

/**
 * F:createErrorResult - Create unified error result
 * @notation P:error,startTime,metadata F:createErrorResult CB:none I:UnifiedOperationResult DB:none
 */
export const createErrorResult = <TData = unknown>(
  error: string | Error,
  startTime?: number,
  metadata?: Record<string, unknown>
): UnifiedOperationResult<TData> => {
  const errorMessage = error instanceof Error ? error.message : error
  return createUnifiedOperationResult<TData>(false, undefined, errorMessage, startTime, metadata)
}

/**
 * F:createUnifiedOperationConfig - Create unified operation configuration
 * @notation P:name,options F:createUnifiedOperationConfig CB:none I:UnifiedOperationConfig DB:none
 */
export const createUnifiedOperationConfig = (
  name: string,
  options?: {
    readonly timeout?: number
    readonly retryable?: boolean
    readonly circuitBreakerKey?: string
    readonly failureThreshold?: number
    readonly recoveryTimeout?: number
    readonly maxRetries?: number
    readonly baseDelay?: number
  }
): UnifiedOperationConfig => {
  return Object.freeze({
    name,
    timeout: options?.timeout,
    retryable: options?.retryable,
    circuitBreakerKey: options?.circuitBreakerKey,
    circuitBreaker: options?.failureThreshold || options?.recoveryTimeout ? Object.freeze({
      failureThreshold: options.failureThreshold,
      recoveryTimeout: options.recoveryTimeout
    }) : undefined,
    retryManager: options?.maxRetries || options?.baseDelay ? Object.freeze({
      maxRetries: options.maxRetries,
      baseDelay: options.baseDelay
    }) : undefined
  })
}

/**
 * F:createMemoryHandlerConfig - Create memory handler configuration (unified)
 * @notation P:none F:createMemoryHandlerConfig CB:none I:UnifiedHandlerConfig DB:none
 */
export const createMemoryHandlerConfig = (): UnifiedHandlerConfig => {
  return createUnifiedHandlerConfig('memory', ['insert', 'query', 'update', 'delete', 'template'], {
    failureThreshold: 5,
    recoveryTimeout: 30000,
    maxRetries: 3,
    baseDelay: 1000
  })
}

/**
 * F:createFileHandlerConfig - Create file handler configuration (unified)
 * @notation P:none F:createFileHandlerConfig CB:none I:UnifiedHandlerConfig DB:none
 */
export const createFileHandlerConfig = (): UnifiedHandlerConfig => {
  return createUnifiedHandlerConfig('file', ['read', 'write', 'exists', 'list', 'search', 'template'], {
    failureThreshold: 5,
    recoveryTimeout: 30000,
    maxRetries: 3,
    baseDelay: 500
  })
}

/**
 * F:createDatabaseHandlerConfig - Create database handler configuration (unified)
 * @notation P:none F:createDatabaseHandlerConfig CB:none I:UnifiedHandlerConfig DB:none
 */
export const createDatabaseHandlerConfig = (): UnifiedHandlerConfig => {
  return createUnifiedHandlerConfig('database', ['connect', 'execute', 'query', 'schema', 'backup', 'migrate', 'template'], {
    failureThreshold: 3,
    recoveryTimeout: 60000,
    maxRetries: 2,
    baseDelay: 2000
  })
}

/**
 * F:createCoordinationHandlerConfig - Create coordination handler configuration (unified)
 * @notation P:none F:createCoordinationHandlerConfig CB:none I:UnifiedHandlerConfig DB:none
 */
export const createCoordinationHandlerConfig = (): UnifiedHandlerConfig => {
  return createUnifiedHandlerConfig('coordination', ['discover', 'register', 'status', 'template'], {
    failureThreshold: 5,
    recoveryTimeout: 30000,
    maxRetries: 3,
    baseDelay: 1000
  })
}

/**
 * F:createFetchHandlerConfig - Create fetch handler configuration (unified)
 * @notation P:none F:createFetchHandlerConfig CB:none I:UnifiedHandlerConfig DB:none
 */
export const createFetchHandlerConfig = (): UnifiedHandlerConfig => {
  return createUnifiedHandlerConfig('fetch', ['get', 'post', 'put', 'delete', 'template'], {
    failureThreshold: 3,
    recoveryTimeout: 30000,
    maxRetries: 2,
    baseDelay: 1000
  })
}
