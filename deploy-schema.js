/**
 * Simple Schema Deployment Script
 * Deploys unified-mcp-schema.sql to SQLite databases
 */

const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

// Load unified schema
const schemaPath = path.join(__dirname, 'src/core/schema/unified-mcp-schema.sql');
const schemaSql = fs.readFileSync(schemaPath, 'utf-8');

// Database paths
const databases = [
  '.augment/db/augster.db',
  '.augment/db/augster-dev.db'
];

async function deployToDatabase(dbPath, schemaSql) {
  return new Promise((resolve) => {
    const db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        resolve({ success: false, database: dbPath, error: err.message });
        return;
      }

      // Split schema into statements
      const statements = schemaSql
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      let completed = 0;
      let tablesCreated = 0;
      let hasError = false;

      if (statements.length === 0) {
        db.close();
        resolve({ success: true, database: dbPath, tablesCreated: 0 });
        return;
      }

      statements.forEach((statement, index) => {
        if (statement.toUpperCase().includes('CREATE TABLE') || 
            statement.toUpperCase().includes('INSERT')) {
          
          db.run(statement + ';', function(err) {
            completed++;
            
            if (err && !err.message.includes('already exists')) {
              if (!hasError) {
                hasError = true;
                db.close();
                resolve({ 
                  success: false, 
                  database: dbPath, 
                  error: err.message 
                });
              }
              return;
            }
            
            if (statement.toUpperCase().includes('CREATE TABLE')) {
              tablesCreated++;
            }
            
            if (completed === statements.length && !hasError) {
              db.close();
              resolve({ 
                success: true, 
                database: dbPath, 
                tablesCreated 
              });
            }
          });
        } else {
          completed++;
          if (completed === statements.length && !hasError) {
            db.close();
            resolve({ 
              success: true, 
              database: dbPath, 
              tablesCreated 
            });
          }
        }
      });
    });
  });
}

async function deployUnifiedSchema() {
  console.log('🚀 Starting unified schema deployment...');
  
  const results = [];
  
  for (const dbPath of databases) {
    console.log(`📦 Deploying to ${dbPath}...`);
    const result = await deployToDatabase(dbPath, schemaSql);
    results.push(result);
    
    if (result.success) {
      console.log(`✅ ${dbPath}: ${result.tablesCreated} tables created/updated`);
    } else {
      console.log(`❌ ${dbPath}: ${result.error}`);
    }
  }
  
  const allSuccess = results.every(r => r.success);
  console.log(`\n${allSuccess ? '✅' : '❌'} Schema deployment ${allSuccess ? 'completed successfully' : 'failed'}`);
  
  return results;
}

// Run deployment
deployUnifiedSchema()
  .then(results => {
    process.exit(results.every(r => r.success) ? 0 : 1);
  })
  .catch(err => {
    console.error('❌ Deployment failed:', err.message);
    process.exit(1);
  });
