/**
 * Shared Template Engine - Consolidated Template Processing for All Handlers
 * @notation P:core/shared/templateEngine F:executeHandlerTemplate,createTemplateResult CB:template-processing-shared I:HandlerTemplateResult,TemplateContext DB:none
 */

import { processMultiHandlerTemplate } from '../tools/templateProcessor'
import { createStandardErrorResult, createStandardSuccessResult } from './errorHandling'

export type HandlerTemplateResult<TData = unknown> = {
  readonly success: boolean
  readonly data?: TData
  readonly error?: string
  readonly templateGenerated?: boolean
  readonly templateVars?: Record<string, unknown>
  readonly aiNotationMetadata?: unknown
  readonly processingTime?: number
  readonly timestamp?: number
}

export type TemplateContext = {
  readonly handlerType: 'memory' | 'database' | 'coordination' | 'file' | 'fetch' | 'github' | 'monitoring' | 'terminal' | 'git' | 'time' | 'enhancedTool'
  readonly operation: string
  readonly startTime?: number
}

/**
 * F:createTemplateResult - Create standardized template result
 * @notation P:success,data,context,vars,metadata F:createTemplateResult CB:none I:HandlerTemplateResult DB:none
 */
export const createTemplateResult = <TData = unknown>(
  success: boolean,
  data?: TData,
  error?: string,
  context?: TemplateContext,
  vars?: Record<string, unknown>,
  metadata?: unknown
): HandlerTemplateResult<TData> => {
  const processingTime = context?.startTime ? Date.now() - context.startTime : 0
  
  return Object.freeze({
    success,
    data,
    error,
    templateGenerated: success,
    templateVars: vars,
    aiNotationMetadata: metadata,
    processingTime,
    timestamp: Date.now()
  })
}

/**
 * F:executeHandlerTemplate - Execute template operation for any handler
 * @notation P:templateSource,vars,engine,context F:executeHandlerTemplate CB:template-processing I:HandlerTemplateResult DB:none
 */
export const executeHandlerTemplate = async <TData = unknown>(
  templateSource: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple',
  context: TemplateContext
): Promise<HandlerTemplateResult<TData>> => {
  const startTime = Date.now()
  
  try {
    const result = await processMultiHandlerTemplate(templateSource, vars, engine)
    
    return createTemplateResult<TData>(
      true,
      result.data as TData,
      undefined,
      { ...context, startTime },
      vars,
      result.aiNotationMetadata
    )
  } catch (error) {
    return createTemplateResult<TData>(
      false,
      undefined,
      (error as Error).message,
      { ...context, startTime },
      vars
    )
  }
}

/**
 * F:executeMemoryTemplate - Execute memory template operation (consolidated)
 * @notation P:templateSource,vars,engine F:executeMemoryTemplate CB:executeMemoryTemplate I:HandlerTemplateResult DB:none
 */
export const executeMemoryTemplate = async (
  templateSource: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple'
): Promise<HandlerTemplateResult> => {
  return executeHandlerTemplate(templateSource, vars, engine, {
    handlerType: 'memory',
    operation: 'template'
  })
}

/**
 * F:executeDatabaseTemplate - Execute database template operation (consolidated)
 * @notation P:templateSource,vars,engine F:executeDatabaseTemplate CB:executeDatabaseTemplate I:HandlerTemplateResult DB:none
 */
export const executeDatabaseTemplate = async (
  templateSource: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple'
): Promise<HandlerTemplateResult> => {
  return executeHandlerTemplate(templateSource, vars, engine, {
    handlerType: 'database',
    operation: 'template'
  })
}

/**
 * F:executeCoordinationTemplate - Execute coordination template operation (consolidated)
 * @notation P:templateSource,vars,engine F:executeCoordinationTemplate CB:executeCoordinationTemplate I:HandlerTemplateResult DB:none
 */
export const executeCoordinationTemplate = async (
  templateSource: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple'
): Promise<HandlerTemplateResult> => {
  return executeHandlerTemplate(templateSource, vars, engine, {
    handlerType: 'coordination',
    operation: 'template'
  })
}

/**
 * F:executeFileTemplate - Execute file template operation (consolidated)
 * @notation P:templateSource,vars,engine F:executeFileTemplate CB:executeFileTemplate I:HandlerTemplateResult DB:none
 */
export const executeFileTemplate = async (
  templateSource: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple'
): Promise<HandlerTemplateResult> => {
  return executeHandlerTemplate(templateSource, vars, engine, {
    handlerType: 'file',
    operation: 'template'
  })
}

/**
 * F:executeFetchTemplate - Execute fetch template operation (consolidated)
 * @notation P:templateSource,vars,engine F:executeFetchTemplate CB:executeFetchTemplate I:HandlerTemplateResult DB:none
 */
export const executeFetchTemplate = async (
  templateSource: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple'
): Promise<HandlerTemplateResult> => {
  return executeHandlerTemplate(templateSource, vars, engine, {
    handlerType: 'fetch',
    operation: 'template'
  })
}

/**
 * F:executeGithubTemplate - Execute github template operation (consolidated)
 * @notation P:templateSource,vars,engine F:executeGithubTemplate CB:executeGithubTemplate I:HandlerTemplateResult DB:none
 */
export const executeGithubTemplate = async (
  templateSource: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple'
): Promise<HandlerTemplateResult> => {
  return executeHandlerTemplate(templateSource, vars, engine, {
    handlerType: 'github',
    operation: 'template'
  })
}

/**
 * F:executeMonitoringTemplate - Execute monitoring template operation (consolidated)
 * @notation P:templateSource,vars,engine F:executeMonitoringTemplate CB:executeMonitoringTemplate I:HandlerTemplateResult DB:none
 */
export const executeMonitoringTemplate = async (
  templateSource: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple'
): Promise<HandlerTemplateResult> => {
  return executeHandlerTemplate(templateSource, vars, engine, {
    handlerType: 'monitoring',
    operation: 'template'
  })
}

/**
 * F:executeTerminalTemplate - Execute terminal template operation (consolidated)
 * @notation P:templateSource,vars,engine F:executeTerminalTemplate CB:executeTerminalTemplate I:HandlerTemplateResult DB:none
 */
export const executeTerminalTemplate = async (
  templateSource: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple'
): Promise<HandlerTemplateResult> => {
  return executeHandlerTemplate(templateSource, vars, engine, {
    handlerType: 'terminal',
    operation: 'template'
  })
}

/**
 * F:executeEnhancedToolTemplate - Execute enhancedTool template operation (consolidated)
 * @notation P:templateSource,vars,engine F:executeEnhancedToolTemplate CB:executeEnhancedToolTemplate I:HandlerTemplateResult DB:none
 */
export const executeEnhancedToolTemplate = async (
  templateSource: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple'
): Promise<HandlerTemplateResult> => {
  return executeHandlerTemplate(templateSource, vars, engine, {
    handlerType: 'enhancedTool',
    operation: 'template'
  })
}
