/**
 * Pattern Constants - Regex Patterns and Detection Rules
 * @notation P:core/constants/pattern-constants F:all CB:none I:all DB:none
 */

export const METHOD_PATTERNS = Object.freeze([
  /^\s*(public|private|protected)?\s*(async\s+)?(\w+)\s*\([^)]*\)\s*:\s*([^{]+)\s*\{/,
  /^\s*(export\s+)?(async\s+)?function\s+(\w+)\s*\([^)]*\)\s*:\s*([^{]+)\s*\{/,
  /^\s*(export\s+)?const\s+(\w+)\s*=\s*\([^)]*\)\s*:\s*([^=]+)\s*=>/
] as const)

export const CIRCUIT_BREAKER_PATTERNS = Object.freeze([
  /CircuitBreaker\(['"`]([^'"`]+)['"`]/,
  /createCircuitBreaker\(['"`]([^'"`]+)['"`]/,
  /executeWithCircuitBreaker\(['"`]([^'"`]+)['"`]/,
  /CB:([a-zA-Z0-9_-]+)/
] as const)

export const EXPORT_PATTERNS = Object.freeze([
  { pattern: /^\s*export\s+(?:default\s+)?function\s+(\w+)/, type: 'function' as const },
  { pattern: /^\s*export\s+(?:default\s+)?class\s+(\w+)/, type: 'class' as const },
  { pattern: /^\s*export\s+(?:default\s+)?const\s+(\w+)/, type: 'const' as const },
  { pattern: /^\s*export\s+(?:default\s+)?interface\s+(\w+)/, type: 'interface' as const },
  { pattern: /^\s*export\s+(?:default\s+)?type\s+(\w+)/, type: 'type' as const }
] as const)

export const INTERFACE_PATTERN = /^\s*(?:export\s+)?interface\s+(\w+)(?:\s+extends\s+([^{]+))?\s*\{/
export const IMPORT_PATTERN_FULL =
  /^\s*import\s+(?:type\s+)?(?:\{([^}]+)\}|\*\s+as\s+\w+|(\w+))\s+from\s+['"`]([^'"`]+)['"`]/
export const PARAMETER_PATTERN = /\(([^)]*)\)/
export const PROPERTY_PATTERN = /^\s*(\w+)(\?)?\s*:\s*([^;,]+)/
export const HANDLER_CLASS_PATTERN = /class\s+(\w+)Handler/

export const ANTI_PATTERNS = Object.freeze({
  classes: /^\s*(?:export\s+)?(?:abstract\s+)?class\s+\w+/,
  defaultExports: /^\s*export\s+default\s+/,
  anyTypes: /:\s*any\b/,
  globalState: /^\s*(?:let|var)\s+\w+\s*[:=]/,
  thisKeyword: /\bthis\./,
  newKeyword: /\bnew\s+\w+/,
  mutations: /\w+\.\w+\s*=\s*/
} as const)

export const AI_NOTATION_SYMBOL_MAP = Object.freeze({
  '🔎': 'EXTRACT',
  '⚡': 'IMPLEMENT',
  '🧪': 'TEST',
  '✅': 'VALIDATE',
  '📝': 'DOCUMENT',
  '🔧': 'CONFIGURE',
  '🚀': 'DEPLOY',
  '🛑': 'STOP',
  '📊': 'MEASURE',
  '🔄': 'COORDINATE',
  '💾': 'COMPILE'
} as const)

export const AI_NOTATION_PARAMETER_PATTERNS = Object.freeze([
  { pattern: /P:([^\s,\]]+)/g, type: 'path' },
  { pattern: /M:([^\s,\]]+)/g, type: 'method' },
  { pattern: /CB:([^\s,\]]+)/g, type: 'circuitBreaker' },
  { pattern: /I:([^\s,\]]+)/g, type: 'interface' },
  { pattern: /S:([^\s,\]]+)/g, type: 'schema' },
  { pattern: /DB:([^\s,\]]+)/g, type: 'database' },
  { pattern: /F:([^\s,\]]+)/g, type: 'function' },
  { pattern: /L:([^\s,\]]+)/g, type: 'line' }
] as const)

export const AI_NOTATION_TOOL_PATTERNS = Object.freeze([
  { pattern: /BA:([^\s,\]]+)/g, tool: 'BatchAnalyzer' },
  { pattern: /TS:([^\s,\]]+)/g, tool: 'TypeScriptTool' },
  { pattern: /DB:([^\s,\]]+)/g, tool: 'DatabaseTool' },
  { pattern: /ANTI:([^\s,\]]+)/g, tool: 'AntiPatternDetector' }
] as const)

export const TOOL_NOTATION_PATTERNS = Object.freeze({
  TOOL_PREFIX: /^(BA|TS|DB):(.+)/,
  BATCH_ANALYSIS: /^BA:(.+)/,
  TYPESCRIPT_VALIDATION: /^TS:(.+)/,
  DATABASE_OPERATION: /^DB:(.+)/,
  ANTI_PATTERN_CHECK: /^ANTI:(.+)/
} as const)

export const TYPESCRIPT_FILE_EXTENSIONS = Object.freeze([
  '.ts',
  '.js',
  '.tsx',
  '.jsx',
  '.d.ts'
] as const)

export const VALIDATION_CATEGORIES = Object.freeze({
  SYNTAX: 'syntax' as const,
  TYPE: 'type' as const,
  SEMANTIC: 'semantic' as const,
  STYLE: 'style' as const,
  PERFORMANCE: 'performance' as const
} as const)

export const SUGGESTION_PATTERNS = Object.freeze([
  {
    pattern: /\.then\(/g,
    suggestion: 'Consider using async/await instead of .then()',
    category: 'style'
  },
  {
    pattern: /var\s+/g,
    suggestion: 'Use const or let instead of var',
    category: 'style'
  },
  {
    pattern: /==\s*(?!==)/g,
    suggestion: 'Use === instead of ==',
    category: 'style'
  }
] as const)

export const VALIDATION_CACHE_TTL = 30000

export const DIAGNOSTIC_CATEGORY_MAP = Object.freeze({
  [0]: 'warning', // ts.DiagnosticCategory.Warning
  [1]: 'error', // ts.DiagnosticCategory.Error
  [2]: 'suggestion', // ts.DiagnosticCategory.Suggestion
  [3]: 'message' // ts.DiagnosticCategory.Message
} as const)

export const DEFAULT_COMPILER_OPTIONS = Object.freeze({
  target: 'ES2020' as const,
  module: 'CommonJS' as const,
  lib: ['ES2020'],
  strict: true,
  esModuleInterop: true,
  skipLibCheck: true,
  forceConsistentCasingInFileNames: true,
  declaration: true,
  outDir: './dist',
  rootDir: './src'
} as const)

export const TYPESCRIPT_SCRIPT_TARGET = 'ES2020'
export const TYPESCRIPT_MODULE_KIND = 'CommonJS'
