/**
 * Deploy Unified Schema - AI-Optimized Pure Functions
 * @notation P:core/tools/deployUnifiedSchema F:deployUnifiedSchema,deployToDatabase CB:deployUnifiedSchema I:DeploymentResult DB:unified-schema
 */

import { readFileSync } from 'fs'
import { join } from 'path'
import { Database } from 'sqlite3'
import { executeDatabaseExecute } from '../handlers/database'

export type DeploymentResult = {
  readonly success: boolean
  readonly database: string
  readonly tablesCreated?: number
  readonly error?: string
  readonly timestamp: number
}

/**
 * F:loadUnifiedSchema - Load unified schema SQL content
 * @notation P:none F:loadUnifiedSchema CB:loadUnifiedSchema I:string DB:schema
 */
export const loadUnifiedSchema = (): string => {
  try {
    const schemaPath = join(process.cwd(), 'src/core/schema/unified-mcp-schema.sql')
    return readFileSync(schemaPath, 'utf-8')
  } catch (error) {
    throw new Error(`Failed to load unified schema: ${(error as Error).message}`)
  }
}

/**
 * F:deployToDatabase - Deploy unified schema to specific database
 * @notation P:dbPath,schemaSql F:deployToDatabase CB:deployToDatabase I:DeploymentResult DB:deployment
 */
export const deployToDatabase = async (
  dbPath: string,
  schemaSql: string
): Promise<DeploymentResult> => {
  try {
    // Split schema into individual statements
    const statements = schemaSql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))

    let tablesCreated = 0
    
    for (const statement of statements) {
      if (statement.toUpperCase().includes('CREATE TABLE')) {
        const result = await executeDatabaseExecute(dbPath, statement + ';')
        if (result.success) {
          tablesCreated++
        } else {
          // Ignore "table already exists" errors
          if (!result.error?.includes('already exists')) {
            return {
              success: false,
              database: dbPath,
              error: result.error,
              timestamp: Date.now()
            }
          }
        }
      } else if (statement.toUpperCase().includes('INSERT')) {
        // Execute INSERT statements (they use OR IGNORE)
        await executeDatabaseExecute(dbPath, statement + ';')
      }
    }

    return {
      success: true,
      database: dbPath,
      tablesCreated,
      timestamp: Date.now()
    }
  } catch (error) {
    return {
      success: false,
      database: dbPath,
      error: (error as Error).message,
      timestamp: Date.now()
    }
  }
}

/**
 * F:deployUnifiedSchema - Deploy unified schema to all MCP databases
 * @notation P:none F:deployUnifiedSchema CB:deployUnifiedSchema I:DeploymentResult[] DB:deployment
 */
export const deployUnifiedSchema = async (): Promise<DeploymentResult[]> => {
  const schemaSql = loadUnifiedSchema()
  const databases = [
    '.augment/db/augster.db',
    '.augment/db/augster-dev.db',
    '.augment/db/mcp.db'
  ]

  const results: DeploymentResult[] = []

  for (const dbPath of databases) {
    const result = await deployToDatabase(dbPath, schemaSql)
    results.push(result)
  }

  return results
}

/**
 * F:validateSchemaDeployment - Validate schema deployment success
 * @notation P:results F:validateSchemaDeployment CB:validateSchemaDeployment I:boolean DB:validation
 */
export const validateSchemaDeployment = (results: DeploymentResult[]): boolean => {
  return results.every(result => result.success)
}
