/**
 * Agent Engine - AI-Optimized Pure Functions
 * Migrated from src/agent/engine.ts to pure function architecture
 *
 * @notation P:core/tools/agent/engine F:executeAgentLoop,createAgentState CB:executeAgentLoop I:AgentState,AgentConfig DB:agent
 */

import { Database } from 'sqlite3'
import * as fs from 'fs'
import * as path from 'path'

export type ContextualState = {
  readonly manifest: any
  readonly feedback: any
  readonly executionPlan: any
  readonly metrics: any
  readonly symbolicTrace: Record<string, string>
  readonly timestamp: number
}

export type DirectiveEnforcementResult = {
  readonly success: boolean
  readonly contextConsumed: boolean
  readonly symbolicTraceActive: boolean
  readonly feedbackIntegrated: boolean
  readonly error?: string
}

export type AgentState = {
  readonly isRunning: boolean
  readonly startTime: number
  readonly heartbeatInterval: number
  readonly lastHeartbeat: number
  readonly database: Database | null
}

export type AgentConfig = {
  readonly heartbeatInterval: number
  readonly enableLogging: boolean
  readonly shutdownTimeout: number
}

export type AgentResult = {
  readonly success: boolean
  readonly state: AgentState
  readonly error?: string
  readonly timestamp: number
}

/**
 * F:createDefaultAgentConfig - Create default agent configuration
 * @notation P:none F:createDefaultAgentConfig CB:none I:AgentConfig DB:none
 */
export const createDefaultAgentConfig = (): AgentConfig => {
  return Object.freeze({
    heartbeatInterval: 30000, // 30 seconds
    enableLogging: true,
    shutdownTimeout: 5000 // 5 seconds
  })
}

/**
 * F:createAgentState - Create initial agent state
 * @notation P:database,config F:createAgentState CB:none I:AgentState DB:agent
 */
export const createAgentState = (
  database: Database | null = null,
  config: AgentConfig = createDefaultAgentConfig()
): AgentState => {
  return Object.freeze({
    isRunning: false,
    startTime: Date.now(),
    heartbeatInterval: config.heartbeatInterval,
    lastHeartbeat: 0,
    database
  })
}

/**
 * F:loadAgentDirectives - Load agent directives from file
 * @notation P:directivePath F:loadAgentDirectives CB:none I:string DB:none
 */
export const loadAgentDirectives = (directivePath: string): string => {
  try {
    if (fs.existsSync(directivePath)) {
      return fs.readFileSync(directivePath, 'utf-8')
    }
    return ''
  } catch (error) {
    console.warn(`⚠️ Failed to load directives from ${directivePath}:`, (error as Error).message)
    return ''
  }
}

type ManifestCache = {
  readonly manifest: unknown
  readonly symbolicTrace: Record<string, string>
  readonly lastModified: number
  readonly timestamp: number
}

let manifestCache: ManifestCache | null = null

/**
 * F:consumeContextualState - Consume all contextual state sources with caching optimization
 * @notation P:workspaceRoot F:consumeContextualState CB:consumeContextualState I:ContextualState DB:agent
 */
export const consumeContextualState = (workspaceRoot: string): ContextualState => {
  const manifestPath = path.join(workspaceRoot, '.augment', 'refactor-manifest.json')

  let manifest = null
  let symbolicTrace: Record<string, string> = {}

  try {
    if (fs.existsSync(manifestPath)) {
      const stats = fs.statSync(manifestPath)
      const lastModified = stats.mtime.getTime()

      if (manifestCache && manifestCache.lastModified === lastModified) {
        console.log('📊 CONTEXTUAL STATE: Using cached manifest (performance optimization)')
        manifest = manifestCache.manifest
        symbolicTrace = manifestCache.symbolicTrace
      } else {
        console.log('📊 CONTEXTUAL STATE: Loading fresh manifest')
        manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'))

        const latestOp = manifest.transformations?.[manifest.transformations.length - 1]
        if (latestOp?.symbolicMapping) {
          symbolicTrace = latestOp.symbolicMapping
        }

        manifestCache = Object.freeze({
          manifest,
          symbolicTrace,
          lastModified,
          timestamp: Date.now()
        })
      }
    }
  } catch (error) {
    console.warn('⚠️ Failed to consume manifest:', (error as Error).message)
  }

  return Object.freeze({
    manifest,
    feedback: null,
    executionPlan: null,
    metrics: null,
    symbolicTrace,
    timestamp: Date.now()
  })
}

/**
 * F:enforceDirectiveCompliance - Enforce directive compliance
 * @notation P:context,response F:enforceDirectiveCompliance CB:enforceDirectiveCompliance I:DirectiveEnforcementResult DB:agent
 */
export const enforceDirectiveCompliance = (
  context: ContextualState,
  response: string
): DirectiveEnforcementResult => {
  const hasSymbolicTrace = Object.keys(context.symbolicTrace).some(
    key => response.includes(`@${key}`) || response.includes(key)
  )

  const hasFeedbackReference = response.includes('feedback') || response.includes('processFeedback')
  const hasManifestReference =
    response.includes('manifest') || response.includes('refactor-manifest')
  const hasContextConsumption = response.includes('context') || response.includes('consumed')

  const contextConsumed = hasManifestReference && hasContextConsumption
  const symbolicTraceActive = hasSymbolicTrace
  const feedbackIntegrated = hasFeedbackReference

  const success = contextConsumed && symbolicTraceActive && feedbackIntegrated

  if (!success) {
    const missing = []
    if (!contextConsumed) missing.push('context consumption')
    if (!symbolicTraceActive) missing.push('symbolic trace')
    if (!feedbackIntegrated) missing.push('feedback integration')

    return Object.freeze({
      success: false,
      contextConsumed,
      symbolicTraceActive,
      feedbackIntegrated,
      error: `Directive violation: Missing ${missing.join(', ')}`
    })
  }

  return Object.freeze({
    success: true,
    contextConsumed,
    symbolicTraceActive,
    feedbackIntegrated
  })
}

/**
 * F:startAgentState - Start agent state
 * @notation P:state F:startAgentState CB:none I:AgentState DB:agent
 */
export const startAgentState = (state: AgentState): AgentState => {
  return Object.freeze({
    ...state,
    isRunning: true,
    startTime: Date.now(),
    lastHeartbeat: Date.now()
  })
}

/**
 * F:stopAgentState - Stop agent state
 * @notation P:state F:stopAgentState CB:none I:AgentState DB:agent
 */
export const stopAgentState = (state: AgentState): AgentState => {
  return Object.freeze({
    ...state,
    isRunning: false
  })
}

/**
 * F:updateHeartbeat - Update agent heartbeat
 * @notation P:state F:updateHeartbeat CB:none I:AgentState DB:agent
 */
export const updateHeartbeat = (state: AgentState): AgentState => {
  return Object.freeze({
    ...state,
    lastHeartbeat: Date.now()
  })
}

/**
 * F:logHeartbeat - Log agent heartbeat
 * @notation P:state,config F:logHeartbeat CB:none I:void DB:none
 */
export const logHeartbeat = (state: AgentState, config: AgentConfig): void => {
  if (config.enableLogging) {
    const uptime = Math.floor((Date.now() - state.startTime) / 1000)
    console.log(`💓 Agent heartbeat - Uptime: ${uptime}s`)
  }
}

/**
 * F:setupShutdownHandlers - Setup graceful shutdown handlers
 * @notation P:state,config,onShutdown F:setupShutdownHandlers CB:none I:void DB:agent
 */
export const setupShutdownHandlers = (
  state: AgentState,
  config: AgentConfig,
  onShutdown: (state: AgentState) => void
): void => {
  const handleShutdown = () => {
    console.log('🛑 Agent loop shutting down...')

    if (state.database) {
      state.database.close(err => {
        if (err) {
          console.error('❌ Error closing database:', err)
        } else {
          console.log('✅ Database connection closed')
        }
      })
    }

    onShutdown(stopAgentState(state))

    setTimeout(() => {
      process.exit(0)
    }, config.shutdownTimeout)
  }

  process.on('SIGINT', handleShutdown)
  process.on('SIGTERM', handleShutdown)
}

/**
 * F:executeAgentLoop - Execute agent loop with pure functions
 * @notation P:database,config F:executeAgentLoop CB:executeAgentLoop I:Promise DB:agent
 */
export const executeAgentLoop = async (
  database: Database,
  config: AgentConfig = createDefaultAgentConfig(),
  workspaceRoot: string = process.cwd()
): Promise<AgentResult> => {
  try {
    console.log('🤖 Agent loop started with database connection')

    const directives = loadAgentDirectives(
      path.join(workspaceRoot, '.augment', 'directive-runtime-selfloop.txt')
    )
    if (directives) {
      console.log('🧠 CONTEXTUAL REASONING DIRECTIVE LOADED')
    }

    const contextualState = consumeContextualState(workspaceRoot)
    console.log('📊 CONTEXTUAL STATE CONSUMED:')
    console.log('- Manifest operations:', contextualState.manifest?.transformations?.length || 0)
    console.log('- Symbolic trace entries:', Object.keys(contextualState.symbolicTrace).length)
    console.log(
      '- Latest symbolic mappings:',
      Object.keys(contextualState.symbolicTrace).join(', ')
    )

    const handlerRegistry = await initializeHandlerRegistry(database)
    console.log('🔧 HANDLER REGISTRY: Initialized for direct MCP execution')

    let agentState = createAgentState(database, config)
    agentState = startAgentState(agentState)

    console.log('✅ Agent engine initialized with contextual consumption')

    setupShutdownHandlers(agentState, config, finalState => {
      console.log('🔄 Agent state updated for shutdown')
    })

    const heartbeatTimer = setInterval(() => {
      agentState = updateHeartbeat(agentState)
      logHeartbeat(agentState, config)
    }, config.heartbeatInterval)

    const cleanup = () => {
      clearInterval(heartbeatTimer)
    }

    process.once('exit', cleanup)

    return Object.freeze({
      success: true,
      state: agentState,
      timestamp: Date.now()
    })
  } catch (error) {
    console.error('❌ Agent loop failed:', error)

    return Object.freeze({
      success: false,
      state: createAgentState(null, config),
      error: (error as Error).message,
      timestamp: Date.now()
    })
  }
}

/**
 * F:initializeHandlerRegistry - Initialize handler registry for direct MCP execution
 * @notation P:database F:initializeHandlerRegistry CB:initializeHandlerRegistry I:object DB:handlers
 */
const initializeHandlerRegistry = async (database: Database | null) => {
  console.log('🔧 INITIALIZING HANDLER REGISTRY: Loading all MCP handlers')

  try {
    const { memoryHandler } = await import('../../handlers/memory')
    const { fileHandler } = await import('../../handlers/file')
    const { databaseHandler } = await import('../../handlers/database')
    const { githubHandler } = await import('../../handlers/github')
    const { monitoringHandler } = await import('../../handlers/monitoring')
    const { coordinationHandler } = await import('../../handlers/coordination')
    const { fetchHandler } = await import('../../handlers/fetch')
    const { timeHandler } = await import('../../handlers/time')
    const { gitHandler } = await import('../../handlers/git')
    const { terminalHandler } = await import('../../handlers/terminal')
    const { enhancedToolHandler } = await import('../../handlers/enhancedTool')

    const registry = Object.freeze({
      memory: database ? memoryHandler(database) : null,
      file: database ? fileHandler(database) : null,
      database: databaseHandler(),
      github: githubHandler(),
      monitoring: monitoringHandler(),
      coordination: database ? coordinationHandler(database) : null,
      fetch: fetchHandler(),
      time: database ? timeHandler(database) : null,
      git: gitHandler(),
      terminal: terminalHandler(),
      enhancedTool: database ? enhancedToolHandler(database) : null
    })

    console.log('✅ HANDLER REGISTRY: All handlers initialized')
    console.log(
      '📦 HANDLER REGISTRY: Available handlers:',
      Object.keys(registry).filter(k => registry[k as keyof typeof registry] !== null)
    )

    return registry
  } catch (error) {
    console.error('❌ HANDLER REGISTRY: Failed to initialize handlers:', (error as Error).message)
    return Object.freeze({})
  }
}

/**
 * F:executeHandlerCommand - Execute command through handler registry
 * @notation P:handlerRegistry,command,payload F:executeHandlerCommand CB:executeHandlerCommand I:unknown DB:handlers
 */
export const executeHandlerCommand = async (
  handlerRegistry: any,
  command: string,
  payload: Record<string, unknown>
): Promise<unknown> => {
  const [handlerName, action] = command.split('.')

  console.log(`🔧 EXECUTING HANDLER COMMAND: ${handlerName}.${action}`)

  let registry = handlerRegistry
  if (!registry) {
    console.log('🔧 CREATING TEMPORARY HANDLER REGISTRY')
    registry = await initializeHandlerRegistry(null)
  }

  const handler = registry[handlerName]
  if (!handler) {
    throw new Error(`Handler not found: ${handlerName}`)
  }

  const result = await handler.execute({ action, ...payload })
  console.log(`✅ HANDLER COMMAND EXECUTED: ${command}`)

  return result
}

/**
 * F:getAgentUptime - Get agent uptime in seconds
 * @notation P:state F:getAgentUptime CB:none I:number DB:none
 */
export const getAgentUptime = (state: AgentState): number => {
  return Math.floor((Date.now() - state.startTime) / 1000)
}

/**
 * F:isAgentHealthy - Check if agent is healthy
 * @notation P:state,maxHeartbeatAge F:isAgentHealthy CB:none I:boolean DB:none
 */
export const isAgentHealthy = (state: AgentState, maxHeartbeatAge: number = 60000): boolean => {
  if (!state.isRunning) return false
  if (!state.database) return false

  const heartbeatAge = Date.now() - state.lastHeartbeat
  return heartbeatAge <= maxHeartbeatAge
}

/**
 * F:getAgentStatus - Get comprehensive agent status
 * @notation P:state F:getAgentStatus CB:none I:object DB:none
 */
export const getAgentStatus = (state: AgentState) => {
  return Object.freeze({
    isRunning: state.isRunning,
    uptime: getAgentUptime(state),
    lastHeartbeat: state.lastHeartbeat,
    isHealthy: isAgentHealthy(state),
    hasDatabase: state.database !== null,
    heartbeatInterval: state.heartbeatInterval
  })
}

/**
 * F:executeAgentLoopLegacy - Legacy compatibility wrapper
 * @notation P:db F:executeAgentLoopLegacy CB:executeAgentLoopLegacy I:Promise DB:agent
 */
export const executeAgentLoopLegacy = async (db: Database): Promise<void> => {
  const result = await executeAgentLoop(db)
  if (!result.success) {
    throw new Error(result.error || 'Agent loop failed')
  }
}

export { executeAgentLoopLegacy as default }
