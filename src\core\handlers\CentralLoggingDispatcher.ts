/**
 * Central Logging Dispatcher - AI-Optimized Pure Functions
 * @notation P:core/handlers/CentralLoggingDispatcher F:autoLog,logMCPCall CB:autoLog I:LogEntry,LogContext DB:logs
 */

import { Database } from 'sqlite3'

/**
 * @I:LogEntry - Log entry structure
 * @notation P:none F:none CB:none I:LogEntry DB:logs
 */
export type LogEntry = {
  readonly type: 'mcp_call' | 'trait' | 'tool_usage' | 'failure' | 'reflection' | 'error_recovery'
  readonly data: unknown
  readonly context: LogContext
}

/**
 * @I:LogContext - Log context information
 * @notation P:none F:none CB:none I:LogContext DB:logs
 */
export type LogContext = {
  readonly sessionId: string
  readonly toolName: string
  readonly operation: string
  readonly performanceMetrics?: {
    readonly responseTime: number
    readonly circuitBreakerState: string
    readonly retryCount: number
  }
  readonly errorContext?: {
    readonly errorType: string
    readonly errorMessage: string
    readonly recoveryAttempts: number
    readonly recoverySuccess: boolean
  }
}

/**
 * @I:MCPCallLogData - MCP call log data
 * @notation P:none F:none CB:none I:MCPCallLogData DB:logs
 */
export type MCPCallLogData = {
  readonly operation: string
  readonly result: unknown
  readonly context?: unknown
}

/**
 * @I:TraitLogData - Trait log data
 * @notation P:none F:none CB:none I:TraitLogData DB:traits
 */
export type TraitLogData = {
  readonly traitName: string
  readonly evidenceSnippet: string
  readonly qualityScore: number
}

/**
 * @I:FailureLogData - Failure log data
 * @notation P:none F:none CB:none I:FailureLogData DB:failures
 */
export type FailureLogData = {
  readonly errorType: string
  readonly triggerPhase: string
  readonly resolutionState: string
  readonly description: string
}

/**
 * @I:LoggingState - Logging state management
 * @notation P:none F:none CB:none I:LoggingState DB:logs
 */
export type LoggingState = {
  readonly sessionId: string
  readonly database: Database
  readonly logQueue: readonly LogEntry[]
  readonly isProcessing: boolean
  readonly batchSize: number
  readonly flushInterval: number
}

/**
 * F:createLoggingState - Create initial logging state
 * @notation P:database,sessionId F:createLoggingState CB:none I:LoggingState DB:logs
 */
export const createLoggingState = (database: Database, sessionId?: string): LoggingState => {
  return Object.freeze({
    sessionId: sessionId || generateSessionId(),
    database,
    logQueue: Object.freeze([]),
    isProcessing: false,
    batchSize: 10,
    flushInterval: 5000
  })
}

/**
 * F:generateSessionId - Generate unique session ID
 * @notation P:none F:generateSessionId CB:none I:string DB:none
 */
export const generateSessionId = (): string => {
  return `sess-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`
}

/**
 * F:addToLogQueue - Add entry to log queue
 * @notation P:state,entry F:addToLogQueue CB:none I:LoggingState DB:logs
 */
export const addToLogQueue = (state: LoggingState, entry: LogEntry): LoggingState => {
  return Object.freeze({
    ...state,
    logQueue: Object.freeze([...state.logQueue, entry])
  })
}

/**
 * F:clearLogQueue - Clear log queue
 * @notation P:state F:clearLogQueue CB:none I:LoggingState DB:logs
 */
export const clearLogQueue = (state: LoggingState): LoggingState => {
  return Object.freeze({
    ...state,
    logQueue: Object.freeze([])
  })
}

/**
 * F:setProcessingState - Set processing state
 * @notation P:state,isProcessing F:setProcessingState CB:none I:LoggingState DB:logs
 */
export const setProcessingState = (state: LoggingState, isProcessing: boolean): LoggingState => {
  return Object.freeze({
    ...state,
    isProcessing
  })
}

/**
 * F:autoLog - Auto-log based on entry type
 * @notation P:state,entry F:autoLog CB:autoLog I:Promise DB:logs
 */
export const autoLog = async (state: LoggingState, entry: LogEntry): Promise<LoggingState> => {
  const newState = addToLogQueue(state, entry)

  // Flush immediately if queue is full
  if (newState.logQueue.length >= newState.batchSize) {
    return await flushLogs(newState)
  }

  return newState
}

/**
 * F:logMCPCall - Log MCP call execution
 * @notation P:state,data,context F:logMCPCall CB:logMCPCall I:Promise DB:logs
 */
export const logMCPCall = async (
  state: LoggingState,
  data: MCPCallLogData,
  context: LogContext
): Promise<LoggingState> => {
  const entry: LogEntry = Object.freeze({
    type: 'mcp_call',
    data,
    context
  })
  return await autoLog(state, entry)
}

/**
 * F:logTrait - Log trait discovery
 * @notation P:state,data,context F:logTrait CB:logTrait I:Promise DB:traits
 */
export const logTrait = async (
  state: LoggingState,
  data: TraitLogData,
  context: LogContext
): Promise<LoggingState> => {
  const entry: LogEntry = Object.freeze({
    type: 'trait',
    data,
    context
  })
  return await autoLog(state, entry)
}

/**
 * F:logFailure - Log failure event
 * @notation P:state,data,context F:logFailure CB:logFailure I:Promise DB:failures
 */
export const logFailure = async (
  state: LoggingState,
  data: FailureLogData,
  context: LogContext
): Promise<LoggingState> => {
  const entry: LogEntry = Object.freeze({
    type: 'failure',
    data,
    context
  })
  return await autoLog(state, entry)
}

/**
 * F:flushLogs - Flush queued logs to database
 * @notation P:state F:flushLogs CB:flushLogs I:Promise DB:logs
 */
export const flushLogs = async (state: LoggingState): Promise<LoggingState> => {
  if (state.isProcessing || state.logQueue.length === 0) {
    return state
  }

  const processingState = setProcessingState(state, true)
  const logsToProcess = [...state.logQueue]
  const clearedState = clearLogQueue(processingState)

  try {
    await processBatch(state.database, logsToProcess)
    return setProcessingState(clearedState, false)
  } catch (error) {
    console.error('Failed to flush logs:', error)
    const reQueuedState = Object.freeze({
      ...clearedState,
      logQueue: Object.freeze([...logsToProcess, ...clearedState.logQueue])
    })
    return setProcessingState(reQueuedState, false)
  }
}

/**
 * F:createPromise - Factory function for creating promises (replaces new Promise())
 * @notation P:executor F:createPromise CB:none I:Promise DB:none
 */
export const createPromise = <T>(executor: (resolve: (value: T | PromiseLike<T>) => void, reject: (reason?: unknown) => void) => void): Promise<T> => {
  return new Promise(executor)
}

/**
 * F:createError - Factory function for creating errors (replaces new Error())
 * @notation P:message F:createError CB:none I:Error DB:none
 */
export const createError = (message: string): Error => {
  return new Error(message)
}

/**
 * F:createISOTimestamp - Factory function for creating ISO timestamps (replaces new Date())
 * @notation P:none F:createISOTimestamp CB:none I:string DB:none
 */
export const createISOTimestamp = (): string => {
  return new Date().toISOString()
}

/**
 * F:processBatch - Process a batch of log entries
 * @notation P:database,entries F:processBatch CB:processBatch I:Promise DB:logs
 */
export const processBatch = async (
  database: Database,
  entries: readonly LogEntry[]
): Promise<void> => {
  return createPromise<void>((resolve, reject) => {
    database.serialize(() => {
      database.run('BEGIN TRANSACTION')

      let completed = 0
      let hasError = false
      let errorMessage = ''

      for (const entry of entries) {
        insertLogEntry(database, entry, err => {
          if (err && !hasError) {
            hasError = true
            errorMessage = err.message
          }

          completed++
          if (completed === entries.length) {
            if (hasError) {
              database.run('ROLLBACK', () => {
                reject(createError(errorMessage))
              })
            } else {
              database.run('COMMIT', () => {
                resolve()
              })
            }
          }
        })
      }
    })
  })
}

/**
 * F:insertLogEntry - Insert single log entry into database
 * @notation P:database,entry,callback F:insertLogEntry CB:insertLogEntry I:void DB:logs
 */
export const insertLogEntry = (
  database: Database,
  entry: LogEntry,
  callback: (err: Error | null) => void
): void => {
  const timestamp = createISOTimestamp()

  switch (entry.type) {
    case 'mcp_call':
      database.run(
        `INSERT INTO mcp_calls (session_id, tool_name, operation, result, context, timestamp) 
         VALUES (?, ?, ?, ?, ?, ?)`,
        [
          entry.context.sessionId,
          entry.context.toolName,
          entry.context.operation,
          JSON.stringify((entry.data as MCPCallLogData).result),
          JSON.stringify((entry.data as MCPCallLogData).context),
          timestamp
        ],
        callback
      )
      break

    case 'trait':
      const traitData = entry.data as TraitLogData
      database.run(
        `INSERT INTO traits (session_id, trait_name, evidence_snippet, quality_score, timestamp) 
         VALUES (?, ?, ?, ?, ?)`,
        [
          entry.context.sessionId,
          traitData.traitName,
          traitData.evidenceSnippet,
          traitData.qualityScore,
          timestamp
        ],
        callback
      )
      break

    case 'failure':
      const failureData = entry.data as FailureLogData
      database.run(
        `INSERT INTO failures (session_id, error_type, trigger_phase, resolution_state, description, timestamp) 
         VALUES (?, ?, ?, ?, ?, ?)`,
        [
          entry.context.sessionId,
          failureData.errorType,
          failureData.triggerPhase,
          failureData.resolutionState,
          failureData.description,
          timestamp
        ],
        callback
      )
      break

    default:
      callback(createError(`Unknown log entry type: ${entry.type}`))
  }
}
