♾️ AGENT DIRECTIVE: SELF-CONSUMING CONTEXTUAL REASONING ACTIVATION

🧠 OBJECTIVE: Enforce real-time feedback loop where the agent:

✅ Actively consumes output from:
   - core/tools/agent/feedbackProcessor.ts
   - core/tools/agent/executionPlanner.ts
   - .augment/refactor-manifest.json
   - resilienceMonitor.ts, circuitBreaker.ts, and symbolic logs
   - Runtime state from router.ts, server.ts, launch.ts

✅ Enriches each suggestion with:
   - @notation traces of recent execution paths
   - Historical refactor operations from the manifest
   - Performance and fallback results (CB / Retry / AI injection outcomes)
   - Task types, execution scores, plan strategies (TaskType, ExecutionPlan)

✅ Iteratively learns by:
   - Mapping past failures to improved routing or suggestion logic
   - Dynamically modifying planning strategies using real feedback
   - Auto-enhancing command prompts using templateParser.ts

🚨 NEW AGENT OPERATIONAL CONSTRAINTS (ACTIVE IMMEDIATELY):

1. All reasoning must reference symbolic entries from .augment/refactor-manifest.json
2. All suggestions must derive from validated execution trace data (CB, F, DB, P, I)
3. Feedback data from feedbackProcessor.ts must be *consumed and reflected* in response logic
4. No speculative answers — cite exact source files, function names, or runtime results

🔄 RUNTIME LOOP MUST BE ACTIVE IN ALL FUTURE RESPONSES:

```ts
const context = {
  manifest: await loadRefactorManifest(),
  feedback: await processFeedback(latestExecution),
  executionPlan: await createExecutionPlan(currentInput),
  metrics: await getRuntimeState(),
}
return generateSuggestion(context) // Must reflect ALL sources above
```

📊 CONSUMED CONTEXTUAL STATE (LATEST):
- Manifest transformations: 39 operations
- Latest agent operation: phase_12a2_agent_primary_runtime_correction
- Symbolic mappings: @P:runtime/router:AGENT_PRIMARY_ACTIVE, @F:executeAgentPrimaryCommand:NEW_PRIMARY_FUNCTION
- Agent tools: Agent Tools v1.0.0 with 4 features active
- Runtime state: AGENT-PRIMARY execution active, legacy fallback restricted
- Resilience system: Circuit breakers, retry managers, monitoring all FUNCTIONAL

🎯 ENFORCEMENT ACTIVE: All agent responses must reflect this consumed context.
