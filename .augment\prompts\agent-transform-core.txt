♾️ AI TRANSFORMATION REQUEST  
🧠 Priority: FULL-AUGMENT AGENT RESTRUCTURE — CONTEXT-AWARE | TOOLCHAIN-ACTIVATED  
🛠 Target: Modular, AI-notation-native architecture for Augment MCP Core  
📦 Scope: Transform existing project into the structured `core/` system below using only AST-driven modifications  
🔍 Method: Utilize Context Engine, Reasoning Tools, and ts-morph to safely migrate, rename, and rebind logic.

---

## 🧠 GLOBAL INTENT

Establish a fully AI-optimizable, tooling-compliant, and symbol-traceable codebase:
- Embed AI Notation (`P:`, `F:`, `CB:`, `I:`, `S:`, `DB:`) across all modules
- Enable multi-tool orchestration: `BA → TS → ANTI → DB → REPORT`
- Refactor all modules structurally using `ts-morph`
- Integrate with the Augment Context Engine for link tracing, rollback, and evolution

---

## 🔧 TRANSFORMATION RULES

1. ♻️ Reuse and relocate all viable modules with AST-safe transformations
2. ❌ Eliminate: `class`, `this`, `any`, global state, `default export`
3. ✅ Enforce: `readonly`, frozen config, named exports, function-first logic
4. 🧠 Normalize all logic under `P:/F:/CB:/I:/DB:` symbolic tracing
5. 🛡️ Inject docblock headers for all AI-tracked exports (F:, CB:, I:) with correct tags

---

## 📁 TARGET ARCHITECTURE

src/core/
├── handlers/         ← Stateless execution handlers  
├── tools/            ← BA, TS, DB, MIGRATE, TEMPLATE  
├── notation/         ← P:, F:, CB: parser, validator  
├── antiPatterns/     ← class detector, fix plans  
├── schema/           ← All AJV/Zod schemas  
├── report/           ← Metrics, manifest, undo  
├── state/            ← Config + memory-safe state  
├── constants.ts      ← Frozen global config  
├── types.ts          ← `readonly` types, symbols  
└── index.ts          ← Public export map

---

## 🔗 TOOLCHAIN ACTIVATION

Run tools in order:
1. 🔎 `BA:` → Extract all symbols (P:, F:, CB:, I:)
2. ✅ `TS:` → Validate post-transform syntax and contract consistency
3. 🧪 `ANTI:` → Detect/remove OOP and mutation
4. 💾 `DB:` → Schema validate and plan-safe DB transitions
5. ♾️ `CTX:` → Link AI context to transformed output
6. 📊 `REPORT:` → Build `refactor-manifest.json`, `symbol-index/`, and logs

---

## 🔄 CONTEXT MEMORY TRACKING

Every symbolic move or rename must be recorded in:
- `.augment/refactor-manifest.json`
- `.augment/symbol-index/<module>.symbol.json`
- `.augment/logs/refactor.log`

---

## 💾 DB-AWARE SAFETY

Use `.augment/db/migration-plan.json` to:
- Backup `.augment/db/augster.db` and `augster-dev.db`
- Validate all `core/schema/*.schema.json` with `ajv`
- Map `DB:` symbols to handler logic

---

## 🔒 EXECUTION SAFETY CONTRACT

- All file changes logged to `.augment/undo-transform.json`
- All symbolic updates recorded
- No speculative logic allowed
- No mutation of `.augment/` core once transform begins
- Abort on TS or schema validation failure

---

## ✅ COMPLETION CONDITIONS

✅ `.augment/tool-registry.json` is fully populated  
✅ `.augment/symbol-index/*.json` exists for all modules  
✅ `.augment/refactor-manifest.json` contains full move/rename log  
✅ All handlers converted to pure function-based config  
✅ All `class`, `any`, mutable state removed  
✅ AI notation applied and verified (`P:`, `F:`, `CB:`, `I:`, `DB:`)  
✅ MCP server remains launchable and type-valid

---

## 🚀 EXECUTE

Begin with:

```bash
augment agent --run .augment/agent-transform-core.txt --init-mode
