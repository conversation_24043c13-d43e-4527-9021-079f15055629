# ~/.bashrc: executed by bash(1) for non-login shells.

# If not running interactively, don't do anything
case $- in
    *i*) ;;
      *) return;;
esac

# History settings
HISTCONTROL=ignoreboth
HISTSIZE=1000
HISTFILESIZE=2000
shopt -s histappend
shopt -s checkwinsize

# Make less more friendly for non-text input files
[ -x /usr/bin/lesspipe ] && eval "$(SHELL=/bin/sh lesspipe)"

# Set variable identifying the chroot you work in
if [ -z "${debian_chroot:-}" ] && [ -r /etc/debian_chroot ]; then
    debian_chroot=$(cat /etc/debian_chroot)
fi

# Set a fancy prompt (non-color, unless we know we "want" color)
case "$TERM" in
    xterm-color|*-256color) color_prompt=yes;;
esac

if [ "$color_prompt" = yes ]; then
    PS1='${debian_chroot:+($debian_chroot)}\[\033[01;32m\]\u@\h\[\033[00m\]:\[\033[01;34m\]\w\[\033[00m\]\$ '
else
    PS1='${debian_chroot:+($debian_chroot)}\u@\h:\w\$ '
fi
unset color_prompt

# Enable color support of ls and also add handy aliases
if [ -x /usr/bin/dircolors ]; then
    test -r ~/.dircolors && eval "$(dircolors -b ~/.dircolors)" || eval "$(dircolors -b)"
    alias ls='ls --color=auto'
    alias grep='grep --color=auto'
    alias fgrep='fgrep --color=auto'
    alias egrep='egrep --color=auto'
fi

# Some more ls aliases
alias ll='ls -alF'
alias la='ls -A'
alias l='ls -CF'

# Add an "alert" alias for long running commands
alias alert='notify-send --urgency=low -i "$([ $? = 0 ] && echo terminal || echo error)" "$(history|tail -n1|sed -e '\''s/^\s*[0-9]\+\s*//;s/[;&|]\s*alert$//'\'')"'

# Enable programmable completion features
if ! shopt -oq posix; then
  if [ -f /usr/share/bash-completion/bash_completion ]; then
    . /usr/share/bash-completion/bash_completion
  elif [ -f /etc/bash_completion ]; then
    . /etc/bash_completion
  fi
fi

# =============================================================================
# AUGSTER MCP SYSTEM CONFIGURATION
# =============================================================================

# SSH Configuration for Augster MCP System
export SSH_KEY_PATH="$HOME/.ssh/id_ed25519"
export SSH_AUTH_SOCK="$SSH_AUTH_SOCK"
export SSH_AGENT_PID="$SSH_AGENT_PID"

# GitHub Integration (used by src/mcp/handlers/github.ts)
# TODO: Set your GitHub token here
export GITHUB_TOKEN="ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAICi4U8kDLvRdqQ1MsQaya6CSWAiHjEfvhOelckyGfE1q <EMAIL>"
export GH_TOKEN="ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAICi4U8kDLvRdqQ1MsQaya6CSWAiHjEfvhOelckyGfE1q <EMAIL>"

# Augster MCP Environment
export AUGSTER_WORKSPACE="/workspaces/augster_mvp_scaffold"
export MCP_PRODUCTION_PORT=8081
export MCP_DEVELOPMENT_PORT=8082

# Node.js and TypeScript
export NODE_ENV="development"
export NODE_PATH="$AUGSTER_WORKSPACE/node_modules"

# Augster MCP Aliases
alias augster-start='cd $AUGSTER_WORKSPACE && ./.augment/env/start.sh'
alias augster-dev='cd $AUGSTER_WORKSPACE && ./.augment/env/start.sh --dev'
alias augster-status='cd $AUGSTER_WORKSPACE && ./.augment/env/start.sh --status'
alias augster-logs='cd $AUGSTER_WORKSPACE && tail -f .augment/logs/startup-prod.log'

# Auto-start SSH agent and add key if not already running
if [ -z "$SSH_AUTH_SOCK" ]; then
    eval "$(ssh-agent -s)" > /dev/null 2>&1
    ssh-add ~/.ssh/id_ed25519 > /dev/null 2>&1
fi

# Auto-navigate to Augster workspace
if [ -d "$AUGSTER_WORKSPACE" ] && [ "$PWD" = "$HOME" ]; then
    cd "$AUGSTER_WORKSPACE"
fi

# Welcome message
echo "🚀 Augster MCP System Environment Loaded"
echo "📁 Workspace: $AUGSTER_WORKSPACE"
echo "🔑 SSH Key: $SSH_KEY_PATH"
echo ""
echo "Quick Commands:"
echo "  augster-start    - Start production server"
echo "  augster-dev      - Start development server"
echo "  augster-status   - Show server status"
echo "  augster-health   - Check system health"
echo ""
