{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Terminal Operations Schema", "description": "Validation schema for terminal operations handler commands", "type": "object", "properties": {"command": {"type": "string", "enum": ["terminal.execute", "terminal.which", "terminal.pwd", "terminal.env", "terminal.ps"]}, "action": {"type": "string", "enum": ["execute", "which", "pwd", "env", "ps", "template"]}, "path": {"type": "string", "description": "Template file path for template action"}, "content": {"type": "string", "description": "Template content for template action"}, "cmd": {"type": "string", "description": "Command to execute (for execute and which actions)", "maxLength": 1000, "pattern": "^[a-zA-Z0-9\\s\\-\\._/\\\\:=@\\[\\]\\(\\)\\{\\}\\+\\*\\?\\^\\$]+$"}, "args": {"type": "array", "description": "Command arguments array", "items": {"type": "string", "maxLength": 500}, "maxItems": 50}, "workingDirectory": {"type": "string", "description": "Working directory for command execution (relative to workspace root)", "maxLength": 500, "pattern": "^[a-zA-Z0-9\\-\\._/\\\\]+$"}, "options": {"type": "object", "properties": {"timeout": {"type": "number", "description": "Command timeout in milliseconds", "minimum": 1000, "maximum": 300000, "default": 30000}, "maxBuffer": {"type": "number", "description": "Maximum buffer size for command output", "minimum": 1024, "maximum": 10485760, "default": 1048576}, "env": {"type": "object", "description": "Additional environment variables", "patternProperties": {"^[A-Z_][A-Z0-9_]*$": {"type": "string", "maxLength": 1000}}, "additionalProperties": false, "maxProperties": 20}, "shell": {"type": "boolean", "description": "Whether to run command in shell", "default": true}, "encoding": {"type": "string", "enum": ["utf8", "buffer"], "description": "Output encoding", "default": "utf8"}, "templateEngine": {"type": "string", "enum": ["simple", "mustache"], "default": "simple"}, "templateVars": {"type": "object", "description": "Template variables for substitution"}, "templateOutputPath": {"type": "string", "description": "Output path for processed template"}}, "additionalProperties": false}}, "required": ["action"], "allOf": [{"if": {"properties": {"action": {"enum": ["execute", "which"]}}}, "then": {"required": ["cmd"], "properties": {"cmd": {"type": "string", "minLength": 1}}}}, {"if": {"properties": {"action": {"const": "execute"}}}, "then": {"properties": {"cmd": {"type": "string", "not": {"pattern": "(rm|rmdir|del|delete|format|fdisk|sudo|su|chmod|chown|passwd|kill|killall|pkill|shutdown|reboot|halt|dd|mount|umount|fsck|iptables|ufw|firewall-cmd|crontab|at|batch|nc|netcat|telnet|ssh|scp|rsync)"}}}}}, {"if": {"properties": {"action": {"const": "execute"}}}, "then": {"properties": {"cmd": {"type": "string", "not": {"pattern": "(&&|\\|\\||;|>|<|>>)"}}}}}], "additionalProperties": false}