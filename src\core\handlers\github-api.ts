/**
 * GitHub API Operations - Core API Functions
 * @notation P:core/handlers/github-api F:makeGitHubRequest,executeGitHubRepo,executeGitHubIssues CB:makeGitHubRequest I:GitHubResponse DB:github
 */

import { GitHubResponse } from '../types'
import {
  GITHUB_API_CONFIG,
  GITHUB_RATE_LIMITS,
  GITHUB_ERROR_CODES,
  GITHUB_ERROR_MESSAGES
} from '../constants'
import { 
  getGitHubToken, 
  checkRateLimit, 
  updateRateLimit, 
  validateRepositoryAccess,
  GitHubUtils 
} from './github-core'

// === API REQUEST HANDLING ===

export async function getRepositoryInfo(): Promise<Readonly<{ owner: string; repo: string }> | null> {
  try {
    const envOwner = process.env.GITHUB_OWNER
    const envRepo = process.env.GITHUB_REPO
    if (envOwner && envRepo) return Object.freeze({ owner: envOwner, repo: envRepo })
    return Object.freeze({ owner: 'cryptokairo', repo: 'augster_mvp_scaffold' })
  } catch {
    return null
  }
}

export async function makeGitHubRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
  const rateLimitCheck = checkRateLimit()
  if (!rateLimitCheck.allowed) throw new Error(rateLimitCheck.message || 'Rate limit exceeded')

  const token = getGitHubToken()
  const headers: Record<string, string> = {
    Accept: 'application/vnd.github.v3+json',
    'User-Agent': GITHUB_API_CONFIG.USER_AGENT,
    ...((options.headers as Record<string, string>) || {})
  }
  if (token) headers['Authorization'] = `token ${token}`

  const url = endpoint.startsWith('http') ? endpoint : `${GITHUB_API_CONFIG.BASE_URL}${endpoint}`
  const response = await fetch(url, { ...options, headers })

  const rateLimit = Object.freeze({
    remaining: parseInt(response.headers.get(GITHUB_RATE_LIMITS.HEADERS.REMAINING) || '0'),
    reset: parseInt(response.headers.get(GITHUB_RATE_LIMITS.HEADERS.RESET) || '0'),
    limit: parseInt(response.headers.get(GITHUB_RATE_LIMITS.HEADERS.LIMIT) || '0'),
    resetTime: parseInt(response.headers.get(GITHUB_RATE_LIMITS.HEADERS.RESET) || '0')
  })
  updateRateLimit(rateLimit)

  if (!response.ok) {
    const errorBody = await response.text()
    let errorMessage = `GitHub API error ${response.status}: ${errorBody}`
    if (response.status === GITHUB_ERROR_CODES.UNAUTHORIZED) {
      errorMessage = GITHUB_ERROR_MESSAGES.AUTH_FAILED
    } else if (response.status === GITHUB_ERROR_CODES.FORBIDDEN) {
      errorMessage =
        rateLimit.remaining === 0
          ? `GitHub rate limit exceeded. Reset at ${new Date(rateLimit.reset * 1000).toISOString()}`
          : GITHUB_ERROR_MESSAGES.ACCESS_FORBIDDEN
    } else if (response.status === GITHUB_ERROR_CODES.NOT_FOUND) {
      errorMessage = GITHUB_ERROR_MESSAGES.RESOURCE_NOT_FOUND
    }
    throw new Error(errorMessage)
  }

  const data = await response.json()
  return { data, rateLimit }
}

// === REPOSITORY OPERATIONS ===

export async function executeGitHubRepo(owner?: string, repo?: string): Promise<GitHubResponse> {
  const repoInfo = await GitHubUtils.resolveRepository(owner, repo)
  if (!repoInfo) {
    const fallback = await getRepositoryInfo()
    if (!fallback) throw new Error('Repository information not available')
    owner = fallback.owner
    repo = fallback.repo
  } else {
    owner = repoInfo.owner
    repo = repoInfo.repo
  }

  const accessCheck = validateRepositoryAccess(owner, repo)
  if (!accessCheck.allowed) {
    throw new Error(accessCheck.message || 'Repository access denied')
  }

  const endpoint = `/repos/${owner}/${repo}`
  const { data, rateLimit } = await makeGitHubRequest(endpoint)

  return Object.freeze({
    success: true,
    data: Object.freeze({
      name: data.name,
      full_name: data.full_name,
      description: data.description,
      private: data.private,
      html_url: data.html_url,
      clone_url: data.clone_url,
      ssh_url: data.ssh_url,
      default_branch: data.default_branch,
      language: data.language,
      languages_url: data.languages_url,
      stargazers_count: data.stargazers_count,
      watchers_count: data.watchers_count,
      forks_count: data.forks_count,
      open_issues_count: data.open_issues_count,
      created_at: data.created_at,
      updated_at: data.updated_at,
      pushed_at: data.pushed_at
    }),
    rateLimit
  })
}

// === ISSUES OPERATIONS ===

export async function executeGitHubIssues(
  owner?: string,
  repo?: string,
  options: any = {}
): Promise<GitHubResponse> {
  const repoInfo = await GitHubUtils.resolveRepository(owner, repo)
  if (!repoInfo) {
    const fallback = await getRepositoryInfo()
    if (!fallback) throw new Error('Repository information not available')
    owner = fallback.owner
    repo = fallback.repo
  } else {
    owner = repoInfo.owner
    repo = repoInfo.repo
  }

  const accessCheck = validateRepositoryAccess(owner, repo)
  if (!accessCheck.allowed) {
    throw new Error(accessCheck.message || 'Repository access denied')
  }

  const queryParams = new URLSearchParams()
  if (options.state) queryParams.append('state', options.state)
  if (options.labels) queryParams.append('labels', options.labels)
  if (options.sort) queryParams.append('sort', options.sort)
  if (options.direction) queryParams.append('direction', options.direction)
  if (options.since) queryParams.append('since', options.since)
  if (options.per_page) queryParams.append('per_page', options.per_page.toString())
  if (options.page) queryParams.append('page', options.page.toString())

  const endpoint = `/repos/${owner}/${repo}/issues${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
  const { data, rateLimit } = await makeGitHubRequest(endpoint)

  return Object.freeze({
    success: true,
    data: Object.freeze(
      data.map((issue: any) =>
        Object.freeze({
          number: issue.number,
          title: issue.title,
          body: issue.body,
          state: issue.state,
          user: Object.freeze({
            login: issue.user.login,
            avatar_url: issue.user.avatar_url
          }),
          labels: Object.freeze(
            issue.labels.map((label: any) =>
              Object.freeze({
                name: label.name,
                color: label.color
              })
            )
          ),
          assignees: Object.freeze(issue.assignees.map((assignee: any) => assignee.login)),
          milestone: issue.milestone
            ? Object.freeze({
                title: issue.milestone.title,
                state: issue.milestone.state
              })
            : null,
          created_at: issue.created_at,
          updated_at: issue.updated_at,
          closed_at: issue.closed_at,
          html_url: issue.html_url
        })
      )
    ),
    rateLimit
  })
}
