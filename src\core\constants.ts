/* eslint-disable prettier/prettier */
/**
 * Core Constants - AI-Optimized Frozen Configuration
 * Machine-only constants for AI consumption and mutation
 *
 * @notation P:core/constants F:none CB:none I:all DB:none
 */

// === ENVIRONMENT TEMPLATES ===

export const ENV_DEVELOPMENT = {
  NODE_ENV: 'development',
  MCP_PORT: '8082',
  MCP_DB_PATH: '.augment/db/augster-dev.db',
  LOG_LEVEL: 'debug',
  LOG_FILE: '.augment/logs/mcp-dev.log'
} as const

export const ENV_PRODUCTION = {
  NODE_ENV: 'production',
  MCP_PORT: '8081',
  MCP_DB_PATH: '.augment/db/augster.db',
  LOG_LEVEL: 'info',
  LOG_FILE: '.augment/logs/mcp-prod.log'
} as const

// === AI NOTATION SYMBOLS ===

export const AI_SYMBOLS = {
  '🔎': 'EXTRACT',
  '⚡': 'IMPLEMENT',
  '🧪': 'TEST',
  '✅': 'VALIDATE',
  '📝': 'DOCUMENT',
  '🔧': 'CONFIGURE',
  '🚀': 'DEPLOY',
  '🛑': 'STOP',
  '📊': 'MEASURE',
  '🔄': 'COORDINATE',
  '💾': 'COMPILE'
} as const

export const NOTATION_PREFIXES = {
  P: 'PARAMETER',
  F: 'FUNCTION',
  CB: 'CIRCUIT_BREAKER',
  I: 'INTERFACE',
  S: 'STATE',
  DB: 'DATABASE'
} as const

// Symbol detection regex - compiled once for performance - extracted from ExecutionPlanner L:85-86
export const SYMBOL_REGEX =
  /(?:🔎|EXTRACT|⚡|IMPLEMENT|🧪|TEST|✅|VALIDATE|📝|DOCUMENT|🔧|CONFIGURE|🚀|DEPLOY|🛑|STOP|📊|MEASURE|🔄|COORDINATE|💾|COMPILE)/

// Parameter extraction regex - extracted from ExecutionPlanner L:87
export const PARAM_REGEX = /([PMCBISDHAT]):([\w\-\.\/\*]+)/g

// FeedbackProcessor Constants - extracted from feedbackProcessor.ts
export const PERFORMANCE_THRESHOLD = 0.8
export const ERROR_CONFIDENCE_DIVISOR = 5
export const SUCCESS_CONFIDENCE_DIVISOR = 10
export const SLOW_EXECUTION_MULTIPLIER = 2
export const MEMORY_THRESHOLD_MB = 100
export const HIGH_CONFIDENCE_THRESHOLD = 0.67

// Pre-compiled SQL queries for O(1) lookup
export const HISTORICAL_DATA_QUERY = `SELECT
  AVG(CASE WHEN success = 1 THEN execution_time END) as avg_success_time,
  COUNT(CASE WHEN success = 1 THEN 1 END) as success_count,
  COUNT(CASE WHEN success = 0 THEN 1 END) as error_count,
  MIN(CASE WHEN success = 1 THEN execution_time END) as min_time,
  MAX(CASE WHEN success = 1 THEN execution_time END) as max_time
 FROM execution_results
 WHERE handler = ? AND action = ?`

// Optimized task configurations using Map for O(1) lookup - extracted from ExecutionPlanner L:37-82
import { TransformationType, RefinementType, TaskType, TaskConfig } from './types'

export const TASK_CONFIGS = new Map<TaskType, TaskConfig>([
  [
    TaskType.EXTRACT,
    { handler: 'file', action: 'read', priority: 1, duration: 2000, maxRetries: 1 }
  ],
  [
    TaskType.IMPLEMENT,
    { handler: 'file', action: 'write', priority: 2, duration: 5000, maxRetries: 1 }
  ],
  [
    TaskType.TEST,
    { handler: 'terminal', action: 'execute', priority: 3, duration: 10000, maxRetries: 3 }
  ],
  [
    TaskType.VALIDATE,
    { handler: 'monitoring', action: 'health', priority: 4, duration: 3000, maxRetries: 1 }
  ],
  [
    TaskType.DOCUMENT,
    { handler: 'file', action: 'write', priority: 5, duration: 4000, maxRetries: 1 }
  ],
  [
    TaskType.CONFIGURE,
    { handler: 'file', action: 'update', priority: 2, duration: 3000, maxRetries: 1 }
  ],
  [
    TaskType.DEPLOY,
    { handler: 'git', action: 'push', priority: 6, duration: 15000, maxRetries: 1 }
  ],
  [
    TaskType.STOP,
    { handler: 'coordination', action: 'stop', priority: 10, duration: 1000, maxRetries: 1 }
  ],
  [
    TaskType.MEASURE,
    { handler: 'monitoring', action: 'metrics', priority: 1, duration: 2000, maxRetries: 1 }
  ],
  [
    TaskType.COORDINATE,
    { handler: 'coordination', action: 'coordinate', priority: 3, duration: 5000, maxRetries: 1 }
  ],
  [
    TaskType.COMPILE,
    { handler: 'terminal', action: 'compile', priority: 4, duration: 8000, maxRetries: 1 }
  ]
])

// Optimized refinement configurations using Map for O(1) lookup

export const REFINEMENT_CONFIGS = new Map([
  [
    TransformationType.ERROR_PATTERN,
    {
      type: RefinementType.RETRY_POLICY_ADJUSTMENT,
      before: 'default retry policy',
      after: 'enhanced retry with exponential backoff',
      improvement: { performanceGain: 0, reliabilityIncrease: 0.3, resourceEfficiency: 0.1 }
    }
  ],
  [
    TransformationType.PERFORMANCE_INSIGHT,
    {
      type: RefinementType.RESOURCE_REALLOCATION,
      before: 'standard resource allocation',
      after: 'increased timeout and circuit breaker thresholds',
      improvement: { performanceGain: 0.2, reliabilityIncrease: 0.1, resourceEfficiency: -0.1 }
    }
  ],
  [
    TransformationType.RESOURCE_OPTIMIZATION,
    {
      type: RefinementType.PARALLELIZATION_OPTIMIZATION,
      before: 'sequential execution',
      after: 'chunked parallel execution',
      improvement: { performanceGain: 0.4, reliabilityIncrease: 0, resourceEfficiency: 0.3 }
    }
  ]
])

// AINotationProcessor Constants - extracted from aiNotationProcessor.ts
export const AI_NOTATION_SYMBOL_MAP = Object.freeze({
  '🔎': 'EXTRACT',
  '⚡': 'IMPLEMENT',
  '🧪': 'TEST',
  '✅': 'VALIDATE',
  '📝': 'DOCUMENT',
  '🔧': 'CONFIGURE',
  '🚀': 'DEPLOY',
  '🛑': 'STOP',
  '📊': 'MEASURE',
  '🔄': 'COORDINATE',
  '💾': 'COMPILE'
})

export const AI_NOTATION_PARAMETER_PATTERNS = Object.freeze([
  { pattern: /P:([^\s,\]]+)/g, type: 'path' },
  { pattern: /M:([^\s,\]]+)/g, type: 'method' },
  { pattern: /CB:([^\s,\]]+)/g, type: 'circuit_breaker' },
  { pattern: /I:([^\s,\]]+)/g, type: 'interface' },
  { pattern: /S:([^\s,\]]+)/g, type: 'server' },
  { pattern: /DB:([^\s,\]]+)/g, type: 'database' },
  { pattern: /H:([^\s,\]]+)/g, type: 'handler' },
  { pattern: /A:([^\s,\]]+)/g, type: 'action' },
  { pattern: /T:([^\s,\]]+)/g, type: 'target_time' }
])

export const AI_NOTATION_TOOL_PATTERNS = Object.freeze([
  { pattern: /BA:([^\s,\]]+)/g, tool: 'BatchAnalyzer' },
  { pattern: /TS:([^\s,\]]+)/g, tool: 'TypeScriptTool' },
  { pattern: /DB:migrate\s+([^\s,\]]+)/g, tool: 'DatabaseMigration' }
])

// EnhancedToolIntegration Constants - extracted from enhancedToolIntegration.ts
export const TOOL_RELIABILITY_IMPROVEMENTS = Object.freeze({
  BA: 0.79, // 79% reduction in analysis steps
  TS: 0.9, // 90% reduction in compilation failures
  DB: 0.95 // 95% improvement in migration safety
})

export const TOOL_NOTATION_PATTERNS = Object.freeze({
  TOOL_PREFIX: /^(BA|TS|DB):(.+)/,
  BATCH_ANALYSIS: /^BA:(.+)/,
  TYPESCRIPT: /^TS:(.+)/,
  DATABASE: /^DB:(.+)/,
  MIGRATION_SPEC: /→/
})

export const METRICS_CAPS = Object.freeze({
  EXECUTION_EFFICIENCY_GAIN: 0.6,
  ERROR_REDUCTION_RATE: 0.9,
  CHARACTER_REDUCTION_BASELINE: 49
})

// TypeScriptIntegration Constants - extracted from typescriptIntegration.ts
export const DEFAULT_COMPILER_OPTIONS = Object.freeze({
  target: 'ES2020' as const,
  module: 'CommonJS' as const,
  strict: true,
  esModuleInterop: true,
  skipLibCheck: true,
  forceConsistentCasingInFileNames: true,
  declaration: false,
  outDir: './dist',
  rootDir: './src'
})

export const TYPESCRIPT_FILE_EXTENSIONS = Object.freeze([
  '.ts',
  '.js',
  '.tsx',
  '.jsx',
  '/index.ts',
  '/index.js'
])

export const VALIDATION_CATEGORIES = Object.freeze({
  SYNTAX: 'syntax' as const,
  TYPE: 'type' as const,
  IMPORT: 'import' as const,
  SEMANTIC: 'semantic' as const
})

export const SUGGESTION_PATTERNS = Object.freeze([
  {
    pattern: /\.then\(/g,
    suggestion: 'Consider using async/await instead of .then()',
    type: 'best-practice' as const
  },
  {
    pattern: /let\s+(\w+)/g,
    suggestion: 'Consider using const instead of let for variables that are not reassigned',
    type: 'best-practice' as const
  }
])

// TypeScriptIntegration Additional Constants - extracted from typescriptIntegration.ts
export const VALIDATION_CACHE_TTL = 30000
export const EXCLUDED_DIRECTORIES = Object.freeze(['node_modules', 'dist', '.git', 'coverage'])
export const TYPESCRIPT_SCRIPT_TARGET = 'ES2020'
export const TYPESCRIPT_MODULE_KIND = 'CommonJS'

// Pre-compiled TypeScript diagnostic categories for O(1) lookup
export const DIAGNOSTIC_CATEGORY_MAP = Object.freeze({
  [0]: 'warning', // ts.DiagnosticCategory.Warning
  [1]: 'error', // ts.DiagnosticCategory.Error
  [2]: 'suggestion', // ts.DiagnosticCategory.Suggestion
  [3]: 'message' // ts.DiagnosticCategory.Message
})

// Optimized file validation patterns
export const IMPORT_PATTERN = /^\s*import\s+.*from\s+['"`]([^'"`]+)['"`]/
export const RELATIVE_IMPORT_PATTERN = /^\./
export const TYPESCRIPT_DECLARATION_PATTERN = /\.d\.ts$/

// DatabaseMigration Constants - extracted from databaseMigration.ts
export const DATABASE_PATHS = Object.freeze({
  DB_PATH: '.augment/db/augster.db',
  BACKUP_DIR: '.augment/db/backups'
})

export const MIGRATION_ERROR_CODES = Object.freeze({
  MIGRATION_FAILED: 'MIGRATION_FAILED',
  SQL_EXECUTION_ERROR: 'SQL_EXECUTION_ERROR',
  BACKUP_FAILED: 'BACKUP_FAILED',
  RESTORE_FAILED: 'RESTORE_FAILED',
  INTEGRITY_CHECK_FAILED: 'INTEGRITY_CHECK_FAILED'
})

export const MIGRATION_WARNING_CODES = Object.freeze({
  DRY_RUN: 'DRY_RUN',
  BACKUP_SKIPPED: 'BACKUP_SKIPPED',
  ROLLBACK_DISABLED: 'ROLLBACK_DISABLED'
})

export const SCHEMA_CHANGE_TYPES = Object.freeze({
  CREATE_TABLE: 'CREATE_TABLE' as const,
  ALTER_TABLE: 'ALTER_TABLE' as const,
  DROP_TABLE: 'DROP_TABLE' as const,
  CREATE_INDEX: 'CREATE_INDEX' as const,
  DROP_INDEX: 'DROP_INDEX' as const
})

export const REQUIRED_TABLES = Object.freeze([
  'mcp_calls',
  'circuit_breaker_events',
  'handler_metrics'
])
export const MINIMUM_RECORD_COUNT = 240

// Pre-compiled SQL templates for performance
export const SQL_TEMPLATES = Object.freeze({
  PERFORMANCE_BASELINES_TABLE: `
    CREATE TABLE IF NOT EXISTS performance_baselines (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      handler_name TEXT NOT NULL,
      operation_name TEXT NOT NULL,
      baseline_ms REAL NOT NULL,
      min_ms REAL NOT NULL,
      max_ms REAL NOT NULL,
      avg_ms REAL NOT NULL,
      p95_ms REAL NOT NULL,
      sample_count INTEGER NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(handler_name, operation_name)
    )
  `,
  PERFORMANCE_BASELINES_HANDLER_INDEX:
    'CREATE INDEX IF NOT EXISTS idx_performance_baselines_handler ON performance_baselines(handler_name)',
  PERFORMANCE_BASELINES_OPERATION_INDEX:
    'CREATE INDEX IF NOT EXISTS idx_performance_baselines_operation ON performance_baselines(operation_name)',
  INTEGRITY_CHECK: 'PRAGMA integrity_check',
  BEGIN_TRANSACTION: 'BEGIN TRANSACTION',
  COMMIT_TRANSACTION: 'COMMIT',
  ROLLBACK_TRANSACTION: 'ROLLBACK',
  TABLE_EXISTS_CHECK: 'SELECT name FROM sqlite_master WHERE type=\'table\' AND name=?',
  GET_ALL_TABLES: 'SELECT name FROM sqlite_master WHERE type=\'table\''
})

// BatchAnalyzer Constants - extracted from batchAnalyzer.ts
export const BATCH_EXCLUDED_PATTERNS = Object.freeze(['node_modules', '/dist/', '.d.ts'])
export const EXCLUDED_PATTERNS = Object.freeze(['node_modules', '/dist/', '.d.ts'])
export const KNOWN_HANDLERS = Object.freeze([
  'memory',
  'file',
  'database',
  'github',
  'monitoring',
  'coordination',
  'fetch',
  'time',
  'git',
  'terminal'
])

// Pre-compiled regex patterns for performance optimization
export const METHOD_PATTERNS = Object.freeze([
  /^\s*(public|private|protected)?\s*(async\s+)?(\w+)\s*\([^)]*\)\s*:\s*([^{]+)\s*\{/,
  /^\s*(export\s+)?(async\s+)?function\s+(\w+)\s*\([^)]*\)\s*:\s*([^{]+)\s*\{/,
  /^\s*(export\s+)?const\s+(\w+)\s*=\s*\([^)]*\)\s*:\s*([^=]+)\s*=>/
])

export const CIRCUIT_BREAKER_PATTERNS = Object.freeze([
  /CircuitBreaker\(['"`]([^'"`]+)['"`]/,
  /createCircuitBreaker\(['"`]([^'"`]+)['"`]/,
  /new\s+CircuitBreaker\(['"`]([^'"`]+)['"`]/,
  /CB:\s*['"`]?([^'"`\s]+)['"`]?/
])

export const EXPORT_PATTERNS = Object.freeze([
  { pattern: /^\s*export\s+(?:default\s+)?function\s+(\w+)/, type: 'function' as const },
  { pattern: /^\s*export\s+(?:default\s+)?class\s+(\w+)/, type: 'class' as const },
  { pattern: /^\s*export\s+(?:default\s+)?interface\s+(\w+)/, type: 'interface' as const },
  { pattern: /^\s*export\s+const\s+(\w+)/, type: 'const' as const },
  { pattern: /^\s*export\s+default/, type: 'default' as const }
])

export const INTERFACE_PATTERN = /^\s*(?:export\s+)?interface\s+(\w+)(?:\s+extends\s+([^{]+))?\s*\{/
export const IMPORT_PATTERN_FULL =
  /^\s*import\s+(?:type\s+)?(?:\{([^}]+)\}|\*\s+as\s+\w+|(\w+))\s+from\s+['"`]([^'"`]+)['"`]/
export const PARAMETER_PATTERN = /\(([^)]*)\)/
export const PROPERTY_PATTERN = /^\s*(\w+)(\?)?\s*:\s*([^;,]+)/
export const HANDLER_CLASS_PATTERN = /class\s+(\w+)Handler/

// === ANTI-PATTERN DETECTION ===

export const ANTI_PATTERNS = Object.freeze({
  classes: /^\s*(?:export\s+)?(?:abstract\s+)?class\s+\w+/,
  defaultExports: /^\s*export\s+default\s+/,
  anyTypes: /:\s*any\b/,
  globalState: /^\s*(?:let|var)\s+\w+\s*[:=]/,
  thisKeyword: /\bthis\./,
  newKeyword: /\bnew\s+\w+/,
  mutations: /\w+\.\w+\s*=\s*/
})

// === TRANSFORMATION METADATA ===

export const TRANSFORMATION_VERSION = '1.0.0'
export const MANIFEST_SCHEMA_VERSION = '1.0.0'

export const SYMBOL_TYPES = Object.freeze([
  'function',
  'class',
  'interface',
  'type',
  'const',
  'enum'
])

export const NOTATION_SYMBOLS = Object.freeze(['P', 'F', 'CB', 'I', 'S', 'DB'])

// === TOOL CONFIGURATION ===

export const TOOL_REGISTRY = {
  BA: 'src/core/tools/batchAnalyzer.ts',
  TS: 'src/core/tools/tsValidator.ts',
  DB: 'src/core/tools/dbMigrator.ts',
  ANTI: 'src/core/antiPatterns/detector.ts',
  CTX: 'src/core/notation/contextEngine.ts',
  REPORT: 'src/core/report/generateManifest.ts',
  SYMBOL: 'src/core/notation/symbolRegistry.ts',
  UNDO: 'src/core/report/undoTransformer.ts',
  CHAIN: 'src/core/handlers/chainExecutor.ts',
  EXECUTE: 'src/core/handlers/executeCommand.ts',
  LAUNCH: 'src/runtime/launch.ts',
  ENV: 'src/core/state/environment.ts',
  STATE: 'src/core/state/memoryState.ts',
  VALIDATE_SCHEMA: 'src/core/schema/validateSchema.ts',
  BACKUP: 'src/core/state/dbBackup.ts',
  MIGRATE: 'src/core/tools/schemaMigrator.ts',
  TEMPLATE: 'src/core/tools/templateProcessor.ts',
  AI_VALIDATE: 'src/core/notation/validateSymbolContracts.ts',
  PERF: 'src/core/report/performanceReporter.ts',
  LOG: 'src/core/state/logWriter.ts'
} as const

// === PERFORMANCE CONSTANTS ===

export const DEFAULT_CHUNK_SIZE = 8192
export const MAX_METRICS = 10000
export const DEFAULT_TIMEOUT = 30000

// === ADDITIONAL CORE TYPES FROM UTILS/CORE.TS ===

export type ChunkedFileOptions = {
  readonly path: string
  readonly content: string
  readonly chunkSize?: number
}

export type ChunkedFileResult = {
  readonly success: boolean
  readonly chunksProcessed: number
  readonly totalSize: number
  readonly processingTime: number
  readonly errors: readonly string[]
  readonly atomicOperationUsed: boolean
}

export type TypeScriptAnalysis = {
  readonly filePath: string
  readonly functions: readonly string[]
  readonly classes: readonly string[]
  readonly interfaces: readonly string[]
  readonly imports: readonly string[]
  readonly exports: readonly string[]
  readonly errors: readonly string[]
}

export type EnvironmentConfig = {
  readonly isDev: boolean
  readonly nodeEnv: 'development' | 'production'
  readonly mcpPort: string
  readonly dbPath: string
  readonly envFile: string
  readonly logLevel: 'debug' | 'info' | 'warn' | 'error'
  readonly logFile: string
}

export type PerformanceMetrics = {
  readonly timestamp: number
  readonly operation: string
  readonly handler: string
  readonly executionTime: number
  readonly success: boolean
  readonly circuitBreakerState?: string
  readonly retryAttempts?: number
  readonly memoryUsage?: NodeJS.MemoryUsage
}

export type ActionHandler<T = unknown, R = unknown> = (input: T) => Promise<R> | R

export type MetricsState = {
  readonly metrics: readonly PerformanceMetrics[]
  readonly maxSize: number
}

export type ActionState = {
  readonly handlers: ReadonlyMap<string, ActionHandler<unknown, unknown>>
}

// === RESILIENCE DEFAULTS ===

export const DEFAULT_CIRCUIT_BREAKER = {
  failureThreshold: 5,
  recoveryTimeout: 30000,
  monitoringWindow: 60000
} as const

export const DEFAULT_RETRY_POLICY = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  jitterPercent: 25,
  retryableErrors: ['SQLITE_BUSY', 'SQLITE_LOCKED', 'ECONNRESET', 'ECONNREFUSED', 'ETIMEDOUT']
} as const

// === HANDLER OPERATION CONFIGS ===

export const MEMORY_OPERATIONS = ['insert', 'query', 'update', 'template'] as const

export const FILE_OPERATIONS = [
  'read',
  'write',
  'exists',
  'list',
  'search',
  'analyze',
  'backup',
  'template'
] as const

export const GITHUB_OPERATIONS = [
  'repo',
  'issues',
  'commits',
  'branches',
  'pulls',
  'files',
  'search',
  'create-pull',
  'update-pull',
  'merge-pull',
  'review-pull',
  'analyze-pr',
  'validate-token',
  'protect-branch',
  'advanced-search',
  'create-issue',
  'update-issue',
  'close-issue'
] as const

export const DATABASE_OPERATIONS = [
  'connect',
  'execute',
  'query',
  'schema',
  'backup',
  'migrate'
] as const

export const MONITORING_OPERATIONS = ['health', 'metrics', 'dashboard'] as const

// === VALIDATION SCHEMAS ===

export const SCHEMA_PATHS = {
  memory: 'src/core/schema/memory.schema.json',
  file: 'src/core/schema/file.schema.json',
  github: 'src/core/schema/github.schema.json',
  database: 'src/core/schema/database.schema.json',
  monitoring: 'src/core/schema/monitoring.schema.json',
  coordination: 'src/core/schema/coordination.schema.json',
  fetch: 'src/core/schema/fetch.schema.json',
  time: 'src/core/schema/time.schema.json',
  git: 'src/core/schema/git.schema.json',
  terminal: 'src/core/schema/terminal.schema.json'
} as const

// === ADDITIONAL CONSTANTS FROM UTILS/CORE.TS ===

export const MIN_PORT = 1024
export const MAX_PORT = 65535

export const REQUIRED_DIRECTORIES = Object.freeze([
  '.augment/db',
  '.augment/logs',
  '.augment/config',
  '.augment/cache'
] as const)

export const LOG_LEVELS = Object.freeze(['debug', 'info', 'warn', 'error'] as const)

export const SUPPORTED_FILE_EXTENSIONS = Object.freeze([
  '.ts',
  '.js',
  '.json',
  '.md',
  '.txt',
  '.log'
] as const)

export const TEMPLATE_ENGINES = Object.freeze(['simple', 'mustache'] as const)

// === HANDLER DEFAULTS ===

export const DEFAULT_HANDLER_OPTIONS = Object.freeze({
  failureThreshold: 5,
  recoveryTimeout: 30000,
  maxRetries: 3,
  baseDelay: 1000,
  monitoringWindow: 60000
} as const)

export const DEFAULT_RETRYABLE_ERRORS = Object.freeze([
  'SQLITE_BUSY',
  'SQLITE_LOCKED',
  'ECONNRESET',
  'ETIMEDOUT',
  'ECONNREFUSED',
  'ENOTFOUND'
] as const)

// GitHub Handler Constants - extracted from github.ts
export const GITHUB_API_CONFIG = Object.freeze({
  BASE_URL: 'https://api.github.com',
  DEFAULT_PER_PAGE: 30,
  MAX_PER_PAGE: 100,
  USER_AGENT: 'Augster-MCP-Server/1.0.0'
})

export const GITHUB_RATE_LIMITS = Object.freeze({
  THRESHOLD: 100,
  RESET_BUFFER: 60000,
  HEADERS: {
    REMAINING: 'X-RateLimit-Remaining',
    RESET: 'X-RateLimit-Reset',
    LIMIT: 'X-RateLimit-Limit'
  }
})

export const GITHUB_ERROR_CODES = Object.freeze({
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  RATE_LIMITED: 403
})

export const GITHUB_ERROR_MESSAGES = Object.freeze({
  TOKEN_NOT_FOUND: 'GitHub token not found. Set GITHUB_TOKEN or GH_TOKEN environment variable.',
  AUTH_FAILED: 'GitHub authentication failed. Check token validity.',
  ACCESS_FORBIDDEN: 'GitHub access forbidden. Check token permissions and repository access.',
  RESOURCE_NOT_FOUND: 'GitHub resource not found. Check repository name and permissions.',
  REPO_REQUIRED: 'Repository owner and name are required',
  REPO_ENV_REQUIRED:
    'Repository owner and name are required. Set GITHUB_OWNER and GITHUB_REPO environment variables or provide in command.'
})

export const GITHUB_ACTIONS = Object.freeze({
  REPO: 'repo',
  ISSUES: 'issues',
  COMMITS: 'commits',
  BRANCHES: 'branches',
  FILES: 'files',
  PULLS: 'pulls',
  SEARCH: 'search',
  CREATE_PULL: 'create-pull',
  UPDATE_PULL: 'update-pull',
  MERGE_PULL: 'merge-pull',
  REVIEW_PULL: 'review-pull',
  ANALYZE_PR: 'analyze-pr',
  PROTECT_BRANCH: 'protect-branch',
  MANAGE_WEBHOOKS: 'manage-webhooks',
  ADVANCED_SEARCH: 'advanced-search',
  VALIDATE_TOKEN: 'validate-token',
  CREATE_ISSUE: 'create-issue',
  UPDATE_ISSUE: 'update-issue',
  CLOSE_ISSUE: 'close-issue',
  TEMPLATE: 'template',
  STAGE_FILES: 'stage-files',
  CREATE_COMMIT: 'create-commit'
})

// Pre-compiled GitHub operation dispatch map for O(1) lookups
export const GITHUB_OPERATION_MAP = new Map([
  [GITHUB_ACTIONS.REPO, 'executeGitHubRepo'],
  [GITHUB_ACTIONS.ISSUES, 'executeGitHubIssues'],
  [GITHUB_ACTIONS.COMMITS, 'executeGitHubCommits'],
  [GITHUB_ACTIONS.BRANCHES, 'executeGitHubBranches'],
  [GITHUB_ACTIONS.FILES, 'executeGitHubFiles'],
  [GITHUB_ACTIONS.PULLS, 'executeGitHubPulls'],
  [GITHUB_ACTIONS.SEARCH, 'executeGitHubSearch'],
  [GITHUB_ACTIONS.CREATE_PULL, 'executeCreatePull'],
  [GITHUB_ACTIONS.UPDATE_PULL, 'executeUpdatePull'],
  [GITHUB_ACTIONS.MERGE_PULL, 'executeMergePull'],
  [GITHUB_ACTIONS.REVIEW_PULL, 'executeReviewPull'],
  [GITHUB_ACTIONS.ANALYZE_PR, 'executeAnalyzePR'],
  [GITHUB_ACTIONS.PROTECT_BRANCH, 'executeProtectBranch'],
  [GITHUB_ACTIONS.MANAGE_WEBHOOKS, 'executeManageWebhooks'],
  [GITHUB_ACTIONS.ADVANCED_SEARCH, 'executeAdvancedSearch'],
  [GITHUB_ACTIONS.VALIDATE_TOKEN, 'executeValidateToken'],
  [GITHUB_ACTIONS.CREATE_ISSUE, 'executeCreateIssue'],
  [GITHUB_ACTIONS.UPDATE_ISSUE, 'executeUpdateIssue'],
  [GITHUB_ACTIONS.CLOSE_ISSUE, 'executeCloseIssue'],
  [GITHUB_ACTIONS.STAGE_FILES, 'executeStageFiles'],
  [GITHUB_ACTIONS.CREATE_COMMIT, 'executeCreateCommit']
])
