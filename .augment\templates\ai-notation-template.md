# 🔎 {{projectName}} Analysis

## ⚡ Implementation Tasks

{{#each tasks}}

- {{this}}
{{/each}}

## 🧪 Testing Strategy

- P:{{testFile}} validation
- M:{{testMethod}} execution
- CB:{{circuitBreaker}} monitoring

## ✅ Validation Criteria

- I:{{interface}} compliance
- S:{{server}} availability
- DB:{{database}} integrity

## �� Documentation

- H:{{handler}} reference
- A:{{action}} specification
- T:{{targetTime}} performance

## 🔧 Configuration

- BA:{{batchPattern}} analysis
- TS:{{tsValidation}} checks
- DB:migrate {{migrationTarget}} schema
