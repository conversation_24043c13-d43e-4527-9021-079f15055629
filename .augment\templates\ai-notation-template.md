# AI Notation Template for Augster v3.0

## Core Symbolic Notation Patterns

### File & Function References

- **P:file:line** - Path references with line numbers
- **F:function** - Function names and calls
- **M:module** - Module imports and dependencies
- **S:schema** - Schema definitions and validations
- **I:interface** - Interface types and contracts
- **DB:operation** - Database operations and queries

### Execution Flow Notation

- **CB:trace→CB:next→CB:final** - Circuit breaker/callback execution paths
- **F:init→F:process→F:complete** - Function call chains
- **P:src/file.ts:123→P:dest/file.ts:456** - Cross-file execution flows

### Quality & State Tracking

- **S:quality-score:0.85/1.0** - Quality measurements
- **CB:reflection-pattern-name** - Reflection entry identifiers
- **I:SelfCorrecting,ExecutionAccountable** - Trait implementations
- **S:CRITICAL,HIGH,MEDIUM,LOW** - Impact classifications

### System Integration Patterns

- **BA:batch-pattern** - Batch analysis operations
- **TS:type-validation** - TypeScript validation checks
- **DB:migrate-schema** - Database migration operations
- **CB:mem-insert+reflection** - Memory insertion with reflection

### Task Description Format

```
DESCRIPTION:P:file:line F:function→F:next I:interface S:schema CB:trace→CB:result
```

### Symbolic Trace Examples

- Runtime Pipeline: `P:src/index.ts:8 F:initDB→executeAgentLoopLegacy:16→src/core/tools/agent/engine.ts:283 F:executeAgentLoop CB:main-entry→CB:db-init→CB:agent-start`
- Handler Registry: `P:engine.ts:351 F:initializeHandlerRegistry→dynamic-imports[memory,file,database] I:BaseHandler CB:handler-registry→CB:handler-imports`
- Schema Validation: `S:memory.schema.json:10[delete-action-missing]-vs-memory.ts:19-MemoryCommand CB:schema-drift`

### Compression Guidelines

1. **Eliminate verbose explanations** - Use symbolic references only
2. **Maintain technical precision** - Include exact file:line references
3. **Use arrow notation** for execution flows: `→`
4. **Group related symbols** with brackets: `[item1,item2,item3]`
5. **Compress findings** to single-line symbolic patterns
6. **Preserve traceability** through consistent notation
