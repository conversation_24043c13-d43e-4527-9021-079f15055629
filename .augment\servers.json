{"mcp": {"prod": {"name": "MCP_PROD", "command": "node", "args": ["dist/launch.js"], "cwd": ".", "env": {"MCP_DB_PATH": ".augment/db/augster.db", "MCP_PORT": "8081", "NODE_ENV": "production"}, "timeout": 30000, "restart_on_failure": true, "max_restarts": 5}, "dev": {"name": "MCP_DEV", "command": "ts-node", "args": ["src/launch.ts"], "cwd": ".", "env": {"MCP_DB_PATH": ".augment/db/augster-dev.db", "MCP_PORT": "8082", "NODE_ENV": "development"}, "timeout": 30000, "restart_on_failure": true, "max_restarts": 3}}}