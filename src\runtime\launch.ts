/**
 * Runtime Launch - Agent-Enhanced MCP Server Launcher
 * Initializes agent system and launches server with full orchestration
 *
 * @notation P:runtime/launch F:main,findWorkspaceRoot,getSimpleConfig,initializeAgentSystem,launchServer CB:main I:void DB:agent
 */

import { spawn, ChildProcess } from 'child_process'
import path from 'path'
import fs from 'fs'
import dotenv from 'dotenv'

import {
  getAgentModuleInfo,
  initializeAgentRuntime,
  registerRuntimeSymbols
} from '../core/tools/agent'

/**
 * F:findWorkspaceRoot - Find workspace root directory
 * @notation P:startDir F:findWorkspaceRoot CB:none I:string DB:none
 */
function findWorkspaceRoot(startDir: string): string {
  let dir = startDir
  while (dir !== path.dirname(dir)) {
    if (fs.existsSync(path.join(dir, 'package.json'))) return dir
    dir = path.dirname(dir)
  }
  return process.cwd()
}

/**
 * F:getSimpleConfig - Build config from env
 * @notation P:workspaceRoot F:getSimpleConfig CB:none I:Config DB:none
 */
function getSimpleConfig(workspaceRoot: string) {
  const nodeEnv = process.env.NODE_ENV || 'development'
  const envFile =
    nodeEnv === 'development' ? '.env.dev' : nodeEnv === 'production' ? '.env.memory' : '.env'
  dotenv.config({ path: path.join(workspaceRoot, envFile) })

  return Object.freeze({
    nodeEnv,
    dbPath: process.env.MCP_DB_PATH || path.join(workspaceRoot, '.augment', 'mcp.db'),
    mcpPort: process.env.MCP_PORT || (nodeEnv === 'development' ? '8082' : '8081')
  })
}

/**
 * F:initializeAgentSystem - Initialize agent runtime
 * @notation P:workspaceRoot,config F:initializeAgentSystem CB:initializeAgentSystem I:AgentInfo DB:agent
 */
async function initializeAgentSystem(
  workspaceRoot: string,
  config: ReturnType<typeof getSimpleConfig>
) {
  console.log('🤖 Initializing Agent System...')
  const agentInfo = getAgentModuleInfo()
  console.log(`📦 Agent Module: ${agentInfo.name} v${agentInfo.version}`)
  console.log(`🏗️ Architecture: ${agentInfo.architecture}`)
  console.log(`🔧 Features: ${agentInfo.features.join(', ')}`)

  await initializeAgentRuntime(workspaceRoot, {
    nodeEnv: config.nodeEnv,
    dbPath: config.dbPath,
    mcpPort: config.mcpPort
  })
  await registerRuntimeSymbols(workspaceRoot)
  console.log('🔧 Agent runtime initialized and symbols registered')

  // ensure augment dir
  const augmentDir = path.dirname(config.dbPath)
  fs.mkdirSync(augmentDir, { recursive: true })
  console.log(`📁 Augment directory ready: ${augmentDir}`)
}

/**
 * F:launchServer - Spawn and supervise the MCP server process
 * @notation P:workspaceRoot,config F:launchServer CB:launchServer I:Promise<void> DB:agent
 */
function launchServer(workspaceRoot: string, config: ReturnType<typeof getSimpleConfig>) {
  return new Promise<void>((resolve, reject) => {
    const mustUseCompiled =
      config.nodeEnv === 'production' ||
      process.argv.includes('--compiled') ||
      process.env.MCP_USE_COMPILED === 'true' ||
      process.env.APPMAP === 'true'

    const serverPath = mustUseCompiled
      ? path.join(workspaceRoot, 'dist/runtime/server.js')
      : path.join(workspaceRoot, 'src/runtime/server.ts')

    console.log('🚀 Launching MCP Server')
    console.log(`   File: ${serverPath}`)
    console.log(`   Port: ${config.mcpPort}`)
    console.log(`   Mode: ${mustUseCompiled ? 'Compiled JS' : 'ts-node'}`)

    let command = 'node'
    let args: string[]
    if (mustUseCompiled) {
      args = [serverPath, '--db-path', config.dbPath, '--port', config.mcpPort]
    } else {
      const tsNodeBin = path.join(workspaceRoot, 'node_modules', 'ts-node', 'dist', 'bin.js')
      if (!fs.existsSync(tsNodeBin)) {
        return reject(new Error('ts-node not found; install with npm i -D ts-node'))
      }
      args = [tsNodeBin, serverPath, '--db-path', config.dbPath, '--port', config.mcpPort]
    }

    const child: ChildProcess = spawn(command, args, {
      stdio: 'inherit',
      cwd: workspaceRoot,
      env: { ...process.env, NODE_PATH: path.join(workspaceRoot, 'node_modules') }
    })

    child.on('error', err => {
      console.error('❌ Spawn error:', err.message)
      reject(err)
    })

    child.on('exit', (code, signal) => {
      if (signal) {
        console.log(`⚠️ Server killed by signal ${signal}`)
        process.exit(1)
      }
      console.log(`✅ Server exited (code ${code})`)
      resolve()
    })

    // handle parent shutdown
    const shutdown = (sig: NodeJS.Signals) => {
      console.log(`🛑 Caught ${sig}, shutting down server`)
      child.kill(sig)
    }
    process.on('SIGINT', () => shutdown('SIGINT'))
    process.on('SIGTERM', () => shutdown('SIGTERM'))
  })
}

/**
 * F:main - Orchestrate initialization and server launch
 * @notation P:none F:main CB:none I:Promise<void> DB:agent
 */
async function main() {
  const workspaceRoot = findWorkspaceRoot(__dirname)
  const config = getSimpleConfig(workspaceRoot)

  await initializeAgentSystem(workspaceRoot, config)
  await launchServer(workspaceRoot, config)

  // never reaches here until server exits
}

main().catch(err => {
  console.error('❌ Launcher failure:', err)
  process.exit(1)
})
