/**
 * Core Anti-Patterns - AI-Optimized Anti-Pattern Exports
 * Machine-only exports for AI consumption and mutation
 *
 * @notation P:core/antiPatterns/index F:all CB:none I:all DB:resilience
 */

export * from './detector'

export * from './circuitBreaker'
export * from './retryManager'
export * from './resilienceMonitor'

import {
  createCircuitBreakerRegistry,
  getOrCreateCircuitBreaker,
  getAllCircuitBreakerMetrics,
  type CircuitBreakerConfig
} from './circuitBreaker'

import {
  createRetryManagerRegistry,
  getOrCreateRetryManager,
  getAllRetryMetrics,
  type RetryConfig
} from './retryManager'

import {
  createResilienceMonitor,
  recordErrorRecoveryEvent,
  getSystemHealthMetrics,
  performHealthCheck,
  type ErrorRecoveryEvent
} from './resilienceMonitor'

/**
 * F:createUnifiedResilienceSystem - Create unified resilience system with all components
 * @notation P:circuitBreakerConfig,retryConfig,monitorConfig F:createUnifiedResilienceSystem CB:none I:object DB:resilience
 */
export const createUnifiedResilienceSystem = (
  circuitBreakerConfig?: Partial<CircuitBreakerConfig>,
  retryConfig?: Partial<RetryConfig>,
  monitorConfig?: { maxEventHistory?: number }
) => {
  const circuitBreakerRegistry = createCircuitBreakerRegistry()
  const retryManagerRegistry = createRetryManagerRegistry()
  const resilienceMonitor = createResilienceMonitor(monitorConfig?.maxEventHistory)

  return Object.freeze({
    circuitBreakerRegistry,
    retryManagerRegistry,
    resilienceMonitor,

    getOrCreateCircuitBreaker: (name: string, config?: Partial<CircuitBreakerConfig>) =>
      getOrCreateCircuitBreaker(circuitBreakerRegistry, name, {
        ...circuitBreakerConfig,
        ...config
      }),

    getOrCreateRetryManager: (name: string, config?: Partial<RetryConfig>) =>
      getOrCreateRetryManager(retryManagerRegistry, name, { ...retryConfig, ...config }),

    recordErrorRecovery: (event: Omit<ErrorRecoveryEvent, 'timestamp'>) =>
      recordErrorRecoveryEvent(resilienceMonitor, event),

    getSystemHealth: () =>
      getSystemHealthMetrics(
        resilienceMonitor,
        getAllCircuitBreakerMetrics(circuitBreakerRegistry),
        getAllRetryMetrics(retryManagerRegistry)
      ),

    performHealthCheck: () => {
      const metrics = getSystemHealthMetrics(
        resilienceMonitor,
        getAllCircuitBreakerMetrics(circuitBreakerRegistry),
        getAllRetryMetrics(retryManagerRegistry)
      )
      performHealthCheck(metrics)
      return metrics
    }
  })
}

export type UnifiedResilienceSystem = ReturnType<typeof createUnifiedResilienceSystem>
