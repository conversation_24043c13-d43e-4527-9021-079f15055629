-- ===================================================================
-- UNIFIED AUGSTER MCP DATABASE SCHEMA
-- Comprehensive schema for all MCP system operations, testing, and analysis
-- ===================================================================

-- === CORE SYSTEM TABLES ===

-- System configuration and metadata
CREATE TABLE IF NOT EXISTS system_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key TEXT NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    config_type TEXT NOT NULL CHECK (config_type IN ('string', 'number', 'boolean', 'json')),
    description TEXT,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);

-- === HANDLER MANAGEMENT ===

-- Handler registry and metadata
CREATE TABLE IF NOT EXISTS handlers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    handler_name TEXT NOT NULL UNIQUE,
    handler_type TEXT NOT NULL,
    version TEXT NOT NULL DEFAULT '1.0.0',
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'deprecated')),
    description TEXT,
    config_schema TEXT, -- JSON schema for handler configuration
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);

-- Handler testing results and metrics
CREATE TABLE IF NOT EXISTS handler_tests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    handler_id INTEGER NOT NULL,
    test_session_id TEXT NOT NULL,
    command TEXT NOT NULL,
    action TEXT NOT NULL,
    payload TEXT, -- JSON payload
    success BOOLEAN NOT NULL,
    execution_time_ms INTEGER NOT NULL,
    response_data TEXT, -- JSON response
    error_message TEXT,
    validation_errors TEXT, -- JSON array of validation errors
    test_timestamp INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    FOREIGN KEY (handler_id) REFERENCES handlers(id)
);

-- Handler performance metrics aggregation
CREATE TABLE IF NOT EXISTS handler_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    handler_id INTEGER NOT NULL,
    metric_date TEXT NOT NULL, -- YYYY-MM-DD format
    total_executions INTEGER NOT NULL DEFAULT 0,
    successful_executions INTEGER NOT NULL DEFAULT 0,
    failed_executions INTEGER NOT NULL DEFAULT 0,
    avg_execution_time_ms REAL NOT NULL DEFAULT 0,
    min_execution_time_ms INTEGER NOT NULL DEFAULT 0,
    max_execution_time_ms INTEGER NOT NULL DEFAULT 0,
    error_rate REAL NOT NULL DEFAULT 0,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    FOREIGN KEY (handler_id) REFERENCES handlers(id),
    UNIQUE(handler_id, metric_date)
);

-- === REFACTOR AND ARCHITECTURE TRACKING ===

-- Comprehensive refactor manifest tracking
CREATE TABLE IF NOT EXISTS refactor_manifest (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    operation_id TEXT NOT NULL UNIQUE,
    operation_type TEXT NOT NULL CHECK (operation_type IN ('migration', 'optimization', 'fix', 'enhancement', 'testing')),
    source_file TEXT NOT NULL,
    target_file TEXT,
    phase TEXT, -- Phase 9, Phase 10, etc.
    status TEXT NOT NULL DEFAULT 'planned' CHECK (status IN ('planned', 'in_progress', 'complete', 'failed', 'rolled_back')),
    changes TEXT NOT NULL, -- JSON array of changes
    validation_results TEXT, -- JSON validation results
    performance_impact TEXT, -- JSON performance metrics
    rollback_data TEXT, -- JSON rollback information
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);

-- Architecture compliance tracking
CREATE TABLE IF NOT EXISTS architecture_compliance (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    file_path TEXT NOT NULL,
    compliance_type TEXT NOT NULL CHECK (compliance_type IN ('ai_notation', 'pure_functions', 'immutability', 'anti_patterns')),
    compliance_status TEXT NOT NULL CHECK (compliance_status IN ('compliant', 'non_compliant', 'warning')),
    issues TEXT, -- JSON array of issues
    recommendations TEXT, -- JSON array of recommendations
    checked_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);

-- === AGENT WORKFLOW AND EXECUTION ===

-- Agent execution sessions
CREATE TABLE IF NOT EXISTS agent_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL UNIQUE,
    agent_version TEXT NOT NULL,
    start_time INTEGER NOT NULL,
    end_time INTEGER,
    status TEXT NOT NULL DEFAULT 'running' CHECK (status IN ('running', 'completed', 'failed', 'timeout')),
    total_commands INTEGER NOT NULL DEFAULT 0,
    successful_commands INTEGER NOT NULL DEFAULT 0,
    failed_commands INTEGER NOT NULL DEFAULT 0,
    total_execution_time_ms INTEGER NOT NULL DEFAULT 0,
    context_data TEXT, -- JSON context information
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);

-- Individual command executions within sessions
CREATE TABLE IF NOT EXISTS command_executions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL,
    command TEXT NOT NULL,
    handler_name TEXT NOT NULL,
    action TEXT NOT NULL,
    payload TEXT, -- JSON payload
    success BOOLEAN NOT NULL,
    execution_time_ms INTEGER NOT NULL,
    response_data TEXT, -- JSON response
    error_message TEXT,
    symbolic_trace TEXT, -- JSON symbolic trace data
    validation_results TEXT, -- JSON validation results
    feedback_data TEXT, -- JSON feedback processing results
    executed_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    FOREIGN KEY (session_id) REFERENCES agent_sessions(session_id)
);

-- === RESILIENCE AND ERROR RECOVERY ===

-- Circuit breaker states and events
CREATE TABLE IF NOT EXISTS circuit_breaker_events (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    circuit_breaker_name TEXT NOT NULL,
    previous_state TEXT NOT NULL CHECK (previous_state IN ('CLOSED', 'OPEN', 'HALF_OPEN')),
    new_state TEXT NOT NULL CHECK (new_state IN ('CLOSED', 'OPEN', 'HALF_OPEN')),
    failure_count INTEGER NOT NULL DEFAULT 0,
    success_count INTEGER NOT NULL DEFAULT 0,
    error_message TEXT,
    recovery_time_ms INTEGER,
    event_timestamp INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);

-- Error recovery tracking
CREATE TABLE IF NOT EXISTS error_recovery_events (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    error_type TEXT NOT NULL,
    error_message TEXT NOT NULL,
    recovery_method TEXT NOT NULL CHECK (recovery_method IN ('RETRY', 'CIRCUIT_BREAKER', 'FALLBACK', 'MANUAL')),
    retry_count INTEGER NOT NULL DEFAULT 0,
    recovery_time_ms INTEGER,
    success BOOLEAN NOT NULL,
    context_data TEXT, -- JSON context information
    occurred_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);

-- === LEARNING AND OPTIMIZATION ===

-- Performance patterns and insights
CREATE TABLE IF NOT EXISTS performance_insights (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    insight_type TEXT NOT NULL CHECK (insight_type IN ('pattern', 'anomaly', 'optimization', 'degradation')),
    handler_name TEXT,
    description TEXT NOT NULL,
    impact_level TEXT NOT NULL CHECK (impact_level IN ('low', 'medium', 'high', 'critical')),
    recommendation TEXT,
    data_points TEXT, -- JSON array of supporting data
    confidence_score REAL NOT NULL CHECK (confidence_score >= 0 AND confidence_score <= 1),
    identified_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);

-- System traits and behavioral patterns
CREATE TABLE IF NOT EXISTS system_traits (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    trait_name TEXT NOT NULL,
    trait_category TEXT NOT NULL CHECK (trait_category IN ('performance', 'reliability', 'efficiency', 'quality')),
    trait_value REAL NOT NULL,
    quality_score REAL NOT NULL CHECK (quality_score >= 0 AND quality_score <= 1),
    evidence TEXT, -- JSON evidence supporting the trait
    reasoning TEXT NOT NULL,
    measured_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);

-- === AUDIT AND DOCUMENTATION ===

-- Comprehensive audit trail
CREATE TABLE IF NOT EXISTS audit_trail (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    entity_type TEXT NOT NULL CHECK (entity_type IN ('handler', 'session', 'refactor', 'config', 'schema')),
    entity_id TEXT NOT NULL,
    action TEXT NOT NULL CHECK (action IN ('create', 'update', 'delete', 'execute', 'validate')),
    old_values TEXT, -- JSON of previous values
    new_values TEXT, -- JSON of new values
    user_context TEXT, -- JSON user/system context
    timestamp INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);

-- === INDEXES FOR PERFORMANCE ===

-- Handler testing indexes
CREATE INDEX IF NOT EXISTS idx_handler_tests_handler_session ON handler_tests(handler_id, test_session_id);
CREATE INDEX IF NOT EXISTS idx_handler_tests_timestamp ON handler_tests(test_timestamp);
CREATE INDEX IF NOT EXISTS idx_handler_tests_success ON handler_tests(success);

-- Performance metrics indexes
CREATE INDEX IF NOT EXISTS idx_handler_metrics_handler_date ON handler_metrics(handler_id, metric_date);
CREATE INDEX IF NOT EXISTS idx_handler_metrics_date ON handler_metrics(metric_date);

-- Refactor manifest indexes
CREATE INDEX IF NOT EXISTS idx_refactor_manifest_status ON refactor_manifest(status);
CREATE INDEX IF NOT EXISTS idx_refactor_manifest_type ON refactor_manifest(operation_type);
CREATE INDEX IF NOT EXISTS idx_refactor_manifest_file ON refactor_manifest(source_file);

-- Agent session indexes
CREATE INDEX IF NOT EXISTS idx_agent_sessions_status ON agent_sessions(status);
CREATE INDEX IF NOT EXISTS idx_command_executions_session ON command_executions(session_id);
CREATE INDEX IF NOT EXISTS idx_command_executions_handler ON command_executions(handler_name);
CREATE INDEX IF NOT EXISTS idx_command_executions_timestamp ON command_executions(executed_at);

-- Circuit breaker indexes
CREATE INDEX IF NOT EXISTS idx_circuit_breaker_name_timestamp ON circuit_breaker_events(circuit_breaker_name, event_timestamp);

-- Audit trail indexes
CREATE INDEX IF NOT EXISTS idx_audit_trail_entity ON audit_trail(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_audit_trail_timestamp ON audit_trail(timestamp);

-- === INITIAL DATA POPULATION ===

-- Insert core handlers
INSERT OR IGNORE INTO handlers (handler_name, handler_type, description) VALUES
('memory', 'core', 'Memory operations and data storage'),
('file', 'core', 'File system operations'),
('database', 'core', 'Database operations and queries'),
('time', 'utility', 'Time and date operations'),
('fetch', 'network', 'HTTP fetch operations'),
('github', 'integration', 'GitHub API operations'),
('monitoring', 'system', 'System monitoring and metrics'),
('coordination', 'system', 'Multi-agent coordination'),
('git', 'integration', 'Git version control operations'),
('terminal', 'system', 'Terminal command execution');

-- Insert system configuration
INSERT OR IGNORE INTO system_config (config_key, config_value, config_type, description) VALUES
('schema_version', '1.0.0', 'string', 'Current database schema version'),
('agent_version', '1.0.0', 'string', 'Current agent system version'),
('performance_baseline_ms', '100', 'number', 'Performance baseline in milliseconds'),
('max_retry_attempts', '3', 'number', 'Maximum retry attempts for failed operations'),
('circuit_breaker_threshold', '5', 'number', 'Circuit breaker failure threshold');
