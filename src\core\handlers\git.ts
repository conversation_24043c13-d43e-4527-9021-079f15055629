/**
 * Git Handler - AI-Optimized Pure Functions
 * @notation P:core/handlers/git F:git<PERSON><PERSON><PERSON>,executeGitCommand CB:executeGitCommand I:GitCommand,GitResult DB:git
 */

import { exec } from 'child_process'
import { promisify } from 'util'
import * as path from 'path'
import * as fs from 'fs'
import { processMultiHandlerTemplate } from '../tools'
import {
  HandlerConfig,
  OperationResult,
  createHandlerConfig,
  executeWithResilience
} from './executeCommand'

const execAsync = promisify(exec)

// @removed:CommandSanitizer,SecurityLogger - Phase 11A.P.6 - security validation removed for core functionality

export type GitCommand = {
  readonly action:
    | 'status'
    | 'log'
    | 'diff'
    | 'branch'
    | 'checkout'
    | 'commit'
    | 'push'
    | 'pull'
    | 'remote'
    | 'add'
    | 'reset'
    | 'template'
  readonly repository?: string
  readonly branch?: string
  readonly message?: string
  readonly files?: readonly string[]
  readonly path?: string
  readonly content?: string
  readonly options?: {
    readonly limit?: number
    readonly format?: string
    readonly remote?: string
    readonly force?: boolean
    readonly all?: boolean
    readonly templateEngine?: 'simple' | 'mustache'
    readonly templateVars?: Record<string, unknown>
    readonly templateOutputPath?: string
  }
}

export type GitResult = {
  readonly success: boolean
  readonly data?: {
    readonly output: string
    readonly repository: string
    readonly command: string
    readonly workingDirectory: string
    readonly result?: unknown
  }
  readonly error?: string
  readonly timestamp: number
  readonly processingTime: number
}

/**
 * F:createGitConfig - Create git handler configuration
 * @notation P:none F:createGitConfig CB:none I:HandlerConfig DB:none
 */
export const createGitConfig = (): HandlerConfig => {
  return createHandlerConfig(
    'git',
    [
      'status',
      'log',
      'diff',
      'branch',
      'checkout',
      'commit',
      'push',
      'pull',
      'remote',
      'add',
      'reset',
      'template'
    ],
    {
      failureThreshold: 5,
      recoveryTimeout: 30000,
      maxRetries: 3,
      baseDelay: 1000
    }
  )
}

/**
 * F:validateGitCommand - Validate git command structure
 * @notation P:command F:validateGitCommand CB:none I:boolean DB:none
 */
export const validateGitCommand = (command: unknown): command is GitCommand => {
  if (!command || typeof command !== 'object') return false

  const cmd = command as Record<string, unknown>

  return (
    typeof cmd.action === 'string' &&
    [
      'status',
      'log',
      'diff',
      'branch',
      'checkout',
      'commit',
      'push',
      'pull',
      'remote',
      'add',
      'reset',
      'template'
    ].includes(cmd.action)
  )
}

/**
 * F:executeGitOperation - Execute Git command safely with security validation
 * @notation P:command,repository,args F:executeGitOperation CB:executeGitOperation I:GitResult DB:git
 */
export const executeGitOperation = async (
  command: string,
  repository?: string
): Promise<GitResult> => {
  const startTime = Date.now()

  try {
    const workingDir = repository || process.cwd()

    if (!fs.existsSync(path.join(workingDir, '.git'))) {
      throw new Error(`Not a git repository: ${workingDir}`)
    }

    // @removed:security_validation - Phase 11A.P.6 - simplified for core functionality

    const { stdout, stderr } = await execAsync(command, {
      cwd: workingDir,
      timeout: 30000,
      maxBuffer: 1024 * 1024
    })

    // @removed:security_logging - Phase 11A.P.6

    return Object.freeze({
      success: true,
      data: Object.freeze({
        output: stdout || stderr,
        repository: workingDir,
        command,
        workingDirectory: workingDir
      }),
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  } catch (error) {
    // @removed:security_logging - Phase 11A.P.6
    return Object.freeze({
      success: false,
      error: (error as Error).message,
      timestamp: Date.now(),
      processingTime: Date.now() - startTime
    })
  }
}

/**
 * F:executeGitStatus - Get repository status
 * @notation P:repository F:executeGitStatus CB:executeGitStatus I:GitResult DB:git
 */
export const executeGitStatus = async (repository?: string): Promise<GitResult> => {
  return await executeGitOperation('git status --porcelain', repository)
}

/**
 * F:executeGitLog - Get commit log
 * @notation P:repository,limit F:executeGitLog CB:executeGitLog I:GitResult DB:git
 */
export const executeGitLog = async (
  repository?: string,
  limit: number = 10
): Promise<GitResult> => {
  const command = `git log --oneline -${limit}`
  return await executeGitOperation(command, repository)
}

/**
 * F:executeGitDiff - Get diff
 * @notation P:repository,files F:executeGitDiff CB:executeGitDiff I:GitResult DB:git
 */
export const executeGitDiff = async (
  repository?: string,
  files?: readonly string[]
): Promise<GitResult> => {
  let command = 'git diff'
  if (files && files.length > 0) {
    command += ` -- ${files.join(' ')}`
  }
  return await executeGitOperation(command, repository)
}

/**
 * F:executeGitBranch - List branches
 * @notation P:repository,all F:executeGitBranch CB:executeGitBranch I:GitResult DB:git
 */
export const executeGitBranch = async (
  repository?: string,
  all: boolean = false
): Promise<GitResult> => {
  const command = all ? 'git branch -a' : 'git branch'
  return await executeGitOperation(command, repository)
}

/**
 * F:executeGitCheckout - Checkout branch
 * @notation P:branch,repository,create F:executeGitCheckout CB:executeGitCheckout I:GitResult DB:git
 */
export const executeGitCheckout = async (
  branch: string,
  repository?: string,
  create: boolean = false
): Promise<GitResult> => {
  const command = create ? `git checkout -b ${branch}` : `git checkout ${branch}`
  return await executeGitOperation(command, repository)
}

/**
 * F:executeGitCommit - Commit changes
 * @notation P:message,repository,files F:executeGitCommit CB:executeGitCommit I:GitResult DB:git
 */
export const executeGitCommit = async (
  message: string,
  repository?: string,
  files?: readonly string[]
): Promise<GitResult> => {
  try {
    if (files && files.length > 0) {
      const addCommand = `git add ${files.join(' ')}`
      await executeGitOperation(addCommand, repository)
    }

    const command = `git commit -m "${message}"`
    return await executeGitOperation(command, repository)
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message,
      timestamp: Date.now(),
      processingTime: 0
    })
  }
}

/**
 * F:executeGitPush - Push changes
 * @notation P:repository,remote,branch F:executeGitPush CB:executeGitPush I:GitResult DB:git
 */
export const executeGitPush = async (
  repository?: string,
  remote: string = 'origin',
  branch?: string
): Promise<GitResult> => {
  let command = `git push ${remote}`
  if (branch) {
    command += ` ${branch}`
  }
  return await executeGitOperation(command, repository)
}

/**
 * F:executeGitPull - Pull changes
 * @notation P:repository,remote,branch F:executeGitPull CB:executeGitPull I:GitResult DB:git
 */
export const executeGitPull = async (
  repository?: string,
  remote: string = 'origin',
  branch?: string
): Promise<GitResult> => {
  let command = `git pull ${remote}`
  if (branch) {
    command += ` ${branch}`
  }
  return await executeGitOperation(command, repository)
}

/**
 * F:executeGitRemote - Get remote information
 * @notation P:repository F:executeGitRemote CB:executeGitRemote I:GitResult DB:git
 */
export const executeGitRemote = async (repository?: string): Promise<GitResult> => {
  return await executeGitOperation('git remote -v', repository)
}

/**
 * F:executeGitAdd - Add files to staging
 * @notation P:files,repository F:executeGitAdd CB:executeGitAdd I:GitResult DB:git
 */
export const executeGitAdd = async (
  files: readonly string[],
  repository?: string
): Promise<GitResult> => {
  const command = `git add ${files.join(' ')}`
  return await executeGitOperation(command, repository)
}

/**
 * F:executeGitReset - Reset changes
 * @notation P:repository,files,hard F:executeGitReset CB:executeGitReset I:GitResult DB:git
 */
export const executeGitReset = async (
  repository?: string,
  files?: readonly string[],
  hard: boolean = false
): Promise<GitResult> => {
  let command = 'git reset'
  if (hard) {
    command += ' --hard'
  }
  if (files && files.length > 0) {
    command += ` -- ${files.join(' ')}`
  }
  return await executeGitOperation(command, repository)
}

/**
 * F:executeGitCommand - Execute git command with resilience
 * @notation P:command F:executeGitCommand CB:executeGitCommand I:OperationResult DB:git
 */
export const executeGitCommand = async (
  command: GitCommand
): Promise<OperationResult<GitResult>> => {
  const config = createGitConfig()

  return await executeWithResilience(command, config, async cmd => {
    const { action, repository, branch, message, files, options } = cmd

    switch (action) {
      case 'status':
        return await executeGitStatus(repository)

      case 'log':
        return await executeGitLog(repository, options?.limit)

      case 'diff':
        return await executeGitDiff(repository, files)

      case 'branch':
        return await executeGitBranch(repository, options?.all)

      case 'checkout':
        if (!branch) throw new Error('Branch name required for checkout')
        return await executeGitCheckout(branch, repository, options?.force)

      case 'commit':
        if (!message) throw new Error('Commit message required')
        return await executeGitCommit(message, repository, files)

      case 'push':
        return await executeGitPush(repository, options?.remote, branch)

      case 'pull':
        return await executeGitPull(repository, options?.remote, branch)

      case 'remote':
        return await executeGitRemote(repository)

      case 'add':
        if (!files || files.length === 0) throw new Error('Files required for add')
        return await executeGitAdd(files, repository)

      case 'reset':
        return await executeGitReset(repository, files, options?.force)

      case 'template':
        const templateSource = cmd.content || cmd.path
        if (!templateSource)
          throw new Error('Either path or content must be provided for template action')
        return await executeGitTemplate(
          templateSource,
          options?.templateVars || {},
          options?.templateEngine || 'simple'
        )

      default:
        throw new Error(`Unknown git action: ${action}`)
    }
  })
}

/**
 * F:executeGitTemplate - Execute git template operation
 * @notation P:templateSource,vars,engine F:executeGitTemplate CB:executeGitTemplate I:GitResult DB:none
 */
export const executeGitTemplate = async (
  templateSource: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple'
): Promise<GitResult> => {
  try {
    const result = await processMultiHandlerTemplate(templateSource, vars, engine)

    return Object.freeze({
      success: true,
      data: Object.freeze({
        output: result.content,
        repository: process.cwd(),
        command: 'template',
        workingDirectory: process.cwd(),
        result: result.content
      }),
      timestamp: Date.now(),
      processingTime: 0
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message,
      timestamp: Date.now(),
      processingTime: 0
    })
  }
}

/**
 * F:gitHandler - Create git handler function
 * @notation P:db F:gitHandler CB:none I:function DB:git
 */
export const gitHandler = () => {
  return {
    execute: async (input: unknown): Promise<GitResult> => {
      if (!validateGitCommand(input)) {
        return Object.freeze({
          success: false,
          error: 'Invalid git command structure',
          timestamp: Date.now(),
          processingTime: 0
        })
      }

      const result = await executeGitCommand(input)
      return result.success
        ? result.data
        : Object.freeze({
            success: false,
            error: result.error,
            timestamp: Date.now(),
            processingTime: 0
          })
    }
  }
}
