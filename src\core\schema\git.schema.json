{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Git Operations Schema", "description": "Validation schema for git operations handler commands", "type": "object", "properties": {"action": {"type": "string", "enum": ["status", "log", "diff", "branch", "checkout", "commit", "push", "pull", "remote", "add", "reset", "template"]}, "path": {"type": "string", "description": "Template file path for template action"}, "content": {"type": "string", "description": "Template content for template action"}, "repository": {"type": "string", "description": "Path to git repository (defaults to current directory)", "maxLength": 500}, "branch": {"type": "string", "description": "Branch name for checkout, push, pull operations", "pattern": "^[a-zA-Z0-9._/-]+$", "maxLength": 100}, "message": {"type": "string", "description": "Commit message", "minLength": 1, "maxLength": 1000}, "files": {"type": "array", "description": "Array of file paths for add, commit, diff, reset operations", "items": {"type": "string", "maxLength": 500}, "maxItems": 100}, "options": {"type": "object", "properties": {"limit": {"type": "number", "description": "Number of log entries to retrieve", "minimum": 1, "maximum": 1000, "default": 10}, "format": {"type": "string", "description": "Output format for log command", "enum": ["oneline", "short", "medium", "full", "fuller", "raw"], "default": "oneline"}, "remote": {"type": "string", "description": "Remote name for push/pull operations", "pattern": "^[a-zA-Z0-9._-]+$", "maxLength": 50, "default": "origin"}, "force": {"type": "boolean", "description": "Force operation (use with caution)", "default": false}, "all": {"type": "boolean", "description": "Include all branches or files", "default": false}, "templateEngine": {"type": "string", "enum": ["simple", "mustache"], "default": "simple"}, "templateVars": {"type": "object", "description": "Template variables for substitution"}, "templateOutputPath": {"type": "string", "description": "Output path for processed template"}}, "additionalProperties": false}}, "required": ["action"], "allOf": [{"if": {"properties": {"action": {"const": "checkout"}}}, "then": {"required": ["branch"]}}, {"if": {"properties": {"action": {"const": "commit"}}}, "then": {"required": ["message"]}}, {"if": {"properties": {"action": {"const": "add"}}}, "then": {"required": ["files"], "properties": {"files": {"type": "array", "minItems": 1}}}}, {"if": {"properties": {"action": {"enum": ["push", "pull"]}}}, "then": {"properties": {"options": {"type": "object", "properties": {"remote": {"type": "string"}}}}}}], "additionalProperties": false}