/**
 * Memory Handler - AI-Optimized Pure Functions
 * Migrated from BaseHandler class to pure function architecture
 *
 * @notation P:core/handlers/memory F:memoryHandler,executeMemoryCommand CB:executeMemoryCommand I:MemoryCommand,MemoryResult DB:memory
 */

import { Database } from 'sqlite3'
import { executeOptimizedQuery, QueryBuilder } from '../tools/queryOptimizer'
import { processMultiHandlerTemplate } from '../tools'
import {
  HandlerConfig,
  OperationResult,
  createHandlerConfig,
  executeWithResilience
} from './executeCommand'
import { validateMemoryCommand } from '../schema/validateSchema'

export type MemoryCommand = {
  readonly action: 'insert' | 'query' | 'update' | 'delete' | 'template'
  readonly table: string
  readonly data?: Record<string, unknown>
  readonly filters?: Record<string, unknown>
  readonly where?: Record<string, unknown>
  readonly limit?: number
  readonly path?: string
  readonly content?: string
  readonly options?: {
    readonly templateEngine?: 'simple' | 'mustache'
    readonly templateOutputPath?: string
    readonly templateVars?: Record<string, unknown>
  }
}

export type MemoryResult = {
  readonly success: boolean
  readonly data?: unknown
  readonly error?: string
  readonly aiNotationMetadata?: unknown
}

/**
 * F:createMemoryConfig - Create memory handler configuration
 * @notation P:none F:createMemoryConfig CB:none I:HandlerConfig DB:none
 */
export const createMemoryConfig = (): HandlerConfig => {
  return createHandlerConfig('memory', ['insert', 'query', 'update', 'delete', 'template'], {
    failureThreshold: 5,
    recoveryTimeout: 30000,
    maxRetries: 3,
    baseDelay: 1000
  })
}

/**
 * F:validateMemoryCommand - Validate memory command structure
 * @notation P:command F:validateMemoryCommand CB:none I:boolean DB:none
 */
export const validateMemoryCommand = (command: unknown): command is MemoryCommand => {
  if (!command || typeof command !== 'object') return false

  const cmd = command as Record<string, unknown>

  return (
    typeof cmd.action === 'string' &&
    ['insert', 'query', 'update', 'template'].includes(cmd.action) &&
    typeof cmd.table === 'string'
  )
}

/**
 * F:executeMemoryInsert - Execute memory insert operation
 * @notation P:db,table,data F:executeMemoryInsert CB:executeMemoryInsert I:MemoryResult DB:memory
 */
export const executeMemoryInsert = async (
  db: Database,
  table: string,
  data: Record<string, unknown>
): Promise<MemoryResult> => {
  try {
    const keys = Object.keys(data)
    const placeholders = keys.map(() => '?').join(',')
    const sql = `INSERT INTO ${table} (${keys.join(',')}) VALUES (${placeholders})`
    const params = Object.values(data)

    const result = await executeOptimizedQuery(db, sql, params)

    return Object.freeze({
      success: true,
      data: result
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message
    })
  }
}

/**
 * F:executeMemoryQuery - Execute memory query operation
 * @notation P:db,table,filters,limit F:executeMemoryQuery CB:executeMemoryQuery I:MemoryResult DB:memory
 */
export const executeMemoryQuery = async (
  db: Database,
  table: string,
  filters?: Record<string, unknown>,
  limit?: number
): Promise<MemoryResult> => {
  try {
    const query = new QueryBuilder(table)

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        query.where(`${key} = ?`, value)
      })
    }

    if (limit) {
      query.limit(limit)
    }

    const result = await query.execute(db)

    return Object.freeze({
      success: true,
      data: result.data
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message
    })
  }
}

/**
 * F:executeMemoryUpdate - Execute memory update operation
 * @notation P:db,table,data,filters F:executeMemoryUpdate CB:executeMemoryUpdate I:MemoryResult DB:memory
 */
export const executeMemoryUpdate = async (
  db: Database,
  table: string,
  data: Record<string, unknown>,
  filters?: Record<string, unknown>
): Promise<MemoryResult> => {
  try {
    const set = Object.keys(data)
      .map(k => `${k} = ?`)
      .join(', ')

    let sql = `UPDATE ${table} SET ${set}`
    let params = [...Object.values(data)]

    if (filters && Object.keys(filters).length > 0) {
      const where = Object.keys(filters)
        .map(k => `${k} = ?`)
        .join(' AND ')
      sql += ` WHERE ${where}`
      params = [...params, ...Object.values(filters)]
    }

    const result = await executeOptimizedQuery(db, sql, params)

    return Object.freeze({
      success: true,
      data: result
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message
    })
  }
}

/**
 * F:executeMemoryDelete - Execute memory delete operation
 * @notation P:db,table,where F:executeMemoryDelete CB:executeMemoryDelete I:MemoryResult DB:memory
 */
export const executeMemoryDelete = async (
  db: Database,
  table: string,
  where?: Record<string, unknown>
): Promise<MemoryResult> => {
  try {
    let sql = `DELETE FROM ${table}`
    let params: unknown[] = []

    if (where && Object.keys(where).length > 0) {
      const whereClause = Object.keys(where)
        .map(k => `${k} = ?`)
        .join(' AND ')
      sql += ` WHERE ${whereClause}`
      params = Object.values(where)
    }

    const result = await executeOptimizedQuery(db, sql, params)

    return Object.freeze({
      success: true,
      data: result
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message
    })
  }
}

/**
 * F:executeMemoryTemplate - Execute memory template operation
 * @notation P:templateSource,vars,engine F:executeMemoryTemplate CB:executeMemoryTemplate I:MemoryResult DB:none
 */
export const executeMemoryTemplate = async (
  templateSource: string,
  vars: Record<string, unknown> = {},
  engine: 'simple' | 'mustache' = 'simple'
): Promise<MemoryResult> => {
  try {
    const result = await processMultiHandlerTemplate(templateSource, vars, engine)

    return Object.freeze({
      success: true,
      data: result.data,
      aiNotationMetadata: result.aiNotationMetadata
    })
  } catch (error) {
    return Object.freeze({
      success: false,
      error: (error as Error).message
    })
  }
}

/**
 * F:executeMemoryCommand - Execute memory command with resilience
 * @notation P:db,command F:executeMemoryCommand CB:executeMemoryCommand I:OperationResult DB:memory
 */
export const executeMemoryCommand = async (
  db: Database,
  command: MemoryCommand
): Promise<OperationResult<MemoryResult>> => {
  const config = createMemoryConfig()

  return await executeWithResilience(command, config, async cmd => {
    const { action, table, data, filters, where, limit, content, path, options } = cmd

    switch (action) {
      case 'insert':
        if (!data) throw new Error('Data is required for insert operation')
        return await executeMemoryInsert(db, table, data)

      case 'query':
        return await executeMemoryQuery(db, table, filters, limit)

      case 'update':
        if (!data) throw new Error('Data is required for update operation')
        return await executeMemoryUpdate(db, table, data, filters)

      case 'delete':
        return await executeMemoryDelete(db, table, where || filters)

      case 'template':
        const templateSource = content || path
        if (!templateSource)
          throw new Error('Either path or content must be provided for template action')
        return await executeMemoryTemplate(
          templateSource,
          options?.templateVars || {},
          options?.templateEngine || 'simple'
        )

      default:
        throw new Error(`Unknown memory action: ${action}`)
    }
  })
}

/**
 * F:memoryHandler - Create memory handler function
 * @notation P:db F:memoryHandler CB:none I:function DB:memory
 */
export const memoryHandler = (db: Database) => {
  return {
    execute: async (input: unknown): Promise<MemoryResult> => {
      if (!validateMemoryCommand(input)) {
        return Object.freeze({
          success: false,
          error: 'Invalid memory command structure'
        })
      }

      const result = await executeMemoryCommand(db, input)
      return result.success
        ? result.data
        : Object.freeze({
            success: false,
            error: result.error
          })
    }
  }
}
